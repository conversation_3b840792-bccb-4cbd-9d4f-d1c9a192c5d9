# BlueStacks Configuration
BLUESTACKS_PATH=/Applications/BlueStacks.app/Contents/MacOS/BlueStacks
BLUESTACKS_DATA_PATH=~/Library/BlueStacks App Player

# Appium Server Configuration
APPIUM_HOST=127.0.0.1
APPIUM_PORT=4723
APPIUM_LOG_LEVEL=error

# Database Configuration
DATABASE_URL=sqlite:///automation.db
# DATABASE_URL=postgresql://user:password@localhost/automation_db

# Dashboard Configuration
DASHBOARD_HOST=0.0.0.0
DASHBOARD_PORT=8000
SECRET_KEY=your-secret-key-here

# Anti-Detection Settings
MIN_ACTION_DELAY=1.5
MAX_ACTION_DELAY=4.0
ENABLE_PROXY_ROTATION=true
ENABLE_DEVICE_ROTATION=true
ENABLE_LOCATION_SPOOFING=true

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/automation.log