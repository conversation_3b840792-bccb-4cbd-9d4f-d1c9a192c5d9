# Android 14.0 Default Configuration

## Overview
All device creation components have been configured to use Android 14.0 by default, since it's the only Android image currently downloaded in Genymotion.

## 📱 Components Updated

### 1. **GenymotionManager**
**File**: `src/genymotion_manager.py`
```python
def create_new_instance(self, instance_name: str, android_version: str = "14") -> bool:
    """Create a new Genymotion instance programmatically (defaults to Android 14.0 - only downloaded image)"""
```
- **Change**: Default parameter changed from `"11"` to `"14"`
- **Impact**: All manual device creation uses Android 14.0 by default

### 2. **Gmail Account Creator**
**File**: `src/gmail_account_creator.py`
```python
# Create device with Android 14.0 (only downloaded image)
logger.info("📱 Creating device with Android 14.0...")
success = self.genymotion.create_new_instance(device_name, android_version="14")
```
- **Change**: Explicitly specifies Android 14.0 for Gmail automation
- **Impact**: Gmail account creation always uses Android 14.0

### 3. **API Server Endpoints**
**File**: `src/api_server.py`

#### Device Creation Endpoint (`/api/instances`)
```python
android_version = request.get("android_version", "14")  # Only Android 14.0 image available
```

#### Instance Creation Endpoint (`/api/instances/create`)
```python
android_version = request.get('android_version', '14')  # Only Android 14.0 image available
```
- **Change**: Both endpoints default to Android 14.0
- **Impact**: API requests without android_version use Android 14.0

### 4. **Dashboard Interface**
**File**: `dashboard/index.html`
```html
<div class="form-group">
    <label for="androidVersion">Android Version:</label>
    <select id="androidVersion" name="androidVersion" required>
        <option value="14" selected>Android 14.0 (Available)</option>
    </select>
    <small class="form-text">Only Android 14.0 image is currently downloaded</small>
</div>
```
- **Change**: Removed all other Android version options
- **Impact**: Users can only select Android 14.0 in the dashboard

## 🎯 Benefits

### **Storage Efficiency**
- No need to download additional Android images (saves ~2-4GB per image)
- Faster device creation (image already available)
- Reduced disk space requirements

### **Consistency**
- All components use the same Android version
- No confusion about which version to use
- Simplified configuration management

### **Performance**
- Faster device startup (no image download required)
- Consistent behavior across all created devices
- Optimized for available resources

### **User Experience**
- Simplified device creation process
- Clear indication of available Android version
- No failed device creation due to missing images

## 🚀 Usage Examples

### **Gmail Account Creation**
```python
from gmail_account_creator import GmailAccountCreator

creator = GmailAccountCreator()
result = creator.run_complete_automation()
# Automatically creates device with Android 14.0
```

### **Manual Device Creation**
```python
from genymotion_manager import GenymotionManager

genymotion = GenymotionManager()
success = genymotion.create_new_instance("MyDevice")
# Creates device with Android 14.0 by default
```

### **API Device Creation**
```bash
# Without specifying android_version (uses Android 14.0)
curl -X POST http://localhost:8000/api/instances \
  -H "Content-Type: application/json" \
  -d '{"name": "MyDevice", "device_profile": "Custom Phone"}'

# Gmail account creation via API (uses Android 14.0)
curl -X POST http://localhost:8000/api/gmail/create \
  -H "Content-Type: application/json" \
  -d '{"count": 1, "use_turkish_data": true}'
```

### **Dashboard Usage**
1. Open dashboard at `http://localhost:8000`
2. Click "Create Device"
3. Android 14.0 is automatically selected (only option)
4. Fill in device name and hardware profile
5. Click "Create Device"

## 📊 Configuration Summary

| Component | Default Version | Status |
|-----------|----------------|---------|
| GenymotionManager | Android 14.0 | ✅ Configured |
| Gmail Account Creator | Android 14.0 | ✅ Configured |
| API Endpoints | Android 14.0 | ✅ Configured |
| Dashboard Interface | Android 14.0 (only option) | ✅ Configured |

## 🔧 Technical Details

### **Backward Compatibility**
- API endpoints still accept `android_version` parameter
- Can be overridden if needed for future use
- Graceful fallback to Android 14.0 if invalid version specified

### **Error Handling**
- Clear error messages if Android 14.0 image is missing
- Informative logging about Android version being used
- User-friendly dashboard messages

### **Future Extensibility**
- Easy to add more Android versions when images are downloaded
- Configuration centralized for easy updates
- API structure supports multiple versions

## 🎉 Results

All device creation now consistently uses Android 14.0:
- **Gmail Automation**: ✅ Uses Android 14.0
- **Manual Device Creation**: ✅ Uses Android 14.0
- **API Device Creation**: ✅ Uses Android 14.0
- **Dashboard Device Creation**: ✅ Uses Android 14.0

This ensures optimal performance and eliminates issues related to missing Android images while maintaining a consistent user experience across all automation scenarios.
