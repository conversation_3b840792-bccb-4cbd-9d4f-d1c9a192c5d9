# Complete HTC One Android 14.0 Automation Scenario Guide

## 🎯 Overview

This guide covers the complete automation scenario that creates an HTC One Android 14.0 virtual device and performs browser automation to visit Google.com. The scenario can be run both from the command line and through the web dashboard.

## 🚀 Quick Start

### Command Line Interface
```bash
# Run with default settings
python3 run_htc_automation.py

# Run automatically without prompts
python3 run_htc_automation.py --auto

# Run with custom instance name and keep running
python3 run_htc_automation.py --instance-name "MyHTC" --keep-running --verbose
```

### Web Dashboard Interface
1. Start the API server: `python3 src/api_server.py`
2. Open browser: `http://localhost:8000`
3. Navigate to "Automation Scenarios" section
4. Click "Run Scenario" on "HTC One + Google.com"
5. Monitor progress in real-time

## 📋 What the Scenario Does

### Complete Workflow
1. **Prerequisites Check** (5%)
   - Verify Genymotion Desktop installation
   - Check ADB availability
   - Validate system resources

2. **Device Creation** (15%)
   - Create HTC One virtual device
   - Configure Android 14.0
   - Set device properties (1080x1920, 441 DPI)

3. **Device Startup** (30%)
   - Start virtual device
   - Wait for Android boot completion
   - Establish ADB connection

4. **System Verification** (60%)
   - Verify Android system is ready
   - Check device properties
   - Confirm ADB connectivity

5. **Browser Launch** (75%)
   - Detect available browsers
   - Launch default Android browser
   - Navigate to https://www.google.com

6. **Search Automation** (90%)
   - Tap on Google search box
   - Type search query: "HTC One Android 14 automation test"
   - Execute search and wait for results

7. **Completion** (100%)
   - Verify successful execution
   - Provide device management instructions

## 🖥️ Command Line Usage

### Basic Commands
```bash
# Show help
python3 run_htc_automation.py --help

# Run with confirmation prompt
python3 run_htc_automation.py

# Run automatically
python3 run_htc_automation.py --auto

# Custom instance name
python3 run_htc_automation.py --instance-name "TestDevice"

# Keep device running after completion
python3 run_htc_automation.py --keep-running

# Verbose logging
python3 run_htc_automation.py --verbose
```

### Expected Output
```
🤖 HTC ONE ANDROID 14.0 AUTOMATION SCENARIO
======================================================================
⚠️ This will create a REAL virtual device!
⚠️ Ensure you have sufficient system resources
======================================================================

🤔 Do you want to proceed with the automation? (y/N): y

🚀 Starting HTC One Android 14.0 + Google.com Automation
======================================================================
📱 Instance Name: HTC_One_Auto_1642781234
🤖 Android Version: 14.0
📱 Device Model: HTC One
🌐 Target: Google.com
======================================================================

[██████████████████████████████] 100% | completion: Scenario completed successfully

🎉 AUTOMATION SCENARIO COMPLETED SUCCESSFULLY!
✅ Device ID: **************:5555
✅ Instance Name: HTC_One_Auto_1642781234
✅ Message: HTC One automation scenario completed successfully
```

## 🌐 Web Dashboard Usage

### Starting the Dashboard
```bash
# Start the API server
python3 src/api_server.py

# Open browser to http://localhost:8000
```

### Dashboard Features
1. **Real-time Progress Tracking**
   - Live progress bar with percentage
   - Step-by-step status updates
   - Real-time messaging via WebSocket

2. **Scenario Management**
   - View available scenarios
   - Start scenarios with one click
   - Stop running scenarios
   - Cleanup resources

3. **Device Integration**
   - View created devices in instances section
   - Start/stop devices directly
   - Monitor device status

### Dashboard Interface
```
┌─────────────────────────────────────────────────────────────┐
│ Automation Scenarios                                        │
├─────────────────────────────────────────────────────────────┤
│ 🤖 HTC One + Google.com                                    │
│ Create HTC One Android 14.0 device and perform Google     │
│ search automation                                          │
│                                                            │
│ Duration: 2-4 minutes                                      │
│ Requirements: 4GB+ RAM, Genymotion Desktop, ADB           │
│                                                            │
│ [▶ Run Scenario]                                           │
└─────────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────────┐
│ 🔄 Running Scenario                                         │
├─────────────────────────────────────────────────────────────┤
│ Device Creation                                            │
│ [████████████████████████████████] 75%                    │
│ Starting virtual device                                    │
│                                                            │
│ [⏹ Stop Scenario]                                         │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 API Endpoints

### Scenario Management
```bash
# Get available scenarios
GET /api/scenarios/available

# Run HTC One scenario
POST /api/scenarios/htc-one-google
{
  "instance_name": "optional_custom_name"
}

# Stop scenario
POST /api/scenarios/htc-one-google/stop
{
  "instance_name": "device_name"
}

# Cleanup scenario resources
DELETE /api/scenarios/htc-one-google/cleanup
{
  "instance_name": "device_name"
}
```

### WebSocket Events
```javascript
// Progress updates
{
  "type": "scenario_progress",
  "scenario": "htc_one_google",
  "data": {
    "step": "device_creation",
    "progress": 45,
    "message": "Starting virtual device"
  }
}

// Completion
{
  "type": "scenario_complete",
  "scenario": "htc_one_google",
  "result": {
    "success": true,
    "device_id": "**************:5555",
    "instance_name": "HTC_One_Auto_1642781234"
  }
}
```

## 📱 Device Management

### After Scenario Completion
```bash
# Check device status
gmtool admin list

# View device via ADB
adb devices

# Take screenshot
adb exec-out screencap -p > screenshot.png

# Interact with device
adb shell input tap 540 350
adb shell input text "test query"
```

### Device Cleanup
```bash
# Stop device
gmtool admin stop "HTC_One_Auto_1642781234"

# Delete device
gmtool admin delete "HTC_One_Auto_1642781234"

# Or via API
curl -X DELETE http://localhost:8000/api/scenarios/htc-one-google/cleanup \
  -H "Content-Type: application/json" \
  -d '{"instance_name": "HTC_One_Auto_1642781234"}'
```

## 🛠️ Troubleshooting

### Common Issues

**1. Genymotion Not Found**
```
Error: Genymotion Desktop not accessible
```
**Solution**: Install Genymotion Desktop and ensure it's running

**2. Device Creation Timeout**
```
Error: Device creation failed
```
**Solution**: Check system resources, try manual creation via Genymotion Desktop

**3. Browser Launch Failed**
```
Error: Could not launch any browser
```
**Solution**: Wait for Android to fully boot, check browser packages

**4. ADB Connection Issues**
```
Error: Device boot timeout
```
**Solution**: Restart ADB server, check device status

### Performance Optimization

**System Requirements:**
- RAM: 8GB+ recommended (4GB minimum)
- CPU: 4+ cores with virtualization support
- Disk: 20GB+ free space
- Network: Stable internet connection

**Genymotion Settings:**
- Enable hardware acceleration
- Allocate 2-4GB RAM to virtual device
- Use NAT network mode

## 📊 Success Metrics

### Expected Results
- ✅ Device creation: 30-60 seconds
- ✅ Android boot: 45-90 seconds  
- ✅ Browser launch: 5-15 seconds
- ✅ Google navigation: 3-8 seconds
- ✅ Search automation: 5-10 seconds
- ✅ Total time: 2-4 minutes

### Verification Steps
1. Device appears in Genymotion Desktop
2. Device shows in `adb devices` as online
3. Browser opens with Google.com loaded
4. Search query executes successfully
5. Search results are displayed

## 🔄 Integration Options

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Run HTC Automation
  run: python3 run_htc_automation.py --auto --instance-name "CI_Test_${{ github.run_id }}"
```

### Programmatic Usage
```python
from src.automation_scenarios import HTCOneGoogleScenario

async def run_automation():
    scenario = HTCOneGoogleScenario(instance_name="MyTest")
    result = await scenario.run_complete_scenario()
    return result["success"]
```

## 📞 Support

For issues or questions:
1. Check system prerequisites with `python3 verify_htc_scenario.py`
2. Review logs with `--verbose` flag
3. Try manual device creation via Genymotion Desktop
4. Check the troubleshooting section above

---

**Status**: ✅ Production Ready  
**Interfaces**: Command Line + Web Dashboard  
**Last Updated**: 2025-07-21
