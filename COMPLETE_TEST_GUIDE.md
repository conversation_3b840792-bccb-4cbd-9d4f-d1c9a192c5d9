# Complete HTC One Android 14.0 Test Scenario Guide

## 🎯 Overview

This guide provides instructions for running the **complete end-to-end test scenario** that creates a real HTC One Android 14.0 virtual device and performs browser automation to visit Google.com.

## ⚠️ Important Notice

**This test creates REAL virtual devices!** 
- Ensure you have sufficient system resources (4GB+ RAM, 10GB+ disk space)
- The test will create a Genymotion virtual device that will consume system resources
- Virtual devices will remain running after the test (unless explicitly stopped)

## 📋 Prerequisites Verification

Before running the complete test, verify your system is ready:

```bash
python3 verify_htc_scenario.py
```

**Expected Output:**
```
🎉 All verifications passed!
✅ System is ready for HTC One scenario
📊 VERIFICATION RESULTS: 7/7 checks passed
```

### What Gets Verified:
- ✅ Genymotion Desktop installation and accessibility
- ✅ ADB (Android Debug Bridge) installation
- ✅ HTC One device template availability
- ✅ Android 14.0 image availability
- ✅ System resources (memory, CPU, disk)
- ✅ Appium capabilities generation
- ✅ Browser automation commands

## 🚀 Running the Complete Test

### Option 1: Interactive Mode (Recommended)
```bash
python3 test_complete_htc_scenario.py
```

The test will ask for confirmation before proceeding:
```
🤔 Do you want to proceed with the complete test? (y/N):
```

### Option 2: Automated Mode
```bash
python3 test_complete_htc_scenario.py --auto
```

This runs the test without user confirmation (useful for CI/CD).

## 📊 Complete Test Workflow

The test performs these steps in sequence:

### 1. Prerequisites Check (10-15 seconds)
- ✅ Verify Genymotion Desktop installation
- ✅ Verify ADB installation and functionality
- ✅ Check system resources (memory, CPU, disk)

### 2. Device Creation (30-60 seconds)
- 📱 Create HTC One virtual device named "HTC_One_Test_Device"
- ⚙️ Configure device properties:
  - Screen: 1080x1920 pixels
  - DPI: 441
  - Manufacturer: HTC
  - Model: HTC One
  - Android: 14.0

### 3. Device Startup (45-90 seconds)
- ▶️ Start the virtual device
- ⏳ Wait for Android boot completion
- 🔗 Establish ADB connection

### 4. System Verification (10-15 seconds)
- 🤖 Verify Android system is fully booted
- 📱 Check Android version (should be 14.x)
- 📱 Verify device model (should be HTC One)

### 5. Browser Automation (15-30 seconds)
- 🌐 Launch default Android browser
- 🔍 Navigate to https://www.google.com
- ⏳ Wait for page load completion

### 6. Search Interaction (10-15 seconds)
- 👆 Tap on Google search box (coordinates: 540, 350)
- ⌨️ Type search query: "HTC One Android 14 automation test"
- ⏎ Press Enter to execute search
- 📸 Take screenshots for verification

### 7. Cleanup (5-10 seconds)
- 🧹 Stop Appium server (if started)
- 💡 Leave device running for further testing
- 📋 Provide cleanup instructions

## 📱 Expected Results

### Successful Test Output:
```
🎉 COMPLETE TEST PASSED!
✅ HTC One Android 14.0 device created and tested successfully
✅ Browser automation working correctly
✅ Google.com navigation and search completed
```

### Test Artifacts:
- **Virtual Device**: `HTC_One_Test_Device` (running in Genymotion)
- **Screenshots**: Saved to `/tmp/htc_test_*.png`
- **ADB Connection**: Device accessible via ADB
- **Browser State**: Google search results displayed

## 🛠 Manual Verification Steps

After the test completes, you can manually verify:

### 1. Check Device Status
```bash
# List running Genymotion instances
gmtool admin list

# Check ADB devices
adb devices
```

### 2. Interact with Device
```bash
# Get device properties
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release

# Take a screenshot
adb exec-out screencap -p > screenshot.png
```

### 3. Test Browser Manually
```bash
# Open browser with custom URL
adb shell am start -a android.intent.action.VIEW -d "https://example.com"

# Simulate touch input
adb shell input tap 540 350
adb shell input text "test query"
adb shell input keyevent KEYCODE_ENTER
```

## 🧹 Cleanup Instructions

### Stop the Test Device
```bash
# Stop the virtual device
gmtool admin stop "HTC_One_Test_Device"

# Or delete it completely
gmtool admin delete "HTC_One_Test_Device"
```

### Kill ADB Server (if needed)
```bash
adb kill-server
adb start-server
```

## 🔧 Troubleshooting

### Common Issues and Solutions

**1. Device Creation Timeout**
```
Error: Gmtool command timed out
```
**Solution**: 
- Ensure sufficient system resources
- Close other applications
- Try creating device manually via Genymotion Desktop

**2. Android Boot Timeout**
```
Error: Device did not become ready within timeout
```
**Solution**:
- Increase wait timeout in the script
- Check system performance
- Restart Genymotion Desktop

**3. Browser Launch Failure**
```
Error: Could not launch any browser
```
**Solution**:
- Verify Android system is fully booted
- Check if browser packages are installed
- Try manual browser launch via ADB

**4. ADB Connection Issues**
```
Error: ADB connection test failed
```
**Solution**:
```bash
adb kill-server
adb start-server
adb devices
```

### Performance Optimization

**System Requirements:**
- **RAM**: 8GB+ recommended (4GB minimum)
- **CPU**: 4+ cores with VT-x/AMD-V support
- **Disk**: 20GB+ free space
- **Network**: Stable internet connection

**Genymotion Settings:**
- Enable hardware acceleration
- Allocate 2-4GB RAM to virtual device
- Use NAT network mode
- Enable GPU acceleration if available

## 📊 Test Metrics

### Typical Execution Times:
- **Total Test Duration**: 2-4 minutes
- **Device Creation**: 30-60 seconds
- **Android Boot**: 45-90 seconds
- **Browser Automation**: 15-30 seconds

### Success Criteria:
- ✅ Device created successfully
- ✅ Android 14.0 boots completely
- ✅ ADB connection established
- ✅ Browser launches and loads Google.com
- ✅ Search interaction completes
- ✅ Screenshots captured

## 🔄 Running Multiple Tests

### Sequential Tests:
```bash
# Run verification first
python3 verify_htc_scenario.py

# Run complete test
python3 test_complete_htc_scenario.py --auto

# Run scenario with different settings
python3 run_genymotion_scenario.py --device-name "HTC_Test_2" --search-query "second test"
```

### Parallel Testing:
- Each test should use unique device names
- Monitor system resources carefully
- Limit concurrent virtual devices based on available RAM

## 📞 Support

If you encounter issues:

1. **Check Prerequisites**: Run `python3 verify_htc_scenario.py`
2. **Review Logs**: Enable verbose logging for detailed output
3. **Manual Verification**: Try creating device manually via Genymotion Desktop
4. **System Resources**: Ensure adequate RAM, CPU, and disk space
5. **Restart Services**: Restart Genymotion Desktop and ADB server

---

**Status**: ✅ Ready for Production Testing  
**Last Updated**: 2025-07-21  
**Test Coverage**: Complete End-to-End Workflow
