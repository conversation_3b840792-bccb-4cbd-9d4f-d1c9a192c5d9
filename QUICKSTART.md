# Quick Start Guide

## ✅ Installation Complete!

Your BlueStacks automation system is now set up. Here's how to get started:

## Prerequisites Check

Run this to verify what you still need:
```bash
./setup.sh
```

## Required Dependencies

1. **BlueStacks**: Download from https://www.bluestacks.com/
2. **Node.js & Appium**: 
   ```bash
   # Install Node.js first, then:
   npm install -g appium
   npm install -g appium-doctor
   ```
3. **Android SDK Platform Tools** (for ADB):
   ```bash
   brew install android-platform-tools
   ```

## Configuration

1. **Edit your environment settings:**
   ```bash
   nano .env
   ```
   
2. **Key settings to configure:**
   - `BLUESTACKS_PATH`: Path to your BlueStacks installation
   - `APPIUM_HOST` and `APPIUM_PORT`: Appium server settings
   - `DATABASE_URL`: Database connection (default SQLite is fine)

## Running the System

### 1. Start Web Dashboard (Recommended)
```bash
./run.sh --mode dashboard
```
Then open: http://localhost:8000

### 2. Run Automation Example
```bash
./run.sh --mode example --app-package com.your.app
```

### 3. Test System Components
```bash
./run.sh --mode test
```

## Web Dashboard Features

Once the dashboard is running, you can:

- **Start/Stop Sessions**: Control automation sessions
- **Device Rotation**: Switch device profiles for stealth
- **Location Control**: Set GPS coordinates or city locations
- **Task Execution**: Run tap, swipe, scroll, and typing actions
- **Real-time Monitoring**: View logs and session statistics
- **Session History**: Track all automation activities

## Basic Usage Flow

1. **Start BlueStacks** and ensure Android debugging is enabled
2. **Start Appium server** (or let the system start it automatically)
3. **Launch Dashboard**: `./run.sh --mode dashboard`
4. **Create Session**: Click "Start Session" in the dashboard
5. **Set Location**: Use location controls to set GPS coordinates
6. **Execute Tasks**: Use quick actions or API calls
7. **Monitor**: Watch real-time logs and session statistics

## Anti-Detection Features

The system automatically:
- ✅ Rotates device IDs (IMEI, Android ID, etc.)
- ✅ Changes device fingerprints
- ✅ Uses human-like touch patterns
- ✅ Implements realistic timing delays
- ✅ Spoofs GPS location
- ✅ Rotates sessions for maximum stealth

## Troubleshooting

### Common Issues:

1. **"ADB not found"**
   ```bash
   brew install android-platform-tools
   ```

2. **"Appium server won't start"**
   ```bash
   npm install -g appium
   appium-doctor --android
   ```

3. **"BlueStacks connection failed"**
   - Enable Android debugging in BlueStacks settings
   - Check that BlueStacks is running
   - Verify device ID with `adb devices`

4. **"Dashboard won't load"**
   - Check if port 8000 is available
   - Run with debug: `./run.sh --mode dashboard --log-level DEBUG`

### Debug Mode:
```bash
./run.sh --mode dashboard --log-level DEBUG
```

## Next Steps

1. **Customize Device Profiles**: Edit `config/device_profiles.yaml`
2. **Create Custom Tasks**: Use the API to build your automation scripts
3. **Monitor Sessions**: Use the dashboard to track performance
4. **Scale Up**: Run multiple sessions with different profiles

## API Usage

Once running, the API is available at `http://localhost:8000/api/`

Example API calls:
```bash
# Start session
curl -X POST http://localhost:8000/api/sessions/start \
  -H "Content-Type: application/json" \
  -d '{"app_package": "com.example.app"}'

# Execute tap
curl -X POST http://localhost:8000/api/tasks/execute \
  -H "Content-Type: application/json" \
  -d '{"task_type": "tap", "parameters": {"x": 100, "y": 200}}'

# Set location
curl -X POST http://localhost:8000/api/location/set \
  -H "Content-Type: application/json" \
  -d '{"city_name": "new_york"}'
```

## Security Notice

⚠️ **Important**: This tool is designed for legitimate automation testing and research purposes only. Always ensure you have proper authorization before testing any applications.

## Support

If you encounter issues:
1. Check the logs in the `logs/` directory
2. Run system test: `./run.sh --mode test`
3. Review the troubleshooting section above

**You're ready to start automating! 🚀**