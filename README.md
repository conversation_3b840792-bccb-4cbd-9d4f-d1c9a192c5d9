# Genymotion Automation System

An advanced, undetectable mobile automation framework using Genymotion, Appium, and Python with comprehensive anti-detection features.

## Features

### 🛡️ Anti-Detection Capabilities
- **Dynamic Device ID Rotation**: IMEI, Android ID, and advertising ID rotation
- **Location Spoofing**: GPS mocking with realistic movement patterns
- **Human-like Touch Behavior**: Bezier curves, natural timing, and randomized gestures
- **Device Fingerprint Rotation**: Screen resolution, DPI, manufacturer data rotation
- **Session Management**: Automatic session rotation to avoid detection patterns

### 🚀 Core Components
- **Genymotion Manager**: Dynamic device configuration and instance management
- **Appium Server Manager**: Automated server management with rotating capabilities
- **Stealth Automation Client**: Human-like interaction patterns and anti-detection
- **Location Spoofer**: Advanced GPS mocking and movement simulation
- **Web Dashboard**: Real-time monitoring and control interface
- **Database System**: Comprehensive logging and session tracking

### 📊 Web Dashboard
- Real-time session monitoring
- Task execution controls
- Location management
- Live logs and debugging
- WebSocket-based updates

## Installation

### Prerequisites
- Python 3.8+
- Genymotion Desktop installed
- Appium server
- ADB (Android Debug Bridge)

### Setup Instructions

1. **<PERSON>lone and setup the project:**
```bash
git clone <repository-url>
cd genymotion-automation
pip install -r requirements.txt
```

2. **Environment Configuration:**
```bash
cp .env.example .env
# Edit .env with your specific configurations
```

3. **Install Appium (if not installed):**
```bash
npm install -g appium
npm install -g appium-doctor
appium-doctor --android  # Verify setup
```

4. **Configure Genymotion:**
- Install Genymotion Desktop from https://www.genymotion.com/
- Create virtual devices using Genymotion Desktop
- Enable ADB connection in Genymotion settings
- Ensure developer options are enabled in the Android instances

## Usage

### Dashboard Mode (Recommended)
Start the web dashboard for interactive control:

```bash
python main.py --mode dashboard --host 0.0.0.0 --port 8000
```

Access the dashboard at: `http://localhost:8000`

### Command Line Automation
Run automation examples:

```bash
# Basic automation example
python main.py --mode example

# With specific app package
python main.py --mode example --app-package com.example.app
```

### System Testing
Test all components:

```bash
python main.py --mode test
```

## Configuration

### Device Profiles (`config/device_profiles.yaml`)
Define multiple device configurations:

```yaml
device_profiles:
  samsung_galaxy_s21:
    platformName: Android
    platformVersion: "12"
    deviceName: Samsung Galaxy S21
    # ... more configuration
```

### Environment Variables (`.env`)
Key configuration options:

```env
# Genymotion Configuration
GENYMOTION_PATH=/Applications/Genymotion.app/Contents/MacOS
GENYMOTION_SHELL_PATH=/Applications/Genymotion Shell.app/Contents/MacOS/genyshell

# Appium Server
APPIUM_HOST=127.0.0.1
APPIUM_PORT=4723

# Anti-Detection Settings
MIN_ACTION_DELAY=1.5
MAX_ACTION_DELAY=4.0
ENABLE_DEVICE_ROTATION=true
ENABLE_LOCATION_SPOOFING=true

# Database
DATABASE_URL=sqlite:///automation.db
```

## Anti-Detection Features

### Device Identity Rotation
The system automatically rotates:
- IMEI numbers
- Android ID
- Advertising ID
- Device model and manufacturer
- Screen resolution and DPI

### Human Behavior Simulation
- **Natural Touch Patterns**: Bezier curve-based touch movements
- **Realistic Timing**: Variable delays between actions
- **Human-like Scrolling**: Multi-touch gestures and natural scrolling patterns
- **Typing Simulation**: Character-by-character typing with realistic speeds

### Location Anti-Detection
- **GPS Spoofing**: Set custom or random locations
- **Movement Simulation**: Realistic movement patterns between locations
- **Location History**: Track and manage location changes
- **City-based Location**: Quick location setting to major cities

### Session Management
- **Automatic Rotation**: Sessions rotate based on time or action count
- **Clean State**: Each session starts with a fresh device state
- **Session Isolation**: Complete separation between automation sessions

## API Documentation

### Session Management
```bash
# Start new session
POST /api/sessions/start
{
  "app_package": "com.example.app",
  "instance_name": "Pixel"
}

# Rotate session
POST /api/sessions/rotate

# Stop session
POST /api/sessions/stop
```

### Task Execution
```bash
# Execute tap
POST /api/tasks/execute
{
  "task_type": "tap",
  "parameters": {"x": 100, "y": 200}
}

# Execute scroll
POST /api/tasks/execute
{
  "task_type": "scroll",
  "parameters": {"direction": "down"}
}
```

### Location Control
```bash
# Set custom location
POST /api/location/set
{
  "latitude": 40.7128,
  "longitude": -74.0060
}

# Set city location
POST /api/location/set
{
  "city_name": "new_york"
}
```

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Dashboard │    │   API Server     │    │   Database      │
│   (Frontend)    │◄──►│   (FastAPI)      │◄──►│   (SQLite/PG)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│ Device Manager  │◄──►│ Automation       │◄──►│ Location        │
│ (Profiles)      │    │ Client           │    │ Spoofer         │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │
         ▼                       ▼
┌─────────────────┐    ┌──────────────────┐
│ BlueStacks      │    │ Appium Server    │
│ Manager         │    │ Manager          │
└─────────────────┘    └──────────────────┘
```

## Security Considerations

⚠️ **Important**: This tool is designed for legitimate automation testing and research purposes only.

### Defensive Use Cases
- Mobile app testing and QA
- Automation research and development
- Security testing of your own applications
- Educational purposes

### Responsible Usage
- Only use on applications you own or have explicit permission to test
- Respect terms of service and applicable laws
- Do not use for spam, fraud, or malicious activities
- Implement appropriate rate limiting and respectful automation patterns

## Troubleshooting

### Common Issues

1. **Appium Server Won't Start**
   - Check Node.js and Appium installation
   - Verify ADB is in PATH
   - Run `appium-doctor --android`

2. **BlueStacks Connection Issues**
   - Enable ADB in BlueStacks settings
   - Check BlueStacks is running
   - Verify device ID with `adb devices`

3. **Location Spoofing Not Working**
   - Enable mock location in Android developer options
   - Check ADB permissions
   - Verify GPS is enabled in the Android instance

4. **Dashboard Not Loading**
   - Check if port 8000 is available
   - Verify all dependencies are installed
   - Check the logs for API errors

### Debug Mode
Enable detailed logging:

```bash
python main.py --mode dashboard --log-level DEBUG
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is intended for educational and legitimate testing purposes only. Users are responsible for ensuring compliance with applicable laws and terms of service.

## Disclaimer

This software is provided for educational and research purposes only. The authors are not responsible for any misuse or damage caused by this software. Users must ensure they have proper authorization before testing any applications or systems.