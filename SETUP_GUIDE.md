# Complete Setup Guide

## Current Status ✅

Your Genymotion automation system is now **ready to use** with the following fixes:
- ✅ Database cleared of test data
- ✅ Genymotion commands updated for modern versions
- ✅ Error handling improved for missing dependencies
- ✅ System will work even without ADB initially

## Quick Start (Works Now!)

You can start using the system immediately:

```bash
./run.sh --mode dashboard
```

Visit: http://localhost:8000

The system will work in **simulation mode** until you install the optional dependencies below.

## For Full Functionality

### 1. Install ADB (Android Debug Bridge)

**Required for location spoofing and device property changes**

```bash
# Install Android Platform Tools
brew install android-platform-tools

# Verify installation
adb --version
```

### 2. Configure Genymotion

**Install and Setup Genymotion:**
1. Download Genymotion Desktop from https://www.genymotion.com/
2. Install and create an account
3. Create virtual devices using the Genymotion Desktop interface
4. Start a virtual device to test

**Enable Developer Options in Android:**
1. Open Android Settings in the Genymotion virtual device
2. Go to About Phone
3. Tap "Build Number" 7 times
4. Go back to Settings → Developer Options
5. Enable "USB Debugging"
6. Enable "Allow mock locations"

### 3. Install Appium (Optional)

**For advanced automation features:**

```bash
# Install Node.js first if not installed
brew install node

# Install Appium
npm install -g appium
npm install -g appium-doctor

# Verify setup
appium-doctor --android
```

### 4. Test Your Setup

```bash
# Test ADB connection
adb devices

# Should show something like:
# 127.0.0.1:5555    device

# Test automation system
./run.sh --mode test
```

## What Works Without Full Setup

Even without ADB and Appium installed, you can:

✅ **Start the dashboard** - Full web interface available
✅ **Create sessions** - Device profiles and session management
✅ **Set locations** - GPS coordinates (simulated until ADB connected)
✅ **View logs** - Real-time monitoring and debugging
✅ **Test system** - All components work in simulation mode

## What Requires Full Setup

For **actual device automation** you need:
- 🔧 **ADB** - For real GPS spoofing and device property changes
- 🔧 **Appium** - For actual app automation and touch actions
- 🔧 **BlueStacks ADB** - For connecting to the Android instance

## Current Dashboard Features

Visit http://localhost:8000 to access:

1. **Session Control**
   - Start/stop automation sessions
   - Device profile rotation
   - Session monitoring

2. **Location Management**
   - Set GPS coordinates
   - City-based location setting
   - Location history tracking

3. **Task Execution**
   - Tap actions (when Appium connected)
   - Scroll and swipe gestures
   - Text input simulation
   - Random human activity

4. **Monitoring**
   - Real-time logs
   - Session statistics
   - Device fingerprint tracking
   - Anti-detection metrics

## Troubleshooting

### "No BlueStacks devices found via ADB"
- Ensure BlueStacks is running
- Enable ADB in BlueStacks settings
- Try: `adb connect 127.0.0.1:5555`

### "Appium server won't start"
- Install Node.js first: `brew install node`
- Install Appium: `npm install -g appium`
- Check requirements: `appium-doctor --android`

### "Session creation fails"
- The system will work in simulation mode
- Check logs for specific errors
- Ensure all services are running

### Dashboard shows errors
- All features work without external dependencies
- Errors are mostly warnings about missing ADB/Appium
- Core functionality remains available

## Development Mode

For development and testing:

```bash
# Run with debug logging
./run.sh --mode dashboard --log-level DEBUG

# Test individual components
./run.sh --mode test

# Run automation example
./run.sh --mode example
```

## Security Notes

⚠️ **Important Reminders:**
- Only use on apps you own or have permission to test
- Respect rate limits and terms of service
- This tool is for legitimate testing and research only
- Monitor your automation to ensure it behaves appropriately

## Getting Help

1. **Check logs**: `tail -f logs/automation.log`
2. **Run tests**: `./run.sh --mode test`
3. **Debug mode**: `./run.sh --mode dashboard --log-level DEBUG`

---

**Your system is ready! Start with `./run.sh --mode dashboard` and gradually add the optional components for full functionality. 🚀**