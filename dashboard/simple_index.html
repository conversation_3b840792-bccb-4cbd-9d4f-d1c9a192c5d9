<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genymotion Automation Dashboard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .header h1 {
            font-size: 2.2rem;
            margin-bottom: 10px;
            color: #2d3748;
        }

        .header p {
            font-size: 1rem;
            color: #718096;
        }

        .dashboard {
            display: grid;
            grid-template-columns: 1fr 1fr;
            grid-template-rows: auto auto auto;
            gap: 20px;
            height: calc(100vh - 150px);
        }

        .widget {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .widget h2 {
            color: #2d3748;
            margin-bottom: 20px;
            font-size: 1.3rem;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
            flex-shrink: 0;
        }

        .widget-content {
            flex: 1;
            overflow-y: auto;
        }

        /* Scenarios Widget */
        .scenarios-widget {
            grid-column: 1;
            grid-row: 1;
        }

        .scenario-card {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .scenario-card:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .scenario-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .scenario-name {
            font-weight: bold;
            color: #2d3748;
            font-size: 1.1rem;
        }

        .scenario-description {
            color: #718096;
            font-size: 0.9rem;
            margin-bottom: 15px;
        }

        .device-selector {
            margin-bottom: 15px;
        }

        .device-selector label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #4a5568;
        }

        .device-selector select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #2d3748;
        }

        .progress-container {
            margin-top: 10px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e2e8f0;
            border-radius: 3px;
            overflow: hidden;
            margin: 8px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4299e1;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.8rem;
            color: #718096;
        }

        /* Devices Widget */
        .devices-widget {
            grid-column: 2;
            grid-row: 1;
        }

        .widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .widget-header h2 {
            margin: 0;
        }

        .device-count {
            background: #e2e8f0;
            color: #4a5568;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            min-width: 80px;
            text-align: center;
        }

        .device-count.has-devices {
            background: #c6f6d5;
            color: #2f855a;
        }

        .device-actions {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-shrink: 0;
        }

        .progress-container {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            animation: pulse 2s infinite;
        }

        .progress-message {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #1976d2;
            font-weight: 500;
        }

        .spinner {
            width: 20px;
            height: 20px;
            border: 2px solid #e3f2fd;
            border-top: 2px solid #2196f3;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.8; }
            100% { opacity: 1; }
        }

        .progress-container.success {
            background: #e8f5e8;
            border-color: #4caf50;
        }

        .progress-container.success .progress-message {
            color: #2e7d32;
        }

        .progress-container.error {
            background: #ffebee;
            border-color: #f44336;
        }

        .progress-container.error .progress-message {
            color: #c62828;
        }

        .device-item {
            background: #f7fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .device-item:hover {
            background: #edf2f7;
            border-color: #cbd5e0;
        }

        .device-item.running {
            border-left: 4px solid #48bb78;
        }

        .device-item.stopped {
            border-left: 4px solid #f56565;
        }

        .device-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .device-name {
            font-weight: bold;
            color: #2d3748;
        }

        .device-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .device-status.running {
            background: #c6f6d5;
            color: #2f855a;
        }

        .device-status.stopped {
            background: #fed7d7;
            color: #c53030;
        }

        .device-status.stopping {
            background: #fbd38d;
            color: #c05621;
            animation: pulse 1.5s infinite;
        }

        .device-status.starting {
            background: #bee3f8;
            color: #2c5282;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .device-details {
            font-size: 0.9rem;
            color: #718096;
            margin-bottom: 10px;
        }

        .device-controls {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .proxy-input {
            width: 100%;
            padding: 6px 10px;
            border: 1px solid #e2e8f0;
            border-radius: 4px;
            font-size: 0.8rem;
            margin-top: 8px;
            placeholder: "Proxy: host:port";
        }

        /* Logs Widget */
        .logs-widget {
            grid-column: 1 / -1;
            grid-row: 3;
        }

        .logs-container {
            background: #1a202c;
            color: #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            height: 100%;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 0.85rem;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 4px;
            padding: 2px 0;
        }

        .log-entry.info {
            color: #90cdf4;
        }

        .log-entry.warning {
            color: #fbb6ce;
        }

        .log-entry.error {
            color: #fc8181;
        }

        .log-entry.success {
            color: #68d391;
        }

        /* Gmail Widget */
        .gmail-widget {
            grid-column: 1 / -1;
            grid-row: 2;
        }

        .gmail-stats {
            font-size: 0.9rem;
            color: #666;
            background: #e8f5e8;
            padding: 4px 12px;
            border-radius: 12px;
        }

        .gmail-controls {
            margin-bottom: 20px;
        }

        .gmail-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .gmail-actions .btn {
            flex: 1;
            min-width: 120px;
        }

        .progress-container {
            margin-top: 15px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e0e0e0;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 8px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #66bb6a);
            width: 0%;
            transition: width 0.3s ease;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #666;
            text-align: center;
        }

        .gmail-accounts {
            border-top: 1px solid #e0e0e0;
            padding-top: 20px;
        }

        .gmail-accounts h3 {
            margin-bottom: 15px;
            font-size: 1.1rem;
            color: #333;
        }

        .accounts-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .account-item {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .account-info {
            flex: 1;
        }

        .account-email {
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 4px;
        }

        .account-details {
            font-size: 0.85rem;
            color: #666;
        }

        .account-actions {
            display: flex;
            gap: 5px;
        }

        .account-actions .btn {
            padding: 4px 8px;
            font-size: 0.8rem;
            min-width: auto;
        }

        .loading-message {
            text-align: center;
            color: #666;
            padding: 20px;
            font-style: italic;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 8px;
        }

        /* Buttons */
        .btn {
            background: #4299e1;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .btn:hover {
            background: #3182ce;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #a0aec0;
            cursor: not-allowed;
            transform: none;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.8rem;
        }

        .btn-danger {
            background: #f56565;
        }

        .btn-danger:hover {
            background: #e53e3e;
        }

        .btn-success {
            background: #48bb78;
        }

        .btn-success:hover {
            background: #38a169;
        }

        .btn-warning {
            background: #ed8936;
        }

        .btn-warning:hover {
            background: #dd6b20;
        }

        /* Responsive */
        @media (max-width: 1200px) {
            .dashboard {
                grid-template-columns: 1fr;
                grid-template-rows: auto auto auto auto;
            }

            .scenarios-widget {
                grid-column: 1;
                grid-row: 1;
            }

            .devices-widget {
                grid-column: 1;
                grid-row: 2;
            }

            .logs-widget {
                grid-column: 1;
                grid-row: 4;
            }

            .gmail-widget {
                grid-column: 1;
                grid-row: 3;
            }
        }

        .loading {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4299e1;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 10% auto;
            padding: 20px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }

        .modal-header h3 {
            margin: 0;
            color: #2d3748;
        }

        .close {
            color: #718096;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            color: #2d3748;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #4a5568;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            background: white;
            color: #2d3748;
            font-size: 0.9rem;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4299e1;
            box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
            margin-top: 20px;
        }

        .btn-secondary {
            background: #718096;
        }

        .btn-secondary:hover {
            background: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Genymotion Automation Dashboard</h1>
            <p>Simplified interface for device management and automation scenarios</p>
        </div>

        <div class="dashboard">
            <!-- Scenarios Widget -->
            <div class="widget scenarios-widget">
                <h2>📱 Automation Scenarios</h2>
                <div class="widget-content" id="scenarios-content">
                    <div class="scenario-card">
                        <div class="scenario-header">
                            <div class="scenario-name">🤖 HTC One + Google.com</div>
                        </div>
                        <div class="scenario-description">
                            Create HTC One Android 14.0 device and perform Google search automation
                        </div>
                        <div class="device-selector">
                            <label for="device-select">Select Device (optional):</label>
                            <select id="device-select">
                                <option value="">Create new device</option>
                            </select>
                        </div>
                        <button class="btn btn-success" id="run-scenario-btn" onclick="runScenario()">
                            ▶️ Run Scenario
                        </button>
                        <div class="progress-container" id="progress-container" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                            </div>
                            <div class="progress-text" id="progress-text">Initializing...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Devices Widget -->
            <div class="widget devices-widget">
                <div class="widget-header">
                    <h2>📱 Device Management</h2>
                    <div class="device-count" id="device-count">0 devices</div>
                </div>
                <div class="device-actions">
                    <button class="btn btn-success" onclick="showCreateDeviceModal()">➕ Create Device</button>
                    <button class="btn" onclick="refreshDevices()">🔄 Refresh</button>
                </div>

                <!-- Progress Display Area -->
                <div id="device-progress" class="progress-container" style="display: none;">
                    <div class="progress-message">
                        <div class="spinner"></div>
                        <span id="progress-text">Creating device...</span>
                    </div>
                </div>
                <div class="widget-content" id="devices-content">
                    <div id="devices-list">
                        <!-- Devices will be loaded here -->
                    </div>
                </div>
            </div>

            <!-- Gmail Account Creation Widget -->
            <div class="widget gmail-widget">
                <div class="widget-header">
                    <h2>📧 Gmail Account Creator</h2>
                    <div class="gmail-stats" id="gmail-stats">0 accounts created</div>
                </div>

                <div class="widget-content">
                    <div class="gmail-controls">
                        <div class="form-group">
                            <label for="gmail-count">Number of Accounts:</label>
                            <input type="number" id="gmail-count" value="1" min="1" max="5" class="form-input">
                            <small>Maximum 5 accounts per batch</small>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="use-turkish-data" checked>
                                <span class="checkmark"></span>
                                Use Turkish Names & Data
                            </label>
                        </div>

                        <div class="form-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="cleanup-device" checked>
                                <span class="checkmark"></span>
                                Cleanup Device After Creation
                            </label>
                        </div>

                        <div class="gmail-actions">
                            <button class="btn btn-success" onclick="createGmailAccount()" id="create-gmail-btn">
                                ➕ Create Gmail Account
                            </button>
                            <button class="btn btn-info" onclick="testTurkishData()">
                                🧪 Test Turkish Data
                            </button>
                            <button class="btn btn-secondary" onclick="refreshGmailAccounts()">
                                🔄 Refresh Accounts
                            </button>
                        </div>

                        <div id="gmail-progress" class="progress-container" style="display: none;">
                            <div class="progress-bar">
                                <div class="progress-fill" id="gmail-progress-bar"></div>
                            </div>
                            <div class="progress-text" id="gmail-progress-text">Initializing...</div>
                        </div>
                    </div>

                    <div class="gmail-accounts">
                        <h3>📋 Created Accounts</h3>
                        <div id="gmail-accounts-list" class="accounts-list">
                            <div class="loading-message">Loading accounts...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Logs Widget -->
            <div class="widget logs-widget">
                <h2>📋 System Logs</h2>
                <div class="widget-content">
                    <div class="logs-container" id="logs-container">
                        <div class="log-entry info">[INFO] Dashboard initialized</div>
                        <div class="log-entry info">[INFO] WebSocket connection established</div>
                        <div class="log-entry info">[INFO] Ready for automation scenarios</div>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Create Device Modal -->
    <div id="createDeviceModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>📱 Create New Device</h3>
                <span class="close" onclick="hideCreateDeviceModal()">&times;</span>
            </div>
            <form id="createDeviceForm">
                <div class="form-group">
                    <label for="deviceName">Device Name:</label>
                    <input type="text" id="deviceName" name="deviceName" required>
                </div>
                <div class="form-group">
                    <label for="deviceProfile">Hardware Profile:</label>
                    <select id="deviceProfile" name="deviceProfile" required>
                        <option value="">Loading profiles...</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="androidVersion">Android Version:</label>
                    <select id="androidVersion" name="androidVersion" required>
                        <option value="14" selected>Android 14.0 (Available)</option>
                    </select>
                    <small class="form-text">Only Android 14.0 image is currently downloaded</small>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn btn-secondary" onclick="hideCreateDeviceModal()">Cancel</button>
                    <button type="submit" class="btn btn-success" id="createDeviceBtn">
                        <span id="createDeviceText">Create Device</span>
                        <span id="createDeviceLoading" class="loading hidden"></span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // WebSocket connection
        let ws = null;
        let devices = [];
        let currentScenario = null;
        let hardwareProfiles = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            connectWebSocket();
            loadDevices(true); // Initial load
            loadHardwareProfiles();
            setInterval(() => loadDevices(false), 10000); // Refresh devices every 10 seconds

            // Setup create device form
            document.getElementById('createDeviceForm').addEventListener('submit', handleCreateDevice);
        });

        // WebSocket connection
        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            ws = new WebSocket(wsUrl);

            ws.onopen = function() {
                addLog('WebSocket connected', 'success');
            };

            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };

            ws.onclose = function() {
                addLog('WebSocket disconnected, attempting to reconnect...', 'warning');
                setTimeout(connectWebSocket, 3000);
            };

            ws.onerror = function(error) {
                addLog('WebSocket error: ' + error, 'error');
            };
        }

        // Handle WebSocket messages
        function handleWebSocketMessage(data) {
            if (data.type === 'scenario_progress') {
                updateScenarioProgress(data.data);
            } else if (data.type === 'scenario_complete') {
                handleScenarioComplete(data.result);
            } else if (data.type === 'device_creation_progress') {
                handleDeviceCreationProgress(data.data);
            } else if (data.type === 'log') {
                addLog(data.message, data.level || 'info');
            }
        }

        function handleDeviceCreationProgress(data) {
            const { instance_name, stage, message } = data;

            // Show progress message
            showProgress(message, stage);

            // Also add to logs
            addLog(message, stage === 'error' ? 'error' : 'info');

            // Refresh device list when completed or error
            if (stage === 'completed' || stage === 'error') {
                setTimeout(() => {
                    refreshDevices();
                }, 2000);
            }
        }

        function showProgress(message, stage = 'progress') {
            const progressContainer = document.getElementById('device-progress');
            const progressText = document.getElementById('progress-text');

            progressContainer.className = 'progress-container';
            if (stage === 'completed') {
                progressContainer.classList.add('success');
            } else if (stage === 'error') {
                progressContainer.classList.add('error');
            }

            progressText.textContent = message;
            progressContainer.style.display = 'block';

            // Auto-hide success/error messages after 5 seconds
            if (stage === 'completed' || stage === 'error') {
                setTimeout(() => {
                    progressContainer.style.display = 'none';
                }, 5000);
            }
        }

        function hideProgress() {
            const progressContainer = document.getElementById('device-progress');
            progressContainer.style.display = 'none';
        }

        // Add log entry
        function addLog(message, level = 'info') {
            const logsContainer = document.getElementById('logs-container');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${level}`;
            logEntry.textContent = `[${timestamp}] ${message}`;

            logsContainer.appendChild(logEntry);
            logsContainer.scrollTop = logsContainer.scrollHeight;

            // Keep only last 100 log entries
            while (logsContainer.children.length > 100) {
                logsContainer.removeChild(logsContainer.firstChild);
            }
        }

        // Load devices
        async function loadDevices(isInitialLoad = false) {
            try {
                const devicesList = document.getElementById('devices-list');

                if (isInitialLoad && devicesList) {
                    // Only clear the list on initial load
                    devicesList.innerHTML = '<div style="text-align: center; padding: 20px; color: #666;">🔄 Loading devices...</div>';
                } else if (devicesList && devices.length > 0) {
                    // For subsequent refreshes, just prepend a refresh indicator
                    const refreshIndicator = document.createElement('div');
                    refreshIndicator.id = 'refresh-indicator';
                    refreshIndicator.style.cssText = 'text-align: center; padding: 10px; color: #666; background: #f8f9fa; border-radius: 6px; margin-bottom: 10px; font-size: 0.9rem;';
                    refreshIndicator.innerHTML = '🔄 Refreshing devices...';

                    // Remove any existing refresh indicator
                    const existingIndicator = document.getElementById('refresh-indicator');
                    if (existingIndicator) {
                        existingIndicator.remove();
                    }

                    devicesList.insertBefore(refreshIndicator, devicesList.firstChild);
                }

                const response = await fetch('/api/instances');
                const data = await response.json();

                // Convert instances object to array
                if (data.instances && typeof data.instances === 'object') {
                    devices = Object.keys(data.instances).map(name => ({
                        name: name,
                        ...data.instances[name]
                    }));
                } else {
                    devices = [];
                }

                updateDevicesList();
                updateDeviceSelector();
                updateDeviceCount();

                // Remove refresh indicator after update
                const refreshIndicator = document.getElementById('refresh-indicator');
                if (refreshIndicator) {
                    setTimeout(() => refreshIndicator.remove(), 500);
                }

                // Log successful refresh
                console.log(`Device list refreshed: ${devices.length} devices found`);

            } catch (error) {
                addLog('Failed to load devices: ' + error.message, 'error');
                // Remove refresh indicator on error
                const refreshIndicator = document.getElementById('refresh-indicator');
                if (refreshIndicator) {
                    refreshIndicator.remove();
                }

                // Show error state only if it's initial load
                if (isInitialLoad) {
                    const devicesList = document.getElementById('devices-list');
                    if (devicesList) {
                        devicesList.innerHTML = '<div style="text-align: center; padding: 20px; color: #999;">Failed to load devices</div>';
                    }
                }
            }
        }

        // Update devices list
        function updateDevicesList() {
            const devicesList = document.getElementById('devices-list');

            if (!devicesList) {
                console.error('devices-list element not found');
                return;
            }

            if (devices.length === 0) {
                devicesList.innerHTML = '<div style="text-align: center; color: #718096; padding: 20px;">No devices found</div>';
                return;
            }

            devicesList.innerHTML = devices.map(device => {
                const status = device.status || 'unknown';
                const isRunning = status.toLowerCase().includes('running') || status.toLowerCase().includes('on');
                const statusClass = isRunning ? 'running' : 'stopped';
                const statusText = isRunning ? 'Running' : 'Stopped';

                return `
                <div class="device-item ${statusClass}">
                    <div class="device-header">
                        <div class="device-name">${device.name}</div>
                        <div class="device-status ${statusClass}">${statusText}</div>
                    </div>
                    <div class="device-details">
                        ADB: ${device.adb_serial || device.device_id || 'N/A'} |
                        Android: ${device.android_version || 'N/A'} |
                        Model: ${device.hardware_profile || 'N/A'}
                    </div>
                    <div class="device-controls">
                        ${!isRunning ?
                            `<button class="btn btn-sm btn-success" onclick="startDevice('${device.name}')">▶️ Start</button>` :
                            `<button class="btn btn-sm btn-warning" onclick="stopDevice('${device.name}')">⏸️ Stop</button>`
                        }
                        <button class="btn btn-sm btn-danger" onclick="deleteDevice('${device.name}')">🗑️ Delete</button>
                    </div>
                    <input type="text" class="proxy-input" placeholder="Proxy: host:port"
                           value="${device.proxy || ''}"
                           onchange="setDeviceProxy('${device.name}', this.value)">
                </div>
                `;
            }).join('');
        }

        // Update device count display
        function updateDeviceCount() {
            const deviceCountElement = document.getElementById('device-count');
            if (!deviceCountElement) return;

            const count = devices.length;
            const runningCount = devices.filter(device => {
                const status = device.status || 'unknown';
                return status.toLowerCase().includes('running') || status.toLowerCase().includes('on');
            }).length;

            let countText;
            if (count === 0) {
                countText = 'No devices';
                deviceCountElement.className = 'device-count';
            } else if (count === 1) {
                countText = '1 device';
                deviceCountElement.className = 'device-count has-devices';
            } else {
                countText = `${count} devices`;
                deviceCountElement.className = 'device-count has-devices';
            }

            // Add running count if there are running devices
            if (runningCount > 0) {
                countText += ` (${runningCount} running)`;
            }

            deviceCountElement.textContent = countText;
        }

        // Update device selector
        function updateDeviceSelector() {
            const deviceSelect = document.getElementById('device-select');
            const currentValue = deviceSelect.value;

            deviceSelect.innerHTML = '<option value="">Create new device</option>';

            devices.forEach(device => {
                const option = document.createElement('option');
                option.value = device.name;
                const status = device.status || 'unknown';
                const isRunning = status.toLowerCase().includes('running') || status.toLowerCase().includes('on');
                const statusText = isRunning ? 'Running' : 'Stopped';
                option.textContent = `${device.name} (${statusText})`;
                deviceSelect.appendChild(option);
            });

            // Restore selection if still valid
            if (currentValue && devices.find(d => d.name === currentValue)) {
                deviceSelect.value = currentValue;
            }
        }

        // Run automation scenario
        async function runScenario() {
            const deviceSelect = document.getElementById('device-select');
            const selectedDevice = deviceSelect.value;
            const runBtn = document.getElementById('run-scenario-btn');
            const progressContainer = document.getElementById('progress-container');

            try {
                runBtn.disabled = true;
                runBtn.innerHTML = '<span class="loading"></span> Running...';
                progressContainer.style.display = 'block';

                const payload = {};
                if (selectedDevice) {
                    payload.instance_name = selectedDevice;
                } else {
                    // Don't set instance_name to let the scenario generate one
                    payload.instance_name = null;
                }

                addLog(`Starting HTC One automation scenario${selectedDevice ? ` on device: ${selectedDevice}` : ' with new device'}`, 'info');

                const response = await fetch('/api/scenarios/htc-one-google', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(payload)
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                addLog('Scenario started successfully', 'success');

            } catch (error) {
                addLog('Failed to start scenario: ' + error.message, 'error');
                resetScenarioUI();
            }
        }

        // Update scenario progress
        function updateScenarioProgress(data) {
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            if (progressFill && progressText) {
                progressFill.style.width = `${data.progress || 0}%`;
                progressText.textContent = `${data.progress || 0}% - ${data.message || 'Processing...'}`;
            }

            addLog(`[${data.progress || 0}%] ${data.step || 'unknown'}: ${data.message || 'Processing...'}`, 'info');
        }

        // Handle scenario completion
        function handleScenarioComplete(result) {
            const progressFill = document.getElementById('progress-fill');
            const progressText = document.getElementById('progress-text');

            if (result.success) {
                progressFill.style.width = '100%';
                progressText.textContent = '100% - Scenario completed successfully!';
                addLog('Scenario completed successfully!', 'success');
                if (result.device_id) {
                    addLog(`Device ID: ${result.device_id}`, 'info');
                }
            } else {
                progressText.textContent = 'Scenario failed: ' + (result.error || 'Unknown error');
                addLog('Scenario failed: ' + (result.error || 'Unknown error'), 'error');
            }

            setTimeout(resetScenarioUI, 3000);
            loadDevices(); // Refresh devices list
        }

        // Reset scenario UI
        function resetScenarioUI() {
            const runBtn = document.getElementById('run-scenario-btn');
            const progressContainer = document.getElementById('progress-container');

            runBtn.disabled = false;
            runBtn.innerHTML = '▶️ Run Scenario';
            progressContainer.style.display = 'none';
        }

        // Device management functions

        // Helper function to update device status immediately for better UX
        function updateDeviceStatus(deviceName, newStatus) {
            const deviceItems = document.querySelectorAll('.device-item');
            deviceItems.forEach(item => {
                const nameElement = item.querySelector('.device-name');
                if (nameElement && nameElement.textContent === deviceName) {
                    const statusElement = item.querySelector('.device-status');
                    const deviceItem = item;

                    // Update status text and styling
                    if (newStatus === 'stopping') {
                        statusElement.textContent = 'Stopping...';
                        statusElement.className = 'device-status stopping';
                        deviceItem.className = 'device-item stopping';

                        // Disable buttons during stopping
                        const buttons = item.querySelectorAll('button');
                        buttons.forEach(btn => {
                            btn.disabled = true;
                            btn.style.opacity = '0.6';
                        });
                    } else if (newStatus === 'starting') {
                        statusElement.textContent = 'Starting...';
                        statusElement.className = 'device-status starting';
                        deviceItem.className = 'device-item starting';

                        // Disable buttons during starting
                        const buttons = item.querySelectorAll('button');
                        buttons.forEach(btn => {
                            btn.disabled = true;
                            btn.style.opacity = '0.6';
                        });
                    }
                }
            });
        }

        async function startDevice(deviceName) {
            try {
                // Update UI to show starting status immediately
                updateDeviceStatus(deviceName, 'starting');
                addLog(`Starting device: ${deviceName}`, 'info');

                const response = await fetch(`/api/instances/${deviceName}/start`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                addLog(`Device started: ${deviceName}`, 'success');

                // Immediate refresh to get actual status
                await loadDevices(false);

                // Additional refresh to ensure status is fully updated
                setTimeout(async () => {
                    await loadDevices(false);
                }, 3000);

            } catch (error) {
                addLog('Failed to start device: ' + error.message, 'error');
                // Refresh to restore correct status on error
                await loadDevices(false);
            }
        }

        async function stopDevice(deviceName) {
            try {
                // Update UI to show stopping status immediately
                updateDeviceStatus(deviceName, 'stopping');
                addLog(`Stopping device: ${deviceName}`, 'info');

                const response = await fetch(`/api/instances/${deviceName}/stop`, {
                    method: 'POST'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                addLog(`Device stopped: ${deviceName}`, 'success');

                // Immediate refresh to get actual status
                await loadDevices(false);

                // Additional refresh to ensure status is fully updated
                setTimeout(async () => {
                    await loadDevices(false);
                }, 2000);

            } catch (error) {
                addLog('Failed to stop device: ' + error.message, 'error');
                // Refresh to restore correct status on error
                await loadDevices(false);
            }
        }

        async function deleteDevice(deviceName) {
            if (!confirm(`Are you sure you want to delete device "${deviceName}"?`)) {
                return;
            }

            try {
                addLog(`Deleting device: ${deviceName}`, 'info');

                const response = await fetch(`/api/instances/${deviceName}`, {
                    method: 'DELETE'
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                addLog(`Device deleted: ${deviceName}`, 'success');

                // Immediate refresh
                await loadDevices(false);

                // Update device selector to remove deleted device
                setTimeout(() => {
                    updateDeviceSelector();
                }, 500);

            } catch (error) {
                addLog('Failed to delete device: ' + error.message, 'error');
            }
        }



        async function setDeviceProxy(deviceName, proxy) {
            try {
                const response = await fetch(`/api/instances/${deviceName}/proxy`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ proxy: proxy })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                addLog(`Proxy set for ${deviceName}: ${proxy || 'none'}`, 'info');

            } catch (error) {
                addLog('Failed to set proxy: ' + error.message, 'error');
            }
        }

        async function refreshDevices() {
            addLog('Manually refreshing devices list...', 'info');
            await loadDevices(false);
            addLog('Device list refresh completed', 'success');
        }

        // Load hardware profiles
        async function loadHardwareProfiles() {
            try {
                const response = await fetch('/api/hardware-profiles');
                const data = await response.json();

                if (data.success && data.profiles) {
                    hardwareProfiles = data.profiles;
                    updateHardwareProfileSelect();
                } else {
                    addLog('Failed to load hardware profiles', 'warning');
                }
            } catch (error) {
                addLog('Failed to load hardware profiles: ' + error.message, 'error');
                // Fallback to common profiles
                hardwareProfiles = [
                    { name: 'Custom Phone', display: '768 x 1280 dpi 320' },
                    { name: 'Google Pixel', display: '1080 x 1920 dpi 420' },
                    { name: 'Google Pixel 2', display: '1080 x 1920 dpi 420' },
                    { name: 'HTC One', display: '1080 x 1920 dpi 480' },
                    { name: 'Samsung Galaxy S8', display: '1440 x 2960 dpi 570' }
                ];
                updateHardwareProfileSelect();
            }
        }

        // Update hardware profile select options
        function updateHardwareProfileSelect() {
            const select = document.getElementById('deviceProfile');
            select.innerHTML = '<option value="">Select hardware profile...</option>';

            hardwareProfiles.forEach(profile => {
                const option = document.createElement('option');
                option.value = profile.name;
                option.textContent = `${profile.name} (${profile.display || 'Unknown specs'})`;
                select.appendChild(option);
            });

            // Add event listener to update device name when hardware profile changes
            select.removeEventListener('change', onHardwareProfileChange); // Remove existing listener
            select.addEventListener('change', onHardwareProfileChange);
        }

        // Handle hardware profile selection change
        function onHardwareProfileChange(event) {
            const selectedProfile = event.target.value;
            const deviceNameInput = document.getElementById('deviceName');

            // Only update if the device name input exists and is visible (modal is open)
            if (deviceNameInput && deviceNameInput.offsetParent !== null) {
                // Generate new device name based on selected hardware profile
                deviceNameInput.value = generateRandomDeviceName(selectedProfile);
            }
        }

        // Generate random device name based on hardware profile
        function generateRandomDeviceName(hardwareProfile = null) {
            const adjectives = ['Fast', 'Smart', 'Cool', 'Super', 'Pro', 'Elite', 'Prime', 'Ultra', 'Mega', 'Turbo'];
            const randomAdj = adjectives[Math.floor(Math.random() * adjectives.length)];
            const randomNum = Math.floor(Math.random() * 1000);

            // Use hardware profile name if provided, otherwise use generic names
            if (hardwareProfile) {
                // Clean up the hardware profile name (remove spaces, special chars)
                const cleanProfileName = hardwareProfile.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '');
                return `${randomAdj}_${cleanProfileName}_${randomNum}`;
            } else {
                const nouns = ['Device', 'Phone', 'Android', 'Mobile', 'Gadget', 'Unit', 'Bot', 'Machine'];
                const randomNoun = nouns[Math.floor(Math.random() * nouns.length)];
                return `${randomAdj}_${randomNoun}_${randomNum}`;
            }
        }

        // Show create device modal
        function showCreateDeviceModal() {
            const modal = document.getElementById('createDeviceModal');
            const deviceNameInput = document.getElementById('deviceName');
            const profileSelect = document.getElementById('deviceProfile');

            // Set default hardware profile if available
            if (hardwareProfiles.length > 0) {
                // Default to first available profile (usually Custom Phone)
                profileSelect.value = hardwareProfiles[0].name;
                // Generate device name based on default profile
                deviceNameInput.value = generateRandomDeviceName(hardwareProfiles[0].name);
            } else {
                // Fallback to generic name if no profiles loaded
                deviceNameInput.value = generateRandomDeviceName();
            }

            // Show modal
            modal.style.display = 'block';

            // Focus on device name input
            setTimeout(() => deviceNameInput.focus(), 100);
        }

        // Hide create device modal
        function hideCreateDeviceModal() {
            const modal = document.getElementById('createDeviceModal');
            modal.style.display = 'none';

            // Reset form
            document.getElementById('createDeviceForm').reset();
            document.getElementById('createDeviceBtn').disabled = false;
            document.getElementById('createDeviceText').style.display = 'inline';
            document.getElementById('createDeviceLoading').classList.add('hidden');
        }

        // Handle create device form submission
        async function handleCreateDevice(event) {
            event.preventDefault();

            const form = event.target;
            const formData = new FormData(form);
            const deviceName = formData.get('deviceName');
            const deviceProfile = formData.get('deviceProfile');
            const androidVersion = formData.get('androidVersion');

            if (!deviceName || !deviceProfile || !androidVersion) {
                addLog('Please fill in all required fields', 'error');
                return;
            }

            // Update button state
            const createBtn = document.getElementById('createDeviceBtn');
            const createText = document.getElementById('createDeviceText');
            const createLoading = document.getElementById('createDeviceLoading');

            createBtn.disabled = true;
            createText.style.display = 'none';
            createLoading.classList.remove('hidden');

            try {
                addLog(`Creating device: ${deviceName} (${deviceProfile}, Android ${androidVersion})`, 'info');

                // Hide modal immediately so user can see progress
                hideCreateDeviceModal();

                // Show initial progress
                showProgress(`Preparing to create device '${deviceName}'...`, 'progress');

                const response = await fetch('/api/instances', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        name: deviceName,
                        device_profile: deviceProfile,
                        android_version: androidVersion
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                addLog(`Device creation process started: ${deviceName}`, 'success');

                // Progress updates and device list refresh will be handled by WebSocket messages
                // Modal already closed, no immediate refresh needed as the process takes time

            } catch (error) {
                addLog('Failed to create device: ' + error.message, 'error');
                showProgress(`Failed to create device: ${error.message}`, 'error');

                // Reset button state
                createBtn.disabled = false;
                createText.style.display = 'inline';
                createLoading.classList.add('hidden');
            }
        }

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('createDeviceModal');
            if (event.target === modal) {
                hideCreateDeviceModal();
            }
        }

        // Gmail Account Creation Functions
        let gmailCreationInProgress = false;

        // Initialize Gmail widget
        document.addEventListener('DOMContentLoaded', function() {
            refreshGmailAccounts();
        });

        // Create Gmail account
        async function createGmailAccount() {
            if (gmailCreationInProgress) {
                addLog('Gmail account creation already in progress', 'warning');
                return;
            }

            try {
                gmailCreationInProgress = true;
                const createBtn = document.getElementById('create-gmail-btn');
                const progressContainer = document.getElementById('gmail-progress');
                const progressBar = document.getElementById('gmail-progress-bar');
                const progressText = document.getElementById('gmail-progress-text');

                // Get form values
                const count = parseInt(document.getElementById('gmail-count').value) || 1;
                const useTurkishData = document.getElementById('use-turkish-data').checked;
                const cleanupDevice = document.getElementById('cleanup-device').checked;

                // Update UI
                createBtn.disabled = true;
                createBtn.innerHTML = '⏳ Creating Account...';
                progressContainer.style.display = 'block';
                progressBar.style.width = '0%';
                progressText.textContent = 'Initializing Gmail account creation...';

                addLog(`Starting Gmail account creation (${count} account${count > 1 ? 's' : ''})`, 'info');

                // Make API request
                const response = await fetch('/api/gmail/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        count: count,
                        use_turkish_data: useTurkishData,
                        cleanup_device: cleanupDevice,
                        save_to_file: true
                    })
                });

                const result = await response.json();

                if (result.success) {
                    progressBar.style.width = '100%';
                    progressText.textContent = `Successfully created ${result.successful_count} account(s)!`;

                    addLog(`✅ Gmail creation completed: ${result.successful_count}/${result.total_requested} successful`, 'success');

                    // Show created accounts
                    if (result.successful_accounts && result.successful_accounts.length > 0) {
                        result.successful_accounts.forEach(account => {
                            addLog(`📧 Created: ${account.email} - ${account.first_name} ${account.last_name}`, 'success');
                        });
                    }

                    // Refresh accounts list
                    setTimeout(() => {
                        refreshGmailAccounts();
                    }, 1000);

                } else {
                    progressText.textContent = 'Gmail account creation failed';
                    addLog(`❌ Gmail creation failed: ${result.error || 'Unknown error'}`, 'error');
                }

            } catch (error) {
                addLog(`❌ Gmail creation error: ${error.message}`, 'error');
                document.getElementById('gmail-progress-text').textContent = 'Error occurred during creation';
            } finally {
                gmailCreationInProgress = false;

                // Reset UI after delay
                setTimeout(() => {
                    const createBtn = document.getElementById('create-gmail-btn');
                    const progressContainer = document.getElementById('gmail-progress');

                    createBtn.disabled = false;
                    createBtn.innerHTML = '➕ Create Gmail Account';
                    progressContainer.style.display = 'none';
                }, 3000);
            }
        }

        // Test Turkish data generation
        async function testTurkishData() {
            try {
                addLog('Testing Turkish data generation...', 'info');

                const response = await fetch('/api/gmail/test-data', {
                    method: 'POST'
                });

                const result = await response.json();

                if (result.success && result.test_profiles) {
                    addLog('✅ Turkish data generation test successful:', 'success');

                    result.test_profiles.forEach((profile, index) => {
                        addLog(`  Profile ${index + 1}: ${profile.first_name} ${profile.last_name} (${profile.username})`, 'info');
                    });
                } else {
                    addLog('❌ Turkish data test failed', 'error');
                }

            } catch (error) {
                addLog(`❌ Turkish data test error: ${error.message}`, 'error');
            }
        }

        // Refresh Gmail accounts list
        async function refreshGmailAccounts() {
            try {
                const accountsList = document.getElementById('gmail-accounts-list');
                const gmailStats = document.getElementById('gmail-stats');

                // Show loading
                accountsList.innerHTML = '<div class="loading-message">Loading accounts...</div>';

                const response = await fetch('/api/gmail/accounts?limit=10');
                const result = await response.json();

                if (result.success) {
                    // Update stats
                    gmailStats.textContent = `${result.total_accounts} accounts created`;

                    // Update accounts list
                    if (result.accounts && result.accounts.length > 0) {
                        accountsList.innerHTML = result.accounts.map(account => `
                            <div class="account-item">
                                <div class="account-info">
                                    <div class="account-email">${account.email}</div>
                                    <div class="account-details">
                                        ${account.first_name} ${account.last_name} • ${account.birth_date} • ${account.created_at}
                                    </div>
                                </div>
                                <div class="account-actions">
                                    <button class="btn btn-sm btn-info" onclick="copyAccountInfo('${account.email}', '${account.password}')">
                                        📋 Copy
                                    </button>
                                </div>
                            </div>
                        `).join('');
                    } else {
                        accountsList.innerHTML = '<div class="loading-message">No accounts created yet</div>';
                    }

                } else {
                    accountsList.innerHTML = '<div class="loading-message">Error loading accounts</div>';
                    addLog('❌ Failed to load Gmail accounts', 'error');
                }

            } catch (error) {
                document.getElementById('gmail-accounts-list').innerHTML = '<div class="loading-message">Error loading accounts</div>';
                addLog(`❌ Error refreshing Gmail accounts: ${error.message}`, 'error');
            }
        }

        // Copy account info to clipboard
        async function copyAccountInfo(email, password) {
            try {
                const accountInfo = `Email: ${email}\nPassword: ${password}`;
                await navigator.clipboard.writeText(accountInfo);
                addLog(`📋 Copied account info for ${email}`, 'success');
            } catch (error) {
                addLog(`❌ Failed to copy account info: ${error.message}`, 'error');
            }
        }

        // Handle Gmail-related WebSocket messages
        function handleGmailWebSocketMessage(data) {
            if (data.type === 'gmail_account_created') {
                const { email, name, account_number, total_accounts } = data.data;
                addLog(`✅ Account ${account_number}/${total_accounts} created: ${email} (${name})`, 'success');

                // Update progress if visible
                const progressBar = document.getElementById('gmail-progress-bar');
                const progressText = document.getElementById('gmail-progress-text');

                if (progressBar && progressText) {
                    const progress = (account_number / total_accounts) * 100;
                    progressBar.style.width = `${progress}%`;
                    progressText.textContent = `Created ${account_number}/${total_accounts} accounts...`;
                }

                // Refresh accounts list
                refreshGmailAccounts();

            } else if (data.type === 'gmail_account_failed') {
                const { error, account_number, total_accounts } = data.data;
                addLog(`❌ Account ${account_number}/${total_accounts} failed: ${error}`, 'error');

            } else if (data.type === 'gmail_batch_progress') {
                const { current, total, status } = data.data;
                addLog(`📊 Batch progress: ${current}/${total} (${status})`, 'info');

            } else if (data.type === 'gmail_batch_completed') {
                const { successful, failed, success_rate } = data.data;
                addLog(`🏁 Batch completed: ${successful} successful, ${failed} failed (${success_rate.toFixed(1)}% success rate)`, 'success');
                refreshGmailAccounts();
            }
        }

        // Extend the existing WebSocket message handler
        const originalHandleWebSocketMessage = handleWebSocketMessage;
        handleWebSocketMessage = function(data) {
            // Handle Gmail messages
            if (data.type && data.type.startsWith('gmail_')) {
                handleGmailWebSocketMessage(data);
            } else {
                // Handle other messages with original handler
                originalHandleWebSocketMessage(data);
            }
        };
    </script>
</body>
</html>
