class AutomationDashboard {
    constructor() {
        this.ws = null;
        this.baseUrl = window.location.origin;
        this.reconnectInterval = 5000;
        this.init();
    }

    init() {
        this.connectWebSocket();
        this.loadInitialData();

        // Auto-refresh every 30 seconds
        setInterval(() => {
            this.refreshStatus();
            this.loadSessions();
            this.loadLogs();
            this.loadInstances();
        }, 30000);
    }

    connectWebSocket() {
        const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;

        try {
            this.ws = new WebSocket(wsUrl);

            this.ws.onopen = () => {
                console.log('WebSocket connected');
                this.updateConnectionStatus(true);
            };

            this.ws.onmessage = (event) => {
                const data = JSON.parse(event.data);
                this.handleWebSocketMessage(data);
            };

            this.ws.onclose = () => {
                console.log('WebSocket disconnected');
                this.updateConnectionStatus(false);

                // Attempt to reconnect
                setTimeout(() => {
                    this.connectWebSocket();
                }, this.reconnectInterval);
            };

            this.ws.onerror = (error) => {
                console.error('WebSocket error:', error);
                this.updateConnectionStatus(false);
            };

        } catch (error) {
            console.error('Failed to connect WebSocket:', error);
            this.updateConnectionStatus(false);
        }
    }

    updateConnectionStatus(connected) {
        const statusElement = document.getElementById('connection-status');
        if (connected) {
            statusElement.innerHTML = '<span class="badge bg-success">Connected</span>';
        } else {
            statusElement.innerHTML = '<span class="badge bg-danger">Disconnected</span>';
        }
    }

    handleWebSocketMessage(data) {
        switch (data.type) {
            case 'session_started':
                this.showNotification('Session started successfully', 'success');
                this.refreshStatus();
                this.loadSessions();
                break;

            case 'session_stopped':
                this.showNotification('Session stopped', 'info');
                this.refreshStatus();
                this.loadSessions();
                break;

            case 'session_rotated':
                this.showNotification('Session rotated for anti-detection', 'warning');
                this.refreshStatus();
                this.loadSessions();
                break;

            case 'task_completed':
                const taskData = data.data;
                const message = `Task ${taskData.task_type} ${taskData.success ? 'completed' : 'failed'}`;
                this.showNotification(message, taskData.success ? 'success' : 'danger');
                this.refreshStatus();
                break;

            case 'location_updated':
                const location = data.data;
                this.updateCurrentLocation(location);
                this.showNotification('Location updated', 'info');
                break;

            case 'instance_started':
                this.showNotification(`Instance ${data.data.instance_name} started`, 'success');
                this.loadInstances();
                break;

            case 'instance_stopped':
                this.showNotification(`Instance ${data.data.instance_name} stopped`, 'info');
                this.loadInstances();
                break;

            case 'all_instances_stopped':
                const stoppedCount = data.data.stopped_instances.length;
                this.showNotification(`Stopped ${stoppedCount} instances`, 'warning');
                this.loadInstances();
                this.refreshStatus();
                break;

            default:
                console.log('Unknown WebSocket message:', data);
        }
    }

    async loadInitialData() {
        await this.refreshStatus();
        await this.loadSessions();
        await this.loadLogs();
        await this.loadInstances();
    }

    async apiCall(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                }
            };

            if (data) {
                options.body = JSON.stringify(data);
            }

            const response = await fetch(`${this.baseUrl}/api${endpoint}`, options);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();

        } catch (error) {
            console.error(`API call failed for ${endpoint}:`, error);
            this.showNotification(`API call failed: ${error.message}`, 'danger');
            throw error;
        }
    }

    async refreshStatus() {
        try {
            const status = await this.apiCall('/status');
            this.updateStatusDisplay(status);
        } catch (error) {
            console.error('Failed to refresh status:', error);
        }
    }

    updateStatusDisplay(status) {
        // Appium Server Status
        const appiumElement = document.getElementById('appium-status');
        if (status.appium_server && status.appium_server.running) {
            appiumElement.textContent = 'Online';
            appiumElement.className = 'metric-value status-active';
        } else {
            appiumElement.textContent = 'Offline';
            appiumElement.className = 'metric-value status-inactive';
        }

        // Genymotion Status
        const genymotionElement = document.getElementById('genymotion-status');
        if (status.genymotion && status.genymotion.running) {
            genymotionElement.textContent = 'Online';
            genymotionElement.className = 'metric-value status-active';
        } else {
            genymotionElement.textContent = 'Offline';
            genymotionElement.className = 'metric-value status-inactive';
        }

        // Active Sessions Count
        document.getElementById('active-sessions-count').textContent =
            status.database ? status.database.active_sessions : 0;

        // Actions Count
        document.getElementById('actions-count').textContent =
            status.automation_client ? status.automation_client.action_count : 0;

        // Current Session Info
        this.updateCurrentSessionInfo(status.automation_client);
    }

    updateCurrentSessionInfo(clientStatus) {
        const container = document.getElementById('current-session-info');

        if (clientStatus && clientStatus.active) {
            container.innerHTML = `
                <div class="alert alert-success mt-3">
                    <strong>Active Session:</strong> ${clientStatus.session_id}<br>
                    <strong>Actions:</strong> ${clientStatus.action_count}
                </div>
            `;
        } else {
            container.innerHTML = `
                <div class="alert alert-secondary mt-3">
                    No active session
                </div>
            `;
        }
    }

    async loadSessions() {
        try {
            const sessions = await this.apiCall('/sessions');
            this.displaySessions(sessions);
        } catch (error) {
            console.error('Failed to load sessions:', error);
        }
    }

    displaySessions(sessions) {
        const container = document.getElementById('sessions-container');

        let html = '';

        // Active Sessions
        if (sessions.active_sessions && sessions.active_sessions.length > 0) {
            html += '<h6 class="text-success"><i class="fas fa-circle"></i> Active Sessions</h6>';
            sessions.active_sessions.forEach(session => {
                html += this.createSessionCard(session, true);
            });
        }

        // Recent Sessions
        if (sessions.session_history && sessions.session_history.length > 0) {
            html += '<h6 class="text-muted mt-3"><i class="fas fa-history"></i> Recent Sessions</h6>';
            sessions.session_history.slice(0, 5).forEach(session => {
                html += this.createSessionCard(session, false);
            });
        }

        if (!html) {
            html = '<p class="text-muted">No sessions found</p>';
        }

        container.innerHTML = html;
    }

    createSessionCard(session, isActive) {
        const statusBadge = isActive ?
            '<span class="badge bg-success">Active</span>' :
            `<span class="badge bg-secondary">${session.status || 'Completed'}</span>`;

        const duration = session.duration_seconds ?
            `${Math.round(session.duration_seconds / 60)} min` :
            (isActive ? this.calculateUptime(session.start_time) : 'N/A');

        return `
            <div class="card session-card mb-2" style="background-color: #363636;">
                <div class="card-body p-3">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <h6 class="card-title mb-1">
                                ${session.device_name || 'Unknown Device'} ${statusBadge}
                            </h6>
                            <small class="text-muted">
                                Model: ${session.device_model || 'N/A'} |
                                Platform: ${session.platform_version || 'N/A'} |
                                Actions: ${session.actions_count || 0}
                            </small>
                            <br>
                            <small class="text-muted">
                                Started: ${this.formatDateTime(session.start_time)} |
                                Duration: ${duration}
                            </small>
                        </div>
                        <div>
                            <button class="btn btn-sm btn-outline-light" onclick="dashboard.viewSessionDetails('${session.session_id}')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    calculateUptime(startTime) {
        const start = new Date(startTime);
        const now = new Date();
        const diffMinutes = Math.floor((now - start) / (1000 * 60));
        return `${diffMinutes} min`;
    }

    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        return new Date(dateTimeString).toLocaleString();
    }

    async loadLogs() {
        try {
            const levelFilter = document.getElementById('log-level-filter').value;
            const endpoint = levelFilter ? `/logs?level=${levelFilter}` : '/logs';
            const logsData = await this.apiCall(endpoint);
            this.displayLogs(logsData.logs);
        } catch (error) {
            console.error('Failed to load logs:', error);
        }
    }

    displayLogs(logs) {
        const container = document.getElementById('logs-container');

        if (!logs || logs.length === 0) {
            container.innerHTML = '<p class="text-muted">No logs found</p>';
            return;
        }

        let html = '';
        logs.forEach(log => {
            const levelClass = `log-${log.level.toLowerCase()}`;
            const timestamp = this.formatDateTime(log.timestamp);

            html += `
                <div class="log-entry ${levelClass}">
                    <span class="text-muted">[${timestamp}]</span>
                    <span class="badge bg-dark">${log.level}</span>
                    ${log.message}
                    ${log.session_id ? `<span class="text-muted">(Session: ${log.session_id.substring(0, 8)}...)</span>` : ''}
                </div>
            `;
        });

        container.innerHTML = html;
        container.scrollTop = 0; // Scroll to top for newest logs
    }

    showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show`;
        notification.style.position = 'fixed';
        notification.style.top = '70px';
        notification.style.right = '20px';
        notification.style.zIndex = '1050';
        notification.style.minWidth = '300px';

        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
    }

    updateCurrentLocation(location) {
        const container = document.getElementById('current-location');
        container.innerHTML = `
            <div class="alert alert-info mt-3">
                <strong>Current Location:</strong><br>
                Lat: ${location.latitude.toFixed(6)}<br>
                Lng: ${location.longitude.toFixed(6)}
            </div>
        `;
    }

    async viewSessionDetails(sessionId) {
        try {
            const details = await this.apiCall(`/sessions/${sessionId}`);
            this.showSessionDetailsModal(details);
        } catch (error) {
            console.error('Failed to load session details:', error);
        }
    }

    showSessionDetailsModal(details) {
        // Create modal content (you can enhance this with Bootstrap modal)
        alert(`Session Details:\nID: ${details.session_id}\nLogs: ${details.logs.length}\nTasks: ${details.tasks.length}`);
    }

    async loadInstances() {
        try {
            const response = await this.apiCall('/instances');
            if (response.success) {
                this.displayInstances(response.instances);
            }
        } catch (error) {
            console.error('Failed to load instances:', error);
            document.getElementById('instances-container').innerHTML =
                '<div class="alert alert-danger">Failed to load instances</div>';
        }
    }

    displayInstances(instances) {
        const container = document.getElementById('instances-container');

        if (Object.keys(instances).length === 0) {
            container.innerHTML = '<div class="alert alert-info">No Genymotion instances found</div>';
            return;
        }

        let html = '';
        for (const [instanceName, instanceInfo] of Object.entries(instances)) {
            const status = instanceInfo.status || 'unknown';
            const adbDevice = instanceInfo.adb_device || 'N/A';
            const adbStatus = instanceInfo.adb_status || 'unknown';

            let statusClass = 'status-offline';
            let statusIcon = 'fas fa-circle';

            switch (status) {
                case 'running':
                    statusClass = 'status-running';
                    statusIcon = 'fas fa-play-circle';
                    break;
                case 'stopped':
                    statusClass = 'status-stopped';
                    statusIcon = 'fas fa-stop-circle';
                    break;
                case 'available':
                    statusClass = 'status-available';
                    statusIcon = 'fas fa-pause-circle';
                    break;
            }

            html += `
                <div class="instance-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="mb-1">
                                <i class="fab fa-android"></i> ${instanceName}
                            </h6>
                            <small class="text-muted">
                                ADB: ${adbDevice} (${adbStatus})
                            </small>
                        </div>
                        <div class="d-flex align-items-center gap-2">
                            <span class="instance-status ${statusClass}">
                                <i class="${statusIcon}"></i> ${status.toUpperCase()}
                            </span>
                            <div class="btn-group btn-group-sm">
                                ${status === 'running' ?
                                    `<button class="btn btn-outline-danger" onclick="stopInstance('${instanceName}')">
                                        <i class="fas fa-stop"></i>
                                    </button>` :
                                    `<button class="btn btn-outline-success" onclick="startInstance('${instanceName}')">
                                        <i class="fas fa-play"></i>
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        container.innerHTML = html;
    }
}

// Session Control Functions
async function startSession() {
    const appPackage = document.getElementById('app-package').value;
    const instanceName = document.getElementById('instance-name').value;

    try {
        await dashboard.apiCall('/sessions/start', 'POST', {
            app_package: appPackage || null,
            instance_name: instanceName
        });
    } catch (error) {
        console.error('Failed to start session:', error);
    }
}

async function stopSession() {
    try {
        await dashboard.apiCall('/sessions/stop', 'POST');
    } catch (error) {
        console.error('Failed to stop session:', error);
    }
}

async function rotateSession() {
    const appPackage = document.getElementById('app-package').value;
    const instanceName = document.getElementById('instance-name').value;

    try {
        await dashboard.apiCall('/sessions/rotate', 'POST', {
            app_package: appPackage || null,
            instance_name: instanceName
        });
    } catch (error) {
        console.error('Failed to rotate session:', error);
    }
}

async function killAllSessions() {
    // Show confirmation dialog
    const confirmed = confirm(
        '⚠️ WARNING: This will kill ALL active sessions and stop the Appium server.\n\n' +
        'This action cannot be undone. Are you sure you want to continue?'
    );

    if (!confirmed) {
        return;
    }

    try {
        dashboard.showNotification('Killing all sessions...', 'warning');
        const response = await dashboard.apiCall('/sessions/kill-all', 'POST');

        if (response.success) {
            dashboard.showNotification(
                `Successfully killed ${response.killed_sessions.length} sessions`,
                'success'
            );

            // Clear current session info display
            document.getElementById('current-session-info').innerHTML = '';

            // Refresh dashboard data
            dashboard.refreshStatus();
            dashboard.loadSessions();
        } else {
            dashboard.showNotification('Failed to kill all sessions', 'danger');
        }
    } catch (error) {
        console.error('Failed to kill all sessions:', error);
        dashboard.showNotification('Error killing sessions: ' + error.message, 'danger');
    }
}

// Task Execution Functions
async function executeTap() {
    const x = parseInt(document.getElementById('tap-x').value);
    const y = parseInt(document.getElementById('tap-y').value);

    if (isNaN(x) || isNaN(y)) {
        dashboard.showNotification('Please enter valid X and Y coordinates', 'warning');
        return;
    }

    try {
        await dashboard.apiCall('/tasks/execute', 'POST', {
            task_type: 'tap',
            parameters: { x, y }
        });
    } catch (error) {
        console.error('Failed to execute tap:', error);
    }
}

async function executeScroll() {
    const direction = document.getElementById('scroll-direction').value;

    try {
        await dashboard.apiCall('/tasks/execute', 'POST', {
            task_type: 'scroll',
            parameters: { direction }
        });
    } catch (error) {
        console.error('Failed to execute scroll:', error);
    }
}

async function executeType() {
    const text = document.getElementById('type-text').value;

    if (!text) {
        dashboard.showNotification('Please enter text to type', 'warning');
        return;
    }

    try {
        await dashboard.apiCall('/tasks/execute', 'POST', {
            task_type: 'type',
            parameters: { text }
        });

        // Clear the input after successful execution
        document.getElementById('type-text').value = '';
    } catch (error) {
        console.error('Failed to execute type:', error);
    }
}

async function executeRandomActivity() {
    const duration = parseInt(document.getElementById('activity-duration').value);

    if (isNaN(duration) || duration < 1) {
        dashboard.showNotification('Please enter a valid duration', 'warning');
        return;
    }

    try {
        await dashboard.apiCall('/tasks/execute', 'POST', {
            task_type: 'random_activity',
            parameters: { duration }
        });
    } catch (error) {
        console.error('Failed to execute random activity:', error);
    }
}

// Location Control Functions
async function setLocationByCity() {
    const cityName = document.getElementById('city-select').value;

    if (!cityName) {
        dashboard.showNotification('Please select a city', 'warning');
        return;
    }

    try {
        await dashboard.apiCall('/location/set', 'POST', {
            city_name: cityName
        });
    } catch (error) {
        console.error('Failed to set location by city:', error);
    }
}

async function setCustomLocation() {
    const latitude = parseFloat(document.getElementById('custom-lat').value);
    const longitude = parseFloat(document.getElementById('custom-lng').value);

    if (isNaN(latitude) || isNaN(longitude)) {
        dashboard.showNotification('Please enter valid latitude and longitude', 'warning');
        return;
    }

    try {
        await dashboard.apiCall('/location/set', 'POST', {
            latitude: latitude,
            longitude: longitude
        });
    } catch (error) {
        console.error('Failed to set custom location:', error);
    }
}

async function setRandomLocation() {
    try {
        await dashboard.apiCall('/location/set', 'POST', {});
    } catch (error) {
        console.error('Failed to set random location:', error);
    }
}

// Instance Management Functions
async function refreshInstances() {
    await dashboard.loadInstances();
}

function showStartNewInstanceModal() {
    const modal = new bootstrap.Modal(document.getElementById('startNewInstanceModal'));
    modal.show();
}

async function startNewInstance() {
    const instanceName = document.getElementById('newInstanceName').value.trim();
    const androidVersion = document.getElementById('androidVersion').value;
    const instanceType = document.getElementById('instanceType').value;

    if (!instanceName) {
        dashboard.showNotification('Please enter an instance name', 'danger');
        return;
    }

    // Validate instance name (no spaces, special characters)
    if (!/^[a-zA-Z0-9_-]+$/.test(instanceName)) {
        dashboard.showNotification('Instance name can only contain letters, numbers, underscores, and hyphens', 'danger');
        return;
    }

    try {
        // Close the modal first
        const modal = bootstrap.Modal.getInstance(document.getElementById('startNewInstanceModal'));
        modal.hide();

        dashboard.showNotification(`Starting new instance "${instanceName}"...`, 'info');

        const response = await dashboard.apiCall(`/instances/${instanceName}/start`, 'POST');

        if (response.success) {
            dashboard.showNotification(response.message, 'success');
            await dashboard.loadInstances();

            // Clear the form
            document.getElementById('startNewInstanceForm').reset();
        } else {
            dashboard.showNotification(response.message || 'Failed to start instance', 'danger');
        }
    } catch (error) {
        console.error('Failed to start new instance:', error);
        dashboard.showNotification(`Failed to start instance: ${error.message}`, 'danger');
    }
}

async function openMultiInstanceManager() {
    try {
        dashboard.showNotification('Opening Genymotion Desktop...', 'info');
        const response = await dashboard.apiCall('/instances/open-manager', 'POST');

        if (response.success) {
            dashboard.showNotification(response.message, 'success');
        } else {
            // Show detailed error message with alternatives
            let message = response.message;
            if (response.alternatives && response.alternatives.length > 0) {
                message += '\n\nAlternatives:\n' + response.alternatives.map(alt => '• ' + alt).join('\n');
            }

            dashboard.showNotification(message, 'warning');

            // Show additional help in console
            console.warn('Multi-Instance Manager failed to open:', response);
            if (response.alternatives) {
                console.info('Try these alternatives:', response.alternatives);
            }
        }
    } catch (error) {
        console.error('Failed to open Multi-Instance Manager:', error);
        dashboard.showNotification(
            'Failed to open Genymotion Desktop. Try opening Genymotion manually from Applications folder.',
            'danger'
        );
    }
}

async function startInstance(instanceName) {
    try {
        dashboard.showNotification(`Starting instance ${instanceName}...`, 'info');
        const response = await dashboard.apiCall(`/instances/${instanceName}/start`, 'POST');

        if (response.success) {
            dashboard.showNotification(response.message, 'success');
            await dashboard.loadInstances();
        } else {
            dashboard.showNotification(response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to start instance:', error);
        dashboard.showNotification(`Failed to start instance ${instanceName}`, 'danger');
    }
}

async function stopInstance(instanceName) {
    const confirmed = confirm(`Are you sure you want to stop instance "${instanceName}"?`);
    if (!confirmed) return;

    try {
        dashboard.showNotification(`Stopping instance ${instanceName}...`, 'warning');
        const response = await dashboard.apiCall(`/instances/${instanceName}/stop`, 'POST');

        if (response.success) {
            dashboard.showNotification(response.message, 'success');
            await dashboard.loadInstances();
        } else {
            dashboard.showNotification(response.message, 'danger');
        }
    } catch (error) {
        console.error('Failed to stop instance:', error);
        dashboard.showNotification(`Failed to stop instance ${instanceName}`, 'danger');
    }
}

async function stopAllInstances() {
    const confirmed = confirm(
        '⚠️ WARNING: This will stop ALL running Genymotion instances.\n\n' +
        'This will terminate any active automation sessions. Are you sure?'
    );

    if (!confirmed) return;

    try {
        dashboard.showNotification('Stopping all instances...', 'warning');
        const response = await dashboard.apiCall('/instances/stop-all', 'POST');

        if (response.success) {
            dashboard.showNotification(
                `Successfully stopped ${response.stopped_instances.length} instances`,
                'success'
            );
            await dashboard.loadInstances();
            await dashboard.refreshStatus();
        } else {
            dashboard.showNotification('Failed to stop all instances', 'danger');
        }
    } catch (error) {
        console.error('Failed to stop all instances:', error);
        dashboard.showNotification('Error stopping instances: ' + error.message, 'danger');
    }
}

// Utility Functions
function refreshDashboard() {
    dashboard.loadInitialData();
}

function filterLogs() {
    dashboard.loadLogs();
}

// Initialize dashboard when page loads
let dashboard;
document.addEventListener('DOMContentLoaded', function() {
    dashboard = new AutomationDashboard();
});