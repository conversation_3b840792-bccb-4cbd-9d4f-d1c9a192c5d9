#!/usr/bin/env python3
"""
Debug Create Account Click
Test specifically the Create Account clicking functionality
"""

import sys
import os
import time
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator
from genymotion_manager import GenymotionManager


def debug_create_account_click():
    """Debug Create Account clicking"""
    print("🔘 Debug Create Account Click")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    gmail_creator = GmailAccountCreator()
    
    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()
    
    running_device = None
    device_id = None
    
    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not running_device:
        print("❌ No running device found")
        return False
    
    # Step 1: Navigate to Gmail first
    print("\n🌐 Step 1: Navigating to Gmail...")
    navigation_success = gmail_creator.navigate_to_gmail_signup(device_id)
    
    if not navigation_success:
        print("❌ Navigation failed")
        return False
    
    print("✅ Navigation completed")
    time.sleep(5)
    
    # Take screenshot before attempting click
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/before_create_account_click.png', 
                     shell=True, timeout=10)
        print("📸 Before click: /tmp/before_create_account_click.png")
    except:
        pass
    
    # Step 2: Find Create Account with OCR
    print("\n🔍 Step 2: Finding Create Account with OCR...")
    
    create_account_patterns = ['create account', 'hesap oluştur', 'create']
    
    coordinates = gmail_creator._find_element_with_ocr(device_id, create_account_patterns, "create account button")
    
    if coordinates:
        x, y = coordinates
        print(f"✅ Found Create Account at coordinates: ({x}, {y})")
        
        # Step 3: Try manual click at those coordinates
        print(f"\n🔘 Step 3: Manually clicking at ({x}, {y})...")
        
        # Method 1: Direct ADB tap
        print("   Method 1: Direct ADB tap...")
        tap_cmd = f'adb -s {device_id} shell input tap {x} {y}'
        result = subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=10)
        print(f"   Command: {tap_cmd}")
        print(f"   Result: {result.returncode}, stderr: {result.stderr}")
        
        time.sleep(3)
        
        # Take screenshot after manual click
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/after_manual_click.png', 
                         shell=True, timeout=10)
            print("📸 After manual click: /tmp/after_manual_click.png")
        except:
            pass
        
        # Step 4: Try OCR click method
        print(f"\n🔘 Step 4: Using OCR click method...")
        
        ocr_success = gmail_creator._click_element_with_ocr(device_id, create_account_patterns, "create account button")
        
        if ocr_success:
            print("✅ OCR click method succeeded")
        else:
            print("❌ OCR click method failed")
        
        time.sleep(3)
        
        # Take screenshot after OCR click
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/after_ocr_click.png', 
                         shell=True, timeout=10)
            print("📸 After OCR click: /tmp/after_ocr_click.png")
        except:
            pass
        
        # Step 5: Check if menu appeared
        print(f"\n🔍 Step 5: Checking if menu appeared...")
        
        # Look for "For my personal use" to see if menu opened
        personal_use_patterns = ['for my personal use', 'personal use', 'personal']
        menu_coordinates = gmail_creator._find_element_with_ocr(device_id, personal_use_patterns, "personal use menu")
        
        if menu_coordinates:
            mx, my = menu_coordinates
            print(f"✅ Menu appeared! Found 'For my personal use' at ({mx}, {my})")
            return True
        else:
            print("❌ Menu did not appear - Create Account click may not have worked")
            
            # Try alternative coordinates around the found location
            print("\n🔘 Step 6: Trying alternative click positions...")
            
            alternative_coords = [
                (x-10, y), (x+10, y), (x, y-10), (x, y+10),  # Slight offsets
                (x-20, y), (x+20, y), (x, y-20), (x, y+20)   # Larger offsets
            ]
            
            for i, (ax, ay) in enumerate(alternative_coords):
                print(f"   Trying position {i+1}: ({ax}, {ay})")
                alt_tap_cmd = f'adb -s {device_id} shell input tap {ax} {ay}'
                subprocess.run(alt_tap_cmd, shell=True, capture_output=True, text=True, timeout=5)
                time.sleep(2)
                
                # Check if menu appeared
                menu_check = gmail_creator._find_element_with_ocr(device_id, personal_use_patterns, "personal use menu check")
                if menu_check:
                    print(f"✅ Success! Menu appeared with alternative position {i+1}")
                    return True
            
            print("❌ None of the alternative positions worked")
            return False
    else:
        print("❌ Could not find Create Account with OCR")
        return False


def main():
    """Main test execution"""
    success = debug_create_account_click()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Create Account click debug SUCCESSFUL!")
        print("🎉 Successfully found and clicked Create Account!")
    else:
        print("❌ Create Account click debug FAILED")
        print("🔍 Check the screenshots for debugging:")
        print("   - /tmp/before_create_account_click.png")
        print("   - /tmp/after_manual_click.png")
        print("   - /tmp/after_ocr_click.png")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
