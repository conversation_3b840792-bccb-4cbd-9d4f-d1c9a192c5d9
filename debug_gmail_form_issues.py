#!/usr/bin/env python3
"""
Debug Gmail Form Issues
Focused test to debug the specific Gmail form interaction problems
"""

import sys
import os
import time
import subprocess
import re
from typing import Dict, List, Optional

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


class GmailFormDebugger:
    def __init__(self):
        self.genymotion = GenymotionManager()
        
    def fix_adb_connection(self, device_id: str) -> bool:
        """Fix ADB connection issues"""
        print(f"🔧 Fixing ADB connection for {device_id}...")
        
        try:
            # Kill ADB server
            subprocess.run('adb kill-server', shell=True, timeout=10)
            time.sleep(2)
            
            # Start ADB server
            subprocess.run('adb start-server', shell=True, timeout=10)
            time.sleep(2)
            
            # Connect to device
            if ':' in device_id:  # Network ADB
                subprocess.run(f'adb connect {device_id}', shell=True, timeout=10)
                time.sleep(3)
            
            # Test connection
            result = subprocess.run(f'adb -s {device_id} shell echo "test"', 
                                  shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                print("✅ ADB connection fixed")
                return True
            else:
                print(f"❌ ADB connection still failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error fixing ADB connection: {e}")
            return False
    
    def get_current_activity(self, device_id: str) -> str:
        """Get current activity/app"""
        try:
            cmd = f'adb -s {device_id} shell dumpsys window windows | grep -E "mCurrentFocus|mFocusedApp"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return "Unknown"
                
        except Exception as e:
            return f"Error: {e}"
    
    def analyze_gmail_form_fields(self, device_id: str) -> Dict:
        """Analyze Gmail form fields in detail"""
        print("🔍 Analyzing Gmail form fields...")
        
        try:
            # Get UI dump
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                return {"error": "Failed to get UI dump"}
            
            ui_content = result.stdout
            
            # Analyze different types of input fields
            analysis = {
                "total_ui_length": len(ui_content),
                "edittext_fields": [],
                "textview_fields": [],
                "button_fields": [],
                "web_elements": [],
                "form_indicators": []
            }
            
            # Find EditText fields (native Android inputs)
            edittext_pattern = r'<node[^>]*class="android\.widget\.EditText"[^>]*>'
            edittext_matches = re.findall(edittext_pattern, ui_content)
            
            for i, match in enumerate(edittext_matches):
                field_info = self._parse_ui_element(match, i, "EditText")
                analysis["edittext_fields"].append(field_info)
            
            # Find WebView elements (for web-based forms)
            webview_pattern = r'<node[^>]*class="android\.webkit\.WebView"[^>]*>'
            webview_matches = re.findall(webview_pattern, ui_content)
            
            for i, match in enumerate(webview_matches):
                field_info = self._parse_ui_element(match, i, "WebView")
                analysis["web_elements"].append(field_info)
            
            # Find buttons
            button_patterns = [
                r'<node[^>]*class="android\.widget\.Button"[^>]*>',
                r'<node[^>]*clickable="true"[^>]*text="[^"]*"[^>]*>'
            ]
            
            for pattern in button_patterns:
                button_matches = re.findall(pattern, ui_content)
                for i, match in enumerate(button_matches):
                    field_info = self._parse_ui_element(match, i, "Button")
                    analysis["button_fields"].append(field_info)
            
            # Look for form indicators
            form_keywords = [
                "first name", "last name", "username", "password", 
                "create account", "sign up", "google account",
                "enter your name", "choose username"
            ]
            
            for keyword in form_keywords:
                if keyword.lower() in ui_content.lower():
                    analysis["form_indicators"].append(keyword)
            
            # Check if we're in a browser or native app
            if "chrome" in ui_content.lower() or "browser" in ui_content.lower():
                analysis["context"] = "browser"
            elif "webview" in ui_content.lower():
                analysis["context"] = "webview"
            else:
                analysis["context"] = "native"
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}
    
    def _parse_ui_element(self, element_xml: str, index: int, element_type: str) -> Dict:
        """Parse UI element XML to extract useful information"""
        info = {
            "index": index,
            "type": element_type,
            "bounds": None,
            "text": "",
            "content_desc": "",
            "resource_id": "",
            "clickable": False,
            "enabled": True,
            "raw": element_xml
        }
        
        # Extract bounds
        bounds_match = re.search(r'bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', element_xml)
        if bounds_match:
            x1, y1, x2, y2 = bounds_match.groups()
            info["bounds"] = {
                "x1": int(x1), "y1": int(y1), 
                "x2": int(x2), "y2": int(y2),
                "center_x": (int(x1) + int(x2)) // 2,
                "center_y": (int(y1) + int(y2)) // 2,
                "width": int(x2) - int(x1),
                "height": int(y2) - int(y1)
            }
        
        # Extract text
        text_match = re.search(r'text="([^"]*)"', element_xml)
        if text_match:
            info["text"] = text_match.group(1)
        
        # Extract content description
        desc_match = re.search(r'content-desc="([^"]*)"', element_xml)
        if desc_match:
            info["content_desc"] = desc_match.group(1)
        
        # Extract resource ID
        resource_match = re.search(r'resource-id="([^"]*)"', element_xml)
        if resource_match:
            info["resource_id"] = resource_match.group(1)
        
        # Check if clickable
        info["clickable"] = 'clickable="true"' in element_xml
        
        # Check if enabled
        info["enabled"] = 'enabled="false"' not in element_xml
        
        return info
    
    def test_input_strategies(self, device_id: str, field_info: Dict) -> Dict:
        """Test different input strategies for a field"""
        print(f"⌨️ Testing input strategies for {field_info['type']} field {field_info['index']}...")
        
        results = {
            "field_info": field_info,
            "tap_test": False,
            "input_test": False,
            "clear_test": False,
            "strategies_tested": []
        }
        
        if not field_info.get("bounds"):
            results["error"] = "No bounds information"
            return results
        
        bounds = field_info["bounds"]
        center_x = bounds["center_x"]
        center_y = bounds["center_y"]
        
        try:
            # Strategy 1: Simple tap and input
            print(f"  📍 Strategy 1: Tap at ({center_x}, {center_y})")
            tap_result = subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', 
                                      shell=True, timeout=5)
            results["tap_test"] = tap_result.returncode == 0
            results["strategies_tested"].append("simple_tap")
            
            if results["tap_test"]:
                time.sleep(1)
                
                # Clear field first
                clear_result = subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_CTRL_A', 
                                            shell=True, timeout=5)
                subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_DEL', 
                             shell=True, timeout=5)
                results["clear_test"] = clear_result.returncode == 0
                results["strategies_tested"].append("clear_field")
                
                time.sleep(1)
                
                # Input test text
                test_text = f"TestInput{field_info['index']}"
                input_result = subprocess.run(f'adb -s {device_id} shell input text "{test_text}"', 
                                            shell=True, timeout=5)
                results["input_test"] = input_result.returncode == 0
                results["strategies_tested"].append("text_input")
                
                time.sleep(1)
            
            # Strategy 2: Focus and input (if simple tap failed)
            if not results["input_test"]:
                print(f"  📍 Strategy 2: Focus field and input")
                # Try to focus the field using different methods
                subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', shell=True, timeout=5)
                time.sleep(0.5)
                subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', shell=True, timeout=5)
                time.sleep(1)
                
                # Try input again
                test_text2 = f"Focus{field_info['index']}"
                input_result2 = subprocess.run(f'adb -s {device_id} shell input text "{test_text2}"', 
                                             shell=True, timeout=5)
                if input_result2.returncode == 0:
                    results["input_test"] = True
                    results["strategies_tested"].append("double_tap_input")
            
            return results
            
        except Exception as e:
            results["error"] = str(e)
            return results
    
    def run_gmail_form_debug(self) -> Dict:
        """Run comprehensive Gmail form debugging"""
        print("🐛 Gmail Form Debugging Session")
        print("=" * 50)
        
        # Find running device
        print("🔍 Finding running device...")
        instances = self.genymotion.get_available_instances()
        
        running_device = None
        device_id = None
        
        for name, info in instances.items():
            if info.get('status') == 'running':
                running_device = name
                device_id = self.genymotion.get_device_adb_id(name)
                print(f"✅ Found running device: {name} ({device_id})")
                break
        
        if not running_device:
            return {"error": "No running device found"}
        
        # Fix ADB connection
        if not self.fix_adb_connection(device_id):
            return {"error": "Could not establish ADB connection"}
        
        # Get current activity
        current_activity = self.get_current_activity(device_id)
        print(f"📱 Current activity: {current_activity}")
        
        # Take screenshot for reference
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_debug_screenshot.png', 
                         shell=True, timeout=10)
            print("📸 Screenshot saved: /tmp/gmail_debug_screenshot.png")
        except:
            pass
        
        # Analyze form fields
        form_analysis = self.analyze_gmail_form_fields(device_id)
        
        if "error" in form_analysis:
            return {"error": f"Form analysis failed: {form_analysis['error']}"}
        
        print(f"\n📊 Form Analysis Results:")
        print(f"  Context: {form_analysis.get('context', 'unknown')}")
        print(f"  EditText fields: {len(form_analysis['edittext_fields'])}")
        print(f"  WebView elements: {len(form_analysis['web_elements'])}")
        print(f"  Button fields: {len(form_analysis['button_fields'])}")
        print(f"  Form indicators: {form_analysis['form_indicators']}")
        
        # Test input strategies on first few fields
        input_test_results = []
        
        # Test EditText fields first
        for field in form_analysis['edittext_fields'][:3]:
            print(f"\n🧪 Testing EditText field {field['index']}:")
            print(f"  Text: '{field['text']}'")
            print(f"  Content-desc: '{field['content_desc']}'")
            print(f"  Resource-id: '{field['resource_id']}'")
            
            if field['bounds']:
                bounds = field['bounds']
                print(f"  Bounds: ({bounds['x1']},{bounds['y1']}) to ({bounds['x2']},{bounds['y2']})")
                print(f"  Size: {bounds['width']}x{bounds['height']}")
                
                test_result = self.test_input_strategies(device_id, field)
                input_test_results.append(test_result)
                
                print(f"  Results: tap={test_result['tap_test']}, input={test_result['input_test']}")
        
        return {
            "device_name": running_device,
            "device_id": device_id,
            "current_activity": current_activity,
            "form_analysis": form_analysis,
            "input_test_results": input_test_results
        }


def main():
    """Main debugging session"""
    print("🐛 Gmail Form Issues Debugging")
    print("=" * 50)
    
    debugger = GmailFormDebugger()
    results = debugger.run_gmail_form_debug()
    
    if "error" in results:
        print(f"❌ Debug session failed: {results['error']}")
        return 1
    
    print(f"\n📋 Debug Session Summary:")
    print(f"  Device: {results['device_name']} ({results['device_id']})")
    print(f"  Activity: {results['current_activity']}")
    
    form_analysis = results['form_analysis']
    print(f"  Form context: {form_analysis.get('context')}")
    print(f"  Input fields found: {len(form_analysis['edittext_fields'])}")
    print(f"  Form indicators: {len(form_analysis['form_indicators'])}")
    
    # Summarize input test results
    successful_inputs = 0
    total_tests = len(results['input_test_results'])
    
    for test in results['input_test_results']:
        if test.get('input_test'):
            successful_inputs += 1
    
    print(f"  Input success rate: {successful_inputs}/{total_tests}")
    
    if successful_inputs == 0 and total_tests > 0:
        print("\n⚠️ ISSUE IDENTIFIED: No input fields are accepting text input")
        print("   Possible causes:")
        print("   1. Fields are in a WebView (need different input method)")
        print("   2. Fields are not properly focused")
        print("   3. Keyboard is not appearing")
        print("   4. Fields are disabled or read-only")
        
        # Check if we're in a web context
        if form_analysis.get('context') == 'browser' or form_analysis.get('context') == 'webview':
            print("   5. Web form requires JavaScript interaction")
    
    print(f"\n✅ Debug session complete!")
    print(f"📸 Screenshot available at: /tmp/gmail_debug_screenshot.png")
    
    return 0


if __name__ == "__main__":
    exit(main())
