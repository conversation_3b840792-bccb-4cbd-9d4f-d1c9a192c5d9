#!/usr/bin/env python3
"""
Debug Screenshot functionality
"""

import sys
import os
import subprocess
import time

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


def debug_screenshot():
    """Debug screenshot functionality"""
    print("🔍 Debug Screenshot Functionality")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    
    # Find running device
    instances = genymotion.get_available_instances()
    
    device_id = None
    for name, info in instances.items():
        if info.get('status') == 'running':
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not device_id:
        print("❌ No running device found")
        return False
    
    # Test ADB connection
    print(f"\n🔗 Testing ADB connection to {device_id}...")
    test_cmd = f'adb -s {device_id} shell echo "ADB connection test"'
    result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True, timeout=10)
    
    if result.returncode == 0:
        print(f"✅ ADB connection working: {result.stdout.strip()}")
    else:
        print(f"❌ ADB connection failed: {result.stderr}")
        return False
    
    # Create screenshots directory
    os.makedirs("screenshots", exist_ok=True)
    
    # Test Method 1: Direct exec-out
    print("\n📸 Method 1: Direct exec-out...")
    screenshot_path_1 = f"screenshots/debug_method1_{int(time.time())}.png"
    cmd1 = f'adb -s {device_id} exec-out screencap -p > {screenshot_path_1}'
    result1 = subprocess.run(cmd1, shell=True, capture_output=True, text=True, timeout=10)
    
    print(f"   Command: {cmd1}")
    print(f"   Return code: {result1.returncode}")
    print(f"   Stderr: {result1.stderr}")
    
    if os.path.exists(screenshot_path_1):
        size1 = os.path.getsize(screenshot_path_1)
        print(f"   ✅ File created, size: {size1} bytes")
    else:
        print(f"   ❌ File not created")
    
    # Test Method 2: Shell + pull
    print("\n📸 Method 2: Shell screencap + pull...")
    screenshot_path_2 = f"screenshots/debug_method2_{int(time.time())}.png"
    
    # Step 1: Save to device
    cmd2a = f'adb -s {device_id} shell screencap -p /sdcard/debug_screenshot.png'
    result2a = subprocess.run(cmd2a, shell=True, capture_output=True, text=True, timeout=10)
    print(f"   Step 1 - Save to device:")
    print(f"   Command: {cmd2a}")
    print(f"   Return code: {result2a.returncode}")
    print(f"   Stderr: {result2a.stderr}")
    
    # Step 2: Pull from device
    cmd2b = f'adb -s {device_id} pull /sdcard/debug_screenshot.png {screenshot_path_2}'
    result2b = subprocess.run(cmd2b, shell=True, capture_output=True, text=True, timeout=10)
    print(f"   Step 2 - Pull from device:")
    print(f"   Command: {cmd2b}")
    print(f"   Return code: {result2b.returncode}")
    print(f"   Stdout: {result2b.stdout}")
    print(f"   Stderr: {result2b.stderr}")
    
    if os.path.exists(screenshot_path_2):
        size2 = os.path.getsize(screenshot_path_2)
        print(f"   ✅ File created, size: {size2} bytes")
    else:
        print(f"   ❌ File not created")
    
    # Test Method 3: Using existing _take_screenshot method
    print("\n📸 Method 3: Using existing _take_screenshot method...")
    from gmail_account_creator import GmailAccountCreator
    gmail_creator = GmailAccountCreator()
    
    try:
        gmail_creator._take_screenshot(device_id, "debug_method3")
        print("   ✅ _take_screenshot method completed")
        
        # Check if file was created
        import glob
        debug_files = glob.glob("screenshots/*debug_method3*")
        if debug_files:
            latest_file = max(debug_files, key=os.path.getctime)
            size3 = os.path.getsize(latest_file)
            print(f"   ✅ File created: {latest_file}, size: {size3} bytes")
        else:
            print("   ❌ No debug_method3 file found")
            
    except Exception as e:
        print(f"   ❌ _take_screenshot method failed: {e}")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Summary:")
    
    methods = [
        ("Method 1 (exec-out)", screenshot_path_1),
        ("Method 2 (shell+pull)", screenshot_path_2)
    ]
    
    for method_name, path in methods:
        if os.path.exists(path):
            size = os.path.getsize(path)
            status = "✅ SUCCESS" if size > 0 else "⚠️ EMPTY FILE"
            print(f"   {method_name}: {status} ({size} bytes)")
        else:
            print(f"   {method_name}: ❌ FAILED (file not created)")
    
    return True


if __name__ == "__main__":
    debug_screenshot()
