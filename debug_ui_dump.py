#!/usr/bin/env python3
"""
Debug UI Dump
Get and analyze the current UI dump from the device
"""

import sys
import os
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


def debug_ui_dump():
    """Get and analyze the current UI dump"""
    print("🔍 Debug UI Dump")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    
    # Find running device
    instances = genymotion.get_available_instances()
    
    device_id = None
    for name, info in instances.items():
        if info.get('status') == 'running':
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not device_id:
        print("❌ No running device found")
        return
    
    # Get UI dump
    print("\n📱 Getting UI dump...")
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
        
        if result.returncode == 0:
            ui_content = result.stdout
            print(f"✅ UI dump retrieved ({len(ui_content)} characters)")
            
            # Save to file for inspection
            with open('/tmp/ui_dump_debug.xml', 'w') as f:
                f.write(ui_content)
            print("💾 Saved to: /tmp/ui_dump_debug.xml")
            
            # Look for key terms
            ui_lower = ui_content.lower()
            
            print("\n🔍 Searching for key terms...")
            
            key_terms = [
                'create account', 'create', 'account', 'sign up', 'signup',
                'first name', 'last name', 'username', 'choose username',
                'for my personal use', 'personal use', 'sign in', 'email or phone'
            ]
            
            found_terms = []
            for term in key_terms:
                if term in ui_lower:
                    found_terms.append(term)
                    # Find context around the term
                    index = ui_lower.find(term)
                    start = max(0, index - 50)
                    end = min(len(ui_content), index + len(term) + 50)
                    context = ui_content[start:end].replace('\n', ' ').replace('\r', ' ')
                    print(f"   ✅ Found '{term}': ...{context}...")
            
            if not found_terms:
                print("   ❌ No key terms found")
            
            print(f"\n📊 Found terms: {found_terms}")
            
            # Look for clickable elements
            print("\n🔘 Looking for clickable elements...")
            clickable_elements = []
            lines = ui_content.split('\n')
            for line in lines:
                if 'clickable="true"' in line and 'text=' in line:
                    # Extract text content
                    start = line.find('text="') + 6
                    if start > 5:
                        end = line.find('"', start)
                        if end > start:
                            text = line[start:end]
                            if text.strip():
                                clickable_elements.append(text)
            
            print(f"   Found {len(clickable_elements)} clickable elements:")
            for element in clickable_elements[:10]:  # Show first 10
                print(f"      - '{element}'")
            
            if len(clickable_elements) > 10:
                print(f"      ... and {len(clickable_elements) - 10} more")
                
        else:
            print("❌ Failed to get UI dump")
            print(f"Error: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Exception getting UI dump: {e}")


if __name__ == "__main__":
    debug_ui_dump()
