#!/usr/bin/env python3
"""
Genymotion Installation and Configuration Diagnostic Tool

This script diagnoses common issues with Genymotion setup and provides
solutions for device creation failures.
"""

import subprocess
import sys
import os
from pathlib import Path
from loguru import logger


def check_genymotion_installation():
    """Check if Genymotion Desktop is installed"""
    logger.info("🔍 Checking Genymotion Desktop installation...")
    
    # Common Genymotion installation paths
    genymotion_paths = [
        "/Applications/Genymotion.app/Contents/MacOS/genymotion",
        "/Applications/Genymotion.app/Contents/MacOS/gmtool",
        "/usr/local/bin/gmtool",
        "/opt/genymotion/gmtool",
        "gmtool"  # In PATH
    ]
    
    found_paths = []
    for path in genymotion_paths:
        if os.path.exists(path) or (path == "gmtool" and check_command_exists("gmtool")):
            found_paths.append(path)
            logger.info(f"✅ Found: {path}")
    
    if not found_paths:
        logger.error("❌ Genymotion Desktop not found!")
        logger.info("💡 Install Genymotion Desktop from: https://www.genymotion.com/")
        return False
    
    return True


def check_command_exists(command):
    """Check if a command exists in PATH"""
    try:
        subprocess.run([command, "--version"], capture_output=True, timeout=5)
        return True
    except:
        return False


def check_genymotion_running():
    """Check if Genymotion Desktop is running"""
    logger.info("🔍 Checking if Genymotion Desktop is running...")
    
    try:
        # Check if Genymotion process is running
        result = subprocess.run(["pgrep", "-f", "Genymotion"], capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Genymotion Desktop is running")
            return True
        else:
            logger.warning("⚠️ Genymotion Desktop is not running")
            logger.info("💡 Please start Genymotion Desktop from Applications folder")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error checking Genymotion process: {e}")
        return False


def test_gmtool_commands():
    """Test basic gmtool commands"""
    logger.info("🔍 Testing gmtool commands...")
    
    commands_to_test = [
        (["gmtool", "version"], "Version check"),
        (["gmtool", "admin", "list"], "List instances"),
        (["gmtool", "admin", "hwprofiles"], "Hardware profiles"),
        (["gmtool", "admin", "osimages"], "OS images")
    ]
    
    results = {}
    
    for cmd, description in commands_to_test:
        try:
            logger.info(f"Testing: {description}")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                logger.info(f"✅ {description}: Success")
                results[description] = {"success": True, "output": result.stdout}
            else:
                logger.error(f"❌ {description}: Failed")
                logger.error(f"Error: {result.stderr}")
                results[description] = {"success": False, "error": result.stderr}
                
        except subprocess.TimeoutExpired:
            logger.error(f"❌ {description}: Timeout")
            results[description] = {"success": False, "error": "Timeout"}
        except Exception as e:
            logger.error(f"❌ {description}: Exception - {e}")
            results[description] = {"success": False, "error": str(e)}
    
    return results


def check_system_resources():
    """Check system resources for virtualization"""
    logger.info("🔍 Checking system resources...")
    
    try:
        import psutil
        
        # Check memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        
        logger.info(f"💾 Total Memory: {memory_gb:.1f}GB")
        logger.info(f"💾 Available Memory: {available_gb:.1f}GB")
        
        if memory_gb >= 8:
            logger.info("✅ Excellent memory for virtual devices")
        elif memory_gb >= 4:
            logger.info("✅ Sufficient memory for virtual devices")
        else:
            logger.warning("⚠️ Low memory - may affect performance")
        
        # Check CPU
        cpu_count = psutil.cpu_count()
        logger.info(f"🖥️ CPU Cores: {cpu_count}")
        
        if cpu_count >= 4:
            logger.info("✅ Good CPU for virtualization")
        elif cpu_count >= 2:
            logger.info("✅ Adequate CPU for virtualization")
        else:
            logger.warning("⚠️ Limited CPU - may affect performance")
        
        # Check disk space
        disk = psutil.disk_usage('/')
        disk_free_gb = disk.free / (1024**3)
        logger.info(f"💽 Free Disk Space: {disk_free_gb:.1f}GB")
        
        if disk_free_gb >= 20:
            logger.info("✅ Sufficient disk space")
        elif disk_free_gb >= 10:
            logger.info("✅ Adequate disk space")
        else:
            logger.warning("⚠️ Low disk space - may cause issues")
        
        return True
        
    except ImportError:
        logger.warning("⚠️ psutil not available, skipping resource check")
        return True
    except Exception as e:
        logger.error(f"❌ Error checking system resources: {e}")
        return False


def check_virtualization_support():
    """Check if virtualization is supported and enabled"""
    logger.info("🔍 Checking virtualization support...")
    
    try:
        # Check if we're on macOS
        result = subprocess.run(["uname", "-s"], capture_output=True, text=True)
        if "Darwin" in result.stdout:
            logger.info("✅ Running on macOS - virtualization supported")
            
            # Check if VT-x is available (Intel Macs)
            try:
                result = subprocess.run(["sysctl", "-n", "machdep.cpu.features"], 
                                      capture_output=True, text=True)
                if "VMX" in result.stdout:
                    logger.info("✅ Intel VT-x virtualization available")
                else:
                    # Check for Apple Silicon
                    result = subprocess.run(["uname", "-m"], capture_output=True, text=True)
                    if "arm64" in result.stdout:
                        logger.info("✅ Apple Silicon - virtualization supported")
                    else:
                        logger.warning("⚠️ Virtualization support unclear")
            except:
                logger.info("ℹ️ Could not determine specific virtualization features")
            
            return True
        else:
            logger.info("ℹ️ Not running on macOS")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error checking virtualization: {e}")
        return False


def provide_solutions():
    """Provide solutions for common issues"""
    logger.info("\n" + "=" * 60)
    logger.info("🔧 SOLUTIONS FOR COMMON ISSUES")
    logger.info("=" * 60)
    
    solutions = [
        {
            "issue": "Genymotion Desktop not found",
            "solutions": [
                "Download and install Genymotion Desktop from https://www.genymotion.com/",
                "Make sure to install the complete Genymotion Desktop (not just the plugin)",
                "Verify installation by opening Genymotion from Applications folder"
            ]
        },
        {
            "issue": "Genymotion Desktop not running",
            "solutions": [
                "Open Genymotion Desktop from Applications folder",
                "Sign in with your Genymotion account",
                "Wait for the application to fully load before running automation"
            ]
        },
        {
            "issue": "Device creation timeout",
            "solutions": [
                "Ensure Genymotion Desktop is running and signed in",
                "Check that you have sufficient system resources (4GB+ RAM)",
                "Try creating a device manually first via Genymotion Desktop",
                "Increase timeout values in the automation code",
                "Close other resource-intensive applications"
            ]
        },
        {
            "issue": "gmtool command not found",
            "solutions": [
                "Add Genymotion to your PATH: export PATH=$PATH:/Applications/Genymotion.app/Contents/MacOS",
                "Use the full path to gmtool in your scripts",
                "Reinstall Genymotion Desktop if the issue persists"
            ]
        },
        {
            "issue": "Low system resources",
            "solutions": [
                "Close unnecessary applications to free up RAM",
                "Allocate less RAM to virtual devices (2GB instead of 4GB)",
                "Use lighter Android versions (Android 11 instead of 14)",
                "Consider upgrading your system RAM"
            ]
        }
    ]
    
    for solution in solutions:
        logger.info(f"\n🔴 {solution['issue']}:")
        for i, sol in enumerate(solution['solutions'], 1):
            logger.info(f"   {i}. {sol}")


def main():
    """Run complete diagnostic"""
    logger.info("=" * 60)
    logger.info("🔧 GENYMOTION DIAGNOSTIC TOOL")
    logger.info("=" * 60)
    
    checks = [
        ("Genymotion Installation", check_genymotion_installation),
        ("Genymotion Running", check_genymotion_running),
        ("System Resources", check_system_resources),
        ("Virtualization Support", check_virtualization_support)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        logger.info(f"\n🧪 {check_name}:")
        try:
            results[check_name] = check_func()
        except Exception as e:
            logger.error(f"❌ {check_name} failed: {e}")
            results[check_name] = False
    
    # Test gmtool commands
    logger.info(f"\n🧪 GMTool Commands:")
    gmtool_results = test_gmtool_commands()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📊 DIAGNOSTIC SUMMARY")
    logger.info("=" * 60)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    logger.info(f"Basic Checks: {passed}/{total} passed")
    
    gmtool_passed = sum(1 for result in gmtool_results.values() if result.get('success', False))
    gmtool_total = len(gmtool_results)
    
    logger.info(f"GMTool Commands: {gmtool_passed}/{gmtool_total} passed")
    
    if passed == total and gmtool_passed == gmtool_total:
        logger.info("🎉 All checks passed! Genymotion should work correctly.")
    elif passed >= total - 1 and gmtool_passed >= gmtool_total - 1:
        logger.warning("⚠️ Most checks passed - minor issues may exist")
    else:
        logger.error("❌ Multiple issues detected - see solutions below")
    
    # Provide solutions
    provide_solutions()


if __name__ == "__main__":
    main()
