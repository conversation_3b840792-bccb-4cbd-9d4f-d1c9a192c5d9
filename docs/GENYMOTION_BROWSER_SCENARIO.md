# Complete Genymotion Browser Automation Scenario

This guide provides a comprehensive walkthrough for creating a new Genymotion virtual device and automating browser interactions to open Google.com.

## 📋 Prerequisites

### Required Software
- **Genymotion Desktop** (Free or Business edition)
- **Python 3.8+** with required packages
- **ADB (Android Debug Bridge)** - included with Android SDK
- **Appium Server** for mobile automation

### Installation Steps
1. Download and install Genymotion Desktop from https://www.genymotion.com/
2. Install Python dependencies:
   ```bash
   pip install appium-python-client selenium loguru pyyaml
   ```
3. Ensure ADB is in your system PATH

## 🎯 Scenario Overview

This scenario demonstrates:
1. **Device Creation**: Creating a Google Pixel 6 virtual device
2. **Device Configuration**: Setting up screen resolution, Android version, etc.
3. **Device Startup**: Starting the virtual device and waiting for boot completion
4. **ADB Connection**: Establishing ADB connection for automation
5. **Browser Automation**: Opening Chrome and navigating to Google.com
6. **User Interaction**: Performing a search query
7. **Cleanup**: Proper resource management

## 🚀 Step-by-Step Execution

### Step 1: Check Genymotion Installation

```python
# Verify Genymotion Desktop is installed and accessible
result = genymotion_manager._execute_gmtool(['version'])
```

**Expected Output:**
```
Genymotion Desktop version and revision information
```

### Step 2: Create Virtual Device Instance

**Device Configuration:**
- **Hardware Profile**: HTC One
- **Android Version**: 14.0
- **Screen Resolution**: 1080x1920
- **DPI**: 441
- **Instance Name**: GoogleTestDevice

**Command Equivalent:**
```bash
gmtool admin create "HTC One" "Android 14.0" "GoogleTestDevice" \
  --width 1080 --height 1920 --density 441 \
  --sysprop MANUFACTURER:HTC \
  --sysprop MODEL:HTC\ One \
  --sysprop DEVICE:HTC\ One
```

### Step 3: Start the Instance

```python
success = genymotion_manager.start_instance("GoogleTestDevice")
```

**Command Equivalent:**
```bash
gmtool admin start "GoogleTestDevice"
```

**Expected Behavior:**
- Virtual device window opens
- Android boot sequence begins
- ADB connection becomes available

### Step 4: Wait for Device Ready

The script waits for:
- ADB device status: `device` (not `offline`)
- Android boot completion: `sys.boot_completed = 1`

**Verification Commands:**
```bash
adb devices
adb shell getprop sys.boot_completed
```

### Step 5: Start Appium Server

```python
capabilities = {
    "platformName": "Android",
    "platformVersion": "14",
    "deviceName": "HTC One",
    "udid": device_id,
    "appPackage": "com.android.browser",
    "automationName": "UiAutomator2"
}
```

### Step 6: Open Default Android Browser

**Method 1: Using ADB Intent (Default Browser)**
```bash
adb shell am start -a android.intent.action.VIEW \
  -d "https://www.google.com"
```

**Method 2: Using Specific Browser Package**
```bash
adb shell am start -a android.intent.action.VIEW \
  -d "https://www.google.com" com.android.browser
```

**Method 3: Using Appium (Alternative)**
```python
driver.start_activity("com.android.browser", ".BrowserActivity")
driver.get("https://www.google.com")
```

### Step 7: Perform Google Search

**Search Interaction Steps:**
1. Tap on Google search box (coordinates: 540, 350 - adjusted for HTC One)
2. Input search text: "Genymotion Android automation"
3. Press Enter key to execute search

**ADB Commands:**
```bash
adb shell input tap 540 350
adb shell input text "Genymotion Android automation"
adb shell input keyevent KEYCODE_ENTER
```

## 📱 Supported Device Templates

### Google Devices
- Nexus 4, 5, 5X, 6, 6P, 7, 9, 10
- Pixel, Pixel 2/XL, Pixel 3/3a/XL, Pixel 5/5a
- Pixel 6/6a/Pro, Pixel 7/7a/Pro, Pixel 8/Pro, Pixel 9
- Pixel C, Pixel XL

### Samsung Devices
- Galaxy S3, S4, S5, S6, S7, S8, S9, S10, S23, S24
- Galaxy A10, A14, A50
- Galaxy Note 2, 3, 10+

### Other Manufacturers
- Amazon Fire 7, HD 8, HD 10
- HTC One
- Huawei P30 Pro
- Motorola Moto X
- Sony Xperia Tablet Z
- Xiaomi 15, Redmi Note 7, Redmi Note 9
- Custom Phone/Tablet templates

## 🔧 Advanced Configuration Options

### Hardware Profile Customization
```bash
gmtool admin edit "GoogleTestDevice" \
  --width 1440 --height 2560 --density 560 \
  --nbcpu 4 --ram 4096 \
  --virtualkeyboard on --navbar on
```

### System Properties
```bash
--sysprop MODEL:Custom_Model
--sysprop MANUFACTURER:Custom_Manufacturer
--sysprop DEVICE:Custom_Device
--sysprop BRAND:Custom_Brand
```

### Network Configuration
```bash
--network-mode nat  # or bridge
--bridged-if eth0   # when using bridge mode
```

## 🌐 Browser Automation Features

### Available Actions
- **Navigation**: Open URLs, back/forward navigation
- **Element Interaction**: Tap, swipe, long press
- **Text Input**: Type text, clear fields
- **Screenshots**: Capture screen states
- **Scroll Actions**: Vertical/horizontal scrolling

### Chrome-Specific Features
- **Incognito Mode**: Private browsing
- **Developer Tools**: Remote debugging
- **Extensions**: Install and manage extensions
- **Bookmarks**: Manage bookmarks programmatically

## 🛠 Troubleshooting

### Common Issues

**1. Instance Creation Fails**
```
Error: Virtual device creation timed out
```
**Solution**: Ensure sufficient system resources and try creating manually via Genymotion Desktop.

**2. ADB Connection Issues**
```
Error: device offline
```
**Solution**: Restart ADB server and reconnect:
```bash
adb kill-server
adb start-server
adb connect localhost:5555
```

**3. Chrome Not Opening**
```
Error: Activity not found
```
**Solution**: Ensure Chrome is installed on the virtual device:
```bash
adb shell pm list packages | grep chrome
```

### Performance Optimization

**1. Allocate Sufficient Resources**
- RAM: Minimum 2GB, recommended 4GB+
- CPU: 2+ cores for better performance
- Storage: 8GB+ available space

**2. Enable Hardware Acceleration**
- Use VT-x/AMD-V virtualization
- Enable GPU acceleration in Genymotion settings

**3. Network Configuration**
- Use NAT mode for better compatibility
- Configure proxy settings if needed

## 📊 Expected Results

### Successful Execution Indicators
- ✅ Genymotion instance created and started
- ✅ ADB connection established
- ✅ Chrome browser opens with Google.com
- ✅ Search query executed successfully
- ✅ Search results displayed

### Performance Metrics
- **Instance Creation**: 30-60 seconds
- **Boot Time**: 45-90 seconds
- **Browser Launch**: 5-10 seconds
- **Page Load**: 3-5 seconds
- **Search Execution**: 2-3 seconds

## 🧹 Cleanup and Resource Management

### Automatic Cleanup
```python
# Stop Appium server
appium_manager.stop_server()

# Optionally stop instance
genymotion_manager.stop_instance("GoogleTestDevice")
```

### Manual Cleanup
```bash
# Stop instance
gmtool admin stop "GoogleTestDevice"

# Delete instance (if no longer needed)
gmtool admin delete "GoogleTestDevice"
```

## 🔄 Running the Scenario

### Execute the Complete Scenario
```bash
cd examples
python3 genymotion_browser_automation_scenario.py
```

### Expected Output
```
🚀 Starting Genymotion Browser Automation Scenario
📋 Checking Genymotion installation...
✅ Genymotion Desktop found and accessible
📱 Creating Genymotion instance: GoogleTestDevice
✅ Instance 'GoogleTestDevice' created successfully
▶️ Starting instance: GoogleTestDevice
✅ Instance 'GoogleTestDevice' started successfully
📱 Device ID: 192.168.56.101:5555
⏳ Waiting for device to be ready...
✅ Device is ready and connected via ADB
✅ Android system fully booted
🔧 Starting Appium server...
✅ Appium server started successfully
🤖 Initializing automation client...
✅ Automation client initialized
🌐 Opening Chrome browser and navigating to Google.com...
✅ Chrome browser opened with Google.com
🔍 Performing Google search...
✅ Search performed: 'Genymotion Android automation'
🧹 Cleaning up...
✅ Appium server stopped
✅ Cleanup completed
🎉 Scenario completed successfully!
```

This comprehensive scenario demonstrates the full capabilities of Genymotion automation integration with our system.
