# Genymotion Automation Examples

This directory contains practical examples demonstrating how to use the Genymotion automation system.

## 📁 Available Examples

### 🌐 Browser Automation Scenario
**File**: `genymotion_browser_automation_scenario.py`

A comprehensive example that demonstrates:
- Creating a new Genymotion virtual device
- Starting and configuring the device
- Opening Chrome browser
- Navigating to Google.com
- Performing a search query
- Proper cleanup and resource management

**Usage:**
```bash
cd examples
python3 genymotion_browser_automation_scenario.py
```

## 🚀 Quick Start Runner

**File**: `../run_genymotion_scenario.py` (in project root)

A command-line tool for running the browser automation scenario with custom options.

**Basic Usage:**
```bash
python3 run_genymotion_scenario.py
```

**Advanced Usage:**
```bash
# Custom device and search query
python3 run_genymotion_scenario.py \
  --device-name "MyTestDevice" \
  --device-model "Samsung Galaxy S10" \
  --android-version "12" \
  --search-query "Python automation testing" \
  --keep-running

# Verbose logging
python3 run_genymotion_scenario.py --verbose
```

**Available Options:**
- `--device-name`: Custom name for the virtual device
- `--android-version`: Android version (11, 12, 13, 14)
- `--device-model`: Device model from supported list
- `--search-query`: Custom Google search query
- `--keep-running`: Keep instance running after completion
- `--verbose`: Enable detailed logging

## 📱 Supported Device Models

### Google Devices
- Google Pixel 6, 7, 8
- Google Pixel series (all variants)
- Nexus series

### Samsung Devices
- Galaxy S10, S23, S24
- Galaxy A series
- Galaxy Note series

### Generic Templates
- Custom Phone
- Custom Tablet

## 🔧 Prerequisites

Before running the examples, ensure you have:

1. **Genymotion Desktop** installed and accessible
2. **Python dependencies** installed:
   ```bash
   pip install appium-python-client selenium loguru pyyaml
   ```
3. **ADB** available in system PATH
4. **Sufficient system resources** (4GB+ RAM recommended)

## 📋 Example Workflow

1. **Check Installation**
   ```bash
   gmtool version  # Verify Genymotion is installed
   adb version     # Verify ADB is available
   ```

2. **Run Basic Scenario**
   ```bash
   python3 run_genymotion_scenario.py
   ```

3. **Monitor Progress**
   - Watch console output for status updates
   - Genymotion Desktop will show the virtual device
   - Chrome browser will open automatically

4. **Cleanup**
   - Appium server stops automatically
   - Virtual device can be kept running or stopped

## 🛠 Customization

### Creating Custom Scenarios

You can extend the base scenario class to create custom automation workflows:

```python
from examples.genymotion_browser_automation_scenario import GenymotionBrowserAutomationScenario

class MyCustomScenario(GenymotionBrowserAutomationScenario):
    def __init__(self):
        super().__init__()
        self.instance_name = "MyCustomDevice"
        # Custom configuration here
    
    def _perform_custom_actions(self):
        # Your custom automation logic
        pass
```

### Device Configuration

Customize device properties:

```python
device_config = {
    "hardware_profile": "Google Pixel 8",
    "android_version": "14",
    "screen_resolution": "1080x2400",
    "dpi": 428,
    "deviceManufacturer": "Google",
    "deviceModel": "Pixel 8"
}
```

## 🔍 Troubleshooting

### Common Issues

**1. Genymotion Not Found**
```
Error: Genymotion Desktop not found or not accessible
```
**Solution**: Install Genymotion Desktop and ensure it's in the default location.

**2. Instance Creation Timeout**
```
Error: Gmtool command timed out
```
**Solution**: Ensure sufficient system resources and try creating the instance manually first.

**3. ADB Connection Issues**
```
Error: device offline
```
**Solution**: Restart ADB and check device status:
```bash
adb kill-server
adb start-server
adb devices
```

**4. Chrome Not Opening**
```
Error: Activity not found
```
**Solution**: Ensure Chrome is installed on the virtual device or install it manually.

### Performance Tips

1. **Allocate More Resources**
   - Increase RAM allocation in Genymotion settings
   - Use multiple CPU cores
   - Enable hardware acceleration

2. **Optimize Network**
   - Use NAT network mode for better compatibility
   - Configure proxy settings if behind corporate firewall

3. **Monitor Resource Usage**
   - Close unnecessary applications
   - Monitor CPU and memory usage
   - Use SSD storage for better performance

## 📚 Additional Resources

- [Genymotion Documentation](https://docs.genymotion.com/)
- [GMTool Command Reference](https://docs.genymotion.com/desktop/06_GMTool/)
- [Appium Documentation](https://appium.io/docs/)
- [Android ADB Reference](https://developer.android.com/studio/command-line/adb)

## 🤝 Contributing

To add new examples:

1. Create a new Python file in the `examples/` directory
2. Follow the existing code structure and naming conventions
3. Include comprehensive error handling and logging
4. Add documentation and usage examples
5. Test with multiple device configurations

## 📄 License

These examples are part of the Genymotion Automation System and follow the same license terms as the main project.
