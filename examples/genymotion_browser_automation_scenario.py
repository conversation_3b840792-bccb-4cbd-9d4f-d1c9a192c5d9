#!/usr/bin/env python3
"""
Complete Genymotion Automation Scenario: Create Instance and Open Google.com

This script demonstrates:
1. Creating a new Genymotion virtual device instance
2. Starting the instance
3. Connecting to it via ADB
4. Setting up Appium automation
5. Opening Chrome browser and navigating to Google.com
6. Performing basic interactions
7. Cleanup

Requirements:
- Genymotion Desktop installed
- Python dependencies: appium-python-client, selenium
- ADB (Android Debug Bridge) available in PATH
"""

import time
import subprocess
import sys
from typing import Optional, Dict, Any
from loguru import logger

# Import our automation components
sys.path.append('..')
from src.genymotion_manager import GenymotionManager
from src.appium_server import AppiumServerManager
from src.automation_client import AutomationClient


class GenymotionBrowserAutomationScenario:
    """Complete scenario for Genymotion device creation and browser automation"""
    
    def __init__(self):
        self.genymotion_manager = GenymotionManager()
        self.appium_manager = AppiumServerManager()
        self.automation_client = None
        self.instance_name = "GoogleTestDevice"
        self.device_id = None
        
        # Device configuration - using Google Pixel 6 as example
        self.device_config = {
            "hardware_profile": "Google Pixel 6",  # From supported devices list
            "android_version": "13",  # Android 13
            "device_name": "Google Pixel 6",
            "screen_resolution": "1080x2340",
            "dpi": 411,
            "deviceManufacturer": "Google",
            "deviceModel": "Pixel 6",
            "platformVersion": "13"
        }
    
    def run_complete_scenario(self) -> bool:
        """Run the complete automation scenario"""
        try:
            logger.info("🚀 Starting Genymotion Browser Automation Scenario")
            
            # Step 1: Check Genymotion installation
            if not self._check_genymotion_installation():
                return False
            
            # Step 2: Create new instance
            if not self._create_genymotion_instance():
                return False
            
            # Step 3: Start the instance
            if not self._start_instance():
                return False
            
            # Step 4: Wait for device to be ready
            if not self._wait_for_device_ready():
                return False
            
            # Step 5: Start Appium server
            if not self._start_appium_server():
                return False
            
            # Step 6: Initialize automation client
            if not self._initialize_automation():
                return False
            
            # Step 7: Open Chrome and navigate to Google
            if not self._open_google_in_browser():
                return False
            
            # Step 8: Perform search interaction
            if not self._perform_google_search():
                return False
            
            logger.info("✅ Scenario completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"❌ Scenario failed: {e}")
            return False
        finally:
            # Cleanup
            self._cleanup()
    
    def _check_genymotion_installation(self) -> bool:
        """Check if Genymotion is properly installed"""
        logger.info("📋 Checking Genymotion installation...")
        
        try:
            # Check if gmtool is available
            result = self.genymotion_manager._execute_gmtool(['version'])
            if result and result.returncode == 0:
                logger.info("✅ Genymotion Desktop found and accessible")
                return True
            else:
                logger.error("❌ Genymotion Desktop not found or not accessible")
                logger.error("Please install Genymotion Desktop from https://www.genymotion.com/")
                return False
        except Exception as e:
            logger.error(f"❌ Error checking Genymotion: {e}")
            return False
    
    def _create_genymotion_instance(self) -> bool:
        """Create a new Genymotion instance"""
        logger.info(f"📱 Creating Genymotion instance: {self.instance_name}")
        
        try:
            # Check if instance already exists
            instances = self.genymotion_manager.get_available_instances()
            if self.instance_name in instances:
                logger.info(f"Instance '{self.instance_name}' already exists, using existing instance")
                return True
            
            # Create new instance
            success = self.genymotion_manager.create_new_instance(
                self.instance_name, 
                self.device_config["android_version"]
            )
            
            if success:
                logger.info(f"✅ Instance '{self.instance_name}' created successfully")
                
                # Configure the instance with device profile
                profile_success = self.genymotion_manager._configure_instance_properties(
                    self.instance_name, 
                    self.device_config
                )
                
                if profile_success:
                    logger.info("✅ Instance configured with device profile")
                else:
                    logger.warning("⚠️ Instance created but configuration partially failed")
                
                return True
            else:
                logger.error(f"❌ Failed to create instance '{self.instance_name}'")
                logger.info("💡 You can create it manually using Genymotion Desktop:")
                logger.info("   1. Open Genymotion Desktop")
                logger.info("   2. Click '+' to add new virtual device")
                logger.info(f"   3. Choose '{self.device_config['hardware_profile']}' template")
                logger.info(f"   4. Name it '{self.instance_name}'")
                logger.info("   5. Select Android version and create")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error creating instance: {e}")
            return False
    
    def _start_instance(self) -> bool:
        """Start the Genymotion instance"""
        logger.info(f"▶️ Starting instance: {self.instance_name}")
        
        try:
            success = self.genymotion_manager.start_instance(self.instance_name)
            
            if success:
                logger.info(f"✅ Instance '{self.instance_name}' started successfully")
                
                # Get the actual device ID
                if self.genymotion_manager.actual_device_id:
                    self.device_id = self.genymotion_manager.actual_device_id
                    logger.info(f"📱 Device ID: {self.device_id}")
                
                return True
            else:
                logger.error(f"❌ Failed to start instance '{self.instance_name}'")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting instance: {e}")
            return False
    
    def _wait_for_device_ready(self) -> bool:
        """Wait for the device to be fully ready"""
        logger.info("⏳ Waiting for device to be ready...")
        
        max_wait = 60  # seconds
        wait_interval = 5
        
        for i in range(0, max_wait, wait_interval):
            try:
                # Check ADB connection
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                
                if self.device_id and f"{self.device_id}\tdevice" in result.stdout:
                    logger.info("✅ Device is ready and connected via ADB")
                    
                    # Additional check: ensure Android is fully booted
                    boot_check = subprocess.run(
                        ['adb', '-s', self.device_id, 'shell', 'getprop', 'sys.boot_completed'],
                        capture_output=True, text=True, timeout=10
                    )
                    
                    if boot_check.stdout.strip() == '1':
                        logger.info("✅ Android system fully booted")
                        return True
                    else:
                        logger.info("⏳ Android still booting...")
                
                time.sleep(wait_interval)
                logger.info(f"⏳ Waiting... ({i + wait_interval}/{max_wait}s)")
                
            except Exception as e:
                logger.debug(f"Device check failed: {e}")
                time.sleep(wait_interval)
        
        logger.warning("⚠️ Device may not be fully ready, but continuing...")
        return True
    
    def _start_appium_server(self) -> bool:
        """Start Appium server for automation"""
        logger.info("🔧 Starting Appium server...")
        
        try:
            # Generate capabilities for our device
            capabilities = self.appium_manager.generate_capabilities(
                device_profile=self.device_config,
                app_package="com.android.chrome"  # We'll use Chrome browser
            )
            
            # Start Appium server
            success = self.appium_manager.start_server()
            
            if success:
                logger.info("✅ Appium server started successfully")
                return True
            else:
                logger.error("❌ Failed to start Appium server")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error starting Appium server: {e}")
            return False
    
    def _initialize_automation(self) -> bool:
        """Initialize automation client"""
        logger.info("🤖 Initializing automation client...")
        
        try:
            # Create automation client
            self.automation_client = AutomationClient()
            
            # Set device profile
            self.automation_client.device_profile = self.device_config
            
            logger.info("✅ Automation client initialized")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error initializing automation: {e}")
            return False
    
    def _open_google_in_browser(self) -> bool:
        """Open Chrome browser and navigate to Google.com"""
        logger.info("🌐 Opening Chrome browser and navigating to Google.com...")
        
        try:
            # Start Chrome browser
            if self.device_id:
                # Open Chrome using ADB
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'am', 'start',
                    '-a', 'android.intent.action.VIEW',
                    '-d', 'https://www.google.com',
                    'com.android.chrome'
                ], timeout=10)
                
                logger.info("✅ Chrome browser opened with Google.com")
                time.sleep(5)  # Wait for page to load
                return True
            else:
                logger.error("❌ No device ID available")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error opening browser: {e}")
            return False
    
    def _perform_google_search(self) -> bool:
        """Perform a search on Google"""
        logger.info("🔍 Performing Google search...")
        
        try:
            if self.device_id:
                # Simulate search interaction using ADB input
                search_query = "Genymotion Android automation"
                
                # Tap on search box (approximate coordinates for Pixel 6)
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'tap', '540', '400'
                ], timeout=5)
                
                time.sleep(2)
                
                # Type search query
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'text', search_query
                ], timeout=5)
                
                time.sleep(2)
                
                # Press Enter
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_ENTER'
                ], timeout=5)
                
                logger.info(f"✅ Search performed: '{search_query}'")
                time.sleep(3)  # Wait for results
                return True
            else:
                logger.error("❌ No device ID available")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error performing search: {e}")
            return False
    
    def _cleanup(self) -> bool:
        """Clean up resources"""
        logger.info("🧹 Cleaning up...")
        
        try:
            # Stop Appium server
            if self.appium_manager:
                self.appium_manager.stop_server()
                logger.info("✅ Appium server stopped")
            
            # Optionally stop the instance (comment out if you want to keep it running)
            # if self.instance_name:
            #     self.genymotion_manager.stop_instance(self.instance_name)
            #     logger.info(f"✅ Instance '{self.instance_name}' stopped")
            
            logger.info("✅ Cleanup completed")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            return False


def main():
    """Main function to run the scenario"""
    scenario = GenymotionBrowserAutomationScenario()
    
    logger.info("=" * 60)
    logger.info("🚀 GENYMOTION BROWSER AUTOMATION SCENARIO")
    logger.info("=" * 60)
    
    success = scenario.run_complete_scenario()
    
    if success:
        logger.info("🎉 Scenario completed successfully!")
        logger.info("💡 The Genymotion instance is still running for further testing")
        logger.info("💡 You can stop it manually using Genymotion Desktop or:")
        logger.info(f"   gmtool admin stop {scenario.instance_name}")
    else:
        logger.error("❌ Scenario failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
