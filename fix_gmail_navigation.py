#!/usr/bin/env python3
"""
Fix Gmail Navigation
Ensures we actually reach the Gmail signup page and test the correct form fields
"""

import sys
import os
import time
import subprocess
import re
from typing import Dict, List, Optional

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


class GmailNavigationFixer:
    def __init__(self):
        self.genymotion = GenymotionManager()
        
    def get_running_device(self) -> Optional[tuple]:
        """Get first running device"""
        try:
            instances = self.genymotion.get_available_instances()
            
            for name, info in instances.items():
                if info.get('status') == 'running':
                    device_id = self.genymotion.get_device_adb_id(name)
                    return name, device_id
            
            return None
        except Exception as e:
            print(f"Error getting running device: {e}")
            return None
    
    def force_open_gmail_signup(self, device_id: str) -> bool:
        """Force open Gmail signup page using multiple strategies"""
        print("🌐 Force opening Gmail signup page...")
        
        try:
            # Strategy 1: Kill all browsers and clear data
            print("   🧹 Clearing browser state...")
            browsers = ['com.android.browser', 'com.android.chrome', 'com.qwant.liberty']
            
            for browser in browsers:
                subprocess.run(f'adb -s {device_id} shell am force-stop {browser}', shell=True, timeout=5)
                subprocess.run(f'adb -s {device_id} shell pm clear {browser}', shell=True, timeout=5)
            
            time.sleep(3)
            
            # Strategy 2: Try Chrome first (if available)
            print("   🌐 Trying Chrome browser...")
            gmail_url = "https://accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp"
            
            chrome_cmd = f'adb -s {device_id} shell am start -n com.android.chrome/com.google.android.apps.chrome.Main -a android.intent.action.VIEW -d "{gmail_url}"'
            chrome_result = subprocess.run(chrome_cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if chrome_result.returncode == 0:
                print("   ✅ Chrome opened successfully")
                time.sleep(10)
                return self._verify_gmail_page(device_id)
            
            # Strategy 3: Try default browser
            print("   🌐 Trying default browser...")
            default_cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "{gmail_url}"'
            default_result = subprocess.run(default_cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if default_result.returncode == 0:
                print("   ✅ Default browser opened")
                time.sleep(10)
                return self._verify_gmail_page(device_id)
            
            # Strategy 4: Manual navigation
            print("   🔧 Manual navigation approach...")
            return self._manual_navigation(device_id)
            
        except Exception as e:
            print(f"❌ Error opening Gmail signup: {e}")
            return False
    
    def _verify_gmail_page(self, device_id: str) -> bool:
        """Verify we're actually on the Gmail signup page"""
        try:
            # Get UI dump to check page content
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                return False
            
            ui_content = result.stdout.lower()
            
            # Check for Gmail-specific indicators
            gmail_indicators = [
                "create your google account",
                "first name",
                "last name", 
                "choose your username",
                "accounts.google.com"
            ]
            
            found_indicators = sum(1 for indicator in gmail_indicators if indicator in ui_content)
            
            print(f"   📊 Found {found_indicators}/{len(gmail_indicators)} Gmail indicators")
            
            # Also check we're not in a search engine
            search_indicators = ["qwant", "search", "suggest"]
            found_search = sum(1 for indicator in search_indicators if indicator in ui_content)
            
            print(f"   📊 Found {found_search} search engine indicators")
            
            return found_indicators >= 2 and found_search == 0
            
        except Exception as e:
            print(f"   ❌ Error verifying page: {e}")
            return False
    
    def _manual_navigation(self, device_id: str) -> bool:
        """Manual navigation to Gmail signup"""
        try:
            print("   🔧 Starting manual navigation...")
            
            # Open browser
            subprocess.run(f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "https://google.com"', 
                         shell=True, timeout=10)
            time.sleep(5)
            
            # Type Gmail signup URL in address bar
            gmail_url = "accounts.google.com/signup"
            
            # Tap address bar (usually at top)
            subprocess.run(f'adb -s {device_id} shell input tap 400 100', shell=True, timeout=5)
            time.sleep(2)
            
            # Clear and type URL
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_CTRL_A', shell=True, timeout=5)
            subprocess.run(f'adb -s {device_id} shell input text "{gmail_url}"', shell=True, timeout=5)
            time.sleep(2)
            
            # Press Enter
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_ENTER', shell=True, timeout=5)
            time.sleep(10)
            
            return self._verify_gmail_page(device_id)
            
        except Exception as e:
            print(f"   ❌ Manual navigation failed: {e}")
            return False
    
    def find_gmail_form_fields(self, device_id: str) -> Dict:
        """Find actual Gmail form fields"""
        print("🔍 Finding Gmail form fields...")
        
        try:
            # Get UI dump
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                return {"error": "Failed to get UI dump"}
            
            ui_content = result.stdout
            
            # Save for debugging
            with open('/tmp/gmail_correct_ui_dump.xml', 'w') as f:
                f.write(ui_content)
            
            # Look for input fields with better context analysis
            fields = {
                "first_name_fields": [],
                "last_name_fields": [],
                "username_fields": [],
                "password_fields": [],
                "all_input_fields": []
            }
            
            # Find all EditText fields
            edittext_pattern = r'<node[^>]*class="android\.widget\.EditText"[^>]*>'
            edittext_matches = re.findall(edittext_pattern, ui_content)
            
            for i, match in enumerate(edittext_matches):
                field_info = self._parse_field_detailed(match, i, ui_content)
                if field_info:
                    fields["all_input_fields"].append(field_info)
                    
                    # Categorize based on surrounding context
                    context = self._get_field_context(field_info, ui_content)
                    field_info["context"] = context
                    
                    if any(keyword in context.lower() for keyword in ["first", "given", "name"]):
                        if "last" not in context.lower():
                            fields["first_name_fields"].append(field_info)
                    
                    if any(keyword in context.lower() for keyword in ["last", "surname", "family"]):
                        fields["last_name_fields"].append(field_info)
                    
                    if any(keyword in context.lower() for keyword in ["username", "email", "choose"]):
                        fields["username_fields"].append(field_info)
                    
                    if any(keyword in context.lower() for keyword in ["password", "create password"]):
                        fields["password_fields"].append(field_info)
            
            print(f"   📊 Found {len(fields['all_input_fields'])} total input fields")
            print(f"   📊 First name candidates: {len(fields['first_name_fields'])}")
            print(f"   📊 Last name candidates: {len(fields['last_name_fields'])}")
            print(f"   📊 Username candidates: {len(fields['username_fields'])}")
            print(f"   📊 Password candidates: {len(fields['password_fields'])}")
            
            return fields
            
        except Exception as e:
            return {"error": str(e)}
    
    def _parse_field_detailed(self, element_xml: str, index: int, full_ui: str) -> Optional[Dict]:
        """Parse field with detailed information"""
        try:
            field = {
                "index": index,
                "bounds": None,
                "text": "",
                "content_desc": "",
                "resource_id": "",
                "raw_xml": element_xml
            }
            
            # Extract bounds
            bounds_match = re.search(r'bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', element_xml)
            if bounds_match:
                x1, y1, x2, y2 = bounds_match.groups()
                field["bounds"] = {
                    "x1": int(x1), "y1": int(y1), "x2": int(x2), "y2": int(y2),
                    "center_x": (int(x1) + int(x2)) // 2,
                    "center_y": (int(y1) + int(y2)) // 2,
                    "width": int(x2) - int(x1),
                    "height": int(y2) - int(y1)
                }
            
            # Extract other attributes
            text_match = re.search(r'text="([^"]*)"', element_xml)
            if text_match:
                field["text"] = text_match.group(1)
            
            desc_match = re.search(r'content-desc="([^"]*)"', element_xml)
            if desc_match:
                field["content_desc"] = desc_match.group(1)
            
            resource_match = re.search(r'resource-id="([^"]*)"', element_xml)
            if resource_match:
                field["resource_id"] = resource_match.group(1)
            
            return field
            
        except Exception as e:
            print(f"Error parsing field: {e}")
            return None
    
    def _get_field_context(self, field_info: Dict, ui_content: str) -> str:
        """Get context around a field by looking at nearby text elements"""
        if not field_info.get("bounds"):
            return ""
        
        try:
            bounds = field_info["bounds"]
            field_center_x = bounds["center_x"]
            field_center_y = bounds["center_y"]
            
            # Look for TextView elements near this field
            textview_pattern = r'<node[^>]*class="android\.widget\.TextView"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*text="([^"]*)"[^>]*>'
            textview_matches = re.findall(textview_pattern, ui_content)
            
            nearby_texts = []
            
            for match in textview_matches:
                tx1, ty1, tx2, ty2, text = match
                text_center_x = (int(tx1) + int(tx2)) // 2
                text_center_y = (int(ty1) + int(ty2)) // 2
                
                # Check if text is near the field (within 150 pixels)
                distance = ((text_center_x - field_center_x) ** 2 + (text_center_y - field_center_y) ** 2) ** 0.5
                
                if distance < 150 and text.strip() and len(text) < 100:
                    nearby_texts.append(text.strip())
            
            return " ".join(nearby_texts)
            
        except Exception as e:
            return ""
    
    def test_gmail_form_input(self, device_id: str, fields: Dict) -> Dict:
        """Test input on actual Gmail form fields"""
        print("⌨️ Testing Gmail form input...")
        
        results = {"tests": [], "success_count": 0}
        
        # Test first name field
        if fields["first_name_fields"]:
            field = fields["first_name_fields"][0]
            print(f"\n🧪 Testing first name field:")
            print(f"   Context: {field.get('context', 'N/A')}")
            
            result = self._test_field_input(device_id, field, "Ahmet")
            results["tests"].append({"type": "first_name", "result": result})
            if result["success"]:
                results["success_count"] += 1
        
        # Test last name field
        if fields["last_name_fields"]:
            field = fields["last_name_fields"][0]
            print(f"\n🧪 Testing last name field:")
            print(f"   Context: {field.get('context', 'N/A')}")
            
            result = self._test_field_input(device_id, field, "Yılmaz")
            results["tests"].append({"type": "last_name", "result": result})
            if result["success"]:
                results["success_count"] += 1
        
        # Test username field
        if fields["username_fields"]:
            field = fields["username_fields"][0]
            print(f"\n🧪 Testing username field:")
            print(f"   Context: {field.get('context', 'N/A')}")
            
            result = self._test_field_input(device_id, field, "ahmetyilmaz123")
            results["tests"].append({"type": "username", "result": result})
            if result["success"]:
                results["success_count"] += 1
        
        return results
    
    def _test_field_input(self, device_id: str, field: Dict, test_text: str) -> Dict:
        """Test input on a specific field"""
        if not field.get("bounds"):
            return {"success": False, "error": "No bounds"}
        
        try:
            bounds = field["bounds"]
            center_x = bounds["center_x"]
            center_y = bounds["center_y"]
            
            print(f"   📍 Tapping at ({center_x}, {center_y})")
            
            # Tap field
            subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', shell=True, timeout=5)
            time.sleep(1)
            
            # Clear existing text
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_CTRL_A', shell=True, timeout=5)
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_DEL', shell=True, timeout=5)
            time.sleep(0.5)
            
            # Input text
            print(f"   ⌨️ Inputting: '{test_text}'")
            input_result = subprocess.run(f'adb -s {device_id} shell input text "{test_text}"', 
                                        shell=True, capture_output=True, text=True, timeout=10)
            
            success = input_result.returncode == 0
            
            if success:
                print(f"   ✅ Input successful")
            else:
                print(f"   ❌ Input failed: {input_result.stderr}")
            
            time.sleep(1)
            
            return {
                "success": success,
                "test_text": test_text,
                "field_info": field,
                "error": input_result.stderr if not success else None
            }
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {"success": False, "error": str(e)}
    
    def run_complete_fix(self) -> Dict:
        """Run complete Gmail navigation fix and form test"""
        print("🔧 Gmail Navigation Fix and Form Test")
        print("=" * 50)
        
        # Get running device
        device_info = self.get_running_device()
        if not device_info:
            return {"error": "No running device found"}
        
        device_name, device_id = device_info
        print(f"📱 Using device: {device_name} ({device_id})")
        
        # Force open Gmail signup
        if not self.force_open_gmail_signup(device_id):
            return {"error": "Failed to navigate to Gmail signup page"}
        
        # Take screenshot
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_fixed_navigation.png', 
                         shell=True, timeout=10)
            print("📸 Screenshot: /tmp/gmail_fixed_navigation.png")
        except:
            pass
        
        # Find Gmail form fields
        fields = self.find_gmail_form_fields(device_id)
        if "error" in fields:
            return {"error": f"Failed to find form fields: {fields['error']}"}
        
        # Test form input
        input_results = self.test_gmail_form_input(device_id, fields)
        
        return {
            "device_name": device_name,
            "device_id": device_id,
            "fields_found": fields,
            "input_results": input_results,
            "success": True
        }


def main():
    """Main execution"""
    fixer = GmailNavigationFixer()
    results = fixer.run_complete_fix()
    
    if "error" in results:
        print(f"❌ Fix failed: {results['error']}")
        return 1
    
    print(f"\n📋 Final Results:")
    print(f"   Device: {results['device_name']}")
    
    fields = results["fields_found"]
    print(f"   Total fields found: {len(fields['all_input_fields'])}")
    print(f"   First name fields: {len(fields['first_name_fields'])}")
    print(f"   Last name fields: {len(fields['last_name_fields'])}")
    print(f"   Username fields: {len(fields['username_fields'])}")
    
    input_results = results["input_results"]
    print(f"   Successful inputs: {input_results['success_count']}/{len(input_results['tests'])}")
    
    if input_results['success_count'] > 0:
        print(f"\n✅ Gmail form input is working!")
    else:
        print(f"\n⚠️ Gmail form input still has issues")
    
    print(f"\n📸 Screenshot: /tmp/gmail_fixed_navigation.png")
    print(f"📄 UI dump: /tmp/gmail_correct_ui_dump.xml")
    
    return 0


if __name__ == "__main__":
    exit(main())
