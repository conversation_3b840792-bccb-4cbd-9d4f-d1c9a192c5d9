#!/usr/bin/env node
/**
 * Faker.js Data Generator for Gmail Account Creation
 * Generates realistic personal information for automated account creation
 */

const { faker } = require('@faker-js/faker');

/**
 * Generate realistic personal information
 */
function generatePersonalInfo() {
    // Set locale to Turkish for realistic Turkish data
    faker.setLocale('tr');

    // Turkish names arrays for more authentic names
    const turkishFirstNames = [
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', 'Furkan', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>ga', 'Umut', 'Volkan', 'Yağız', 'Zafer', 'Barış'
    ];

    const turkishLastNames = [
        'Yılmaz', 'Kaya', 'Demir', 'Şahin', 'Çelik', 'Yıldız', 'Yıldırım', 'Öztürk', 'Aydin', 'Özdemir',
        'Arslan', 'Doğan', 'Kilic', 'Aslan', 'Çetin', 'Kara', 'Koç', 'Kurt', 'Özkan', 'Şimşek',
        'Erdoğan', 'Güneş', 'Korkmaz', 'Çakır', 'Uçar', 'Şen', 'Polat', 'Güler', 'Kaplan', 'Kılıç',
        'Aktaş', 'Ateş', 'Bulut', 'Duman', 'Erdem', 'Fidan', 'Gökçe', 'Işık', 'Karaca', 'Mutlu'
    ];

    // Always use Turkish names for consistent Turkish data
    const firstName = faker.helpers.arrayElement(turkishFirstNames);
    const lastName = faker.helpers.arrayElement(turkishLastNames);

    // Convert Turkish characters to ASCII for username compatibility
    const convertTurkishToAscii = (text) => {
        return text
            .replace(/ğ/g, 'g').replace(/Ğ/g, 'G')
            .replace(/ü/g, 'u').replace(/Ü/g, 'U')
            .replace(/ş/g, 's').replace(/Ş/g, 'S')
            .replace(/ı/g, 'i').replace(/İ/g, 'I')
            .replace(/ö/g, 'o').replace(/Ö/g, 'O')
            .replace(/ç/g, 'c').replace(/Ç/g, 'C');
    };

    const firstNameAscii = convertTurkishToAscii(firstName.toLowerCase());
    const lastNameAscii = convertTurkishToAscii(lastName.toLowerCase());

    // Generate username variations
    const usernameVariations = [
        `${firstNameAscii}${lastNameAscii}`,
        `${firstNameAscii}.${lastNameAscii}`,
        `${firstNameAscii}_${lastNameAscii}`,
        `${firstNameAscii}${lastNameAscii}${faker.number.int({ min: 10, max: 999 })}`,
        `${firstNameAscii.substring(0, 3)}${lastNameAscii}${faker.number.int({ min: 10, max: 99 })}`,
    ];

    const username = faker.helpers.arrayElement(usernameVariations);

    // Generate secure password
    const password = generateSecurePassword();

    // Generate birth date (18-65 years old)
    const birthDate = faker.date.birthdate({
        min: 18,
        max: 65,
        mode: 'age'
    });

    // Format birth date as MM/DD/YYYY
    const formattedBirthDate = `${String(birthDate.getMonth() + 1).padStart(2, '0')}/${String(birthDate.getDate()).padStart(2, '0')}/${birthDate.getFullYear()}`;

    // Generate Turkish phone number (+90 5XX XXX XX XX format)
    const phoneNumber = `+90 5${faker.number.int({ min: 10, max: 99 })} ${faker.number.int({ min: 100, max: 999 })} ${faker.number.int({ min: 10, max: 99 })} ${faker.number.int({ min: 10, max: 99 })}`;

    // Generate recovery email with Turkish providers
    const recoveryEmailProviders = ['outlook.com', 'hotmail.com', 'gmail.com', 'yandex.com.tr'];
    const recoveryProvider = faker.helpers.arrayElement(recoveryEmailProviders);
    const recoveryEmail = `${username}_recovery@${recoveryProvider}`;

    return {
        firstName,
        lastName,
        username,
        password,
        birthDate: formattedBirthDate,
        phoneNumber,
        recoveryEmail,
        fullName: `${firstName} ${lastName}`,
        // Additional metadata
        generatedAt: new Date().toISOString(),
        locale: 'tr',
        nameSource: 'turkish_authentic'
    };
}

/**
 * Generate a secure password that meets Gmail requirements
 */
function generateSecurePassword() {
    // Gmail password requirements:
    // - At least 8 characters
    // - Mix of letters, numbers, and symbols

    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%^&*';

    let password = '';

    // Ensure at least one character from each category
    password += faker.helpers.arrayElement(lowercase);
    password += faker.helpers.arrayElement(uppercase);
    password += faker.helpers.arrayElement(numbers);
    password += faker.helpers.arrayElement(symbols);

    // Fill the rest with random characters
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = 4; i < 12; i++) {
        password += faker.helpers.arrayElement(allChars);
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Generate additional profile data for more realistic accounts
 */
function generateProfileData() {
    return {
        interests: faker.helpers.arrayElements([
            'Technology', 'Sports', 'Music', 'Travel', 'Photography',
            'Cooking', 'Reading', 'Gaming', 'Fitness', 'Art'
        ], faker.number.int({ min: 2, max: 5 })),

        occupation: faker.person.jobTitle(),
        company: faker.company.name(),

        address: {
            street: faker.location.streetAddress(),
            city: faker.location.city(),
            state: faker.location.state(),
            zipCode: faker.location.zipCode(),
            country: 'TR'
        },

        // Browser and device preferences
        preferredLanguage: 'tr-TR',
        timezone: 'Europe/Istanbul',

        // Security questions (common ones)
        securityQuestions: [
            {
                question: "What was the name of your first pet?",
                answer: faker.animal.dog()
            },
            {
                question: "What city were you born in?",
                answer: faker.location.city()
            },
            {
                question: "What was your mother's maiden name?",
                answer: faker.person.lastName()
            }
        ]
    };
}

/**
 * Main function to generate and output data
 */
function main() {
    try {
        const personalInfo = generatePersonalInfo();
        const profileData = generateProfileData();

        const completeData = {
            ...personalInfo,
            profile: profileData
        };

        // Output as JSON for Python to parse
        console.log(JSON.stringify(completeData, null, 2));

    } catch (error) {
        console.error('Error generating fake data:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    generatePersonalInfo,
    generateProfileData,
    generateSecurePassword
};
