#!/usr/bin/env node
/**
 * Faker.js Data Generator for Gmail Account Creation
 * Generates realistic personal information for automated account creation
 */

const { faker } = require('@faker-js/faker');

/**
 * Generate realistic personal information
 */
function generatePersonalInfo() {
    // Set locale for more realistic data
    faker.locale = 'en_US';
    
    // Generate basic info
    const firstName = faker.name.firstName();
    const lastName = faker.name.lastName();
    
    // Generate username variations
    const usernameVariations = [
        `${firstName.toLowerCase()}${lastName.toLowerCase()}`,
        `${firstName.toLowerCase()}.${lastName.toLowerCase()}`,
        `${firstName.toLowerCase()}_${lastName.toLowerCase()}`,
        `${firstName.toLowerCase()}${lastName.toLowerCase()}${faker.datatype.number({ min: 10, max: 999 })}`,
        `${firstName.toLowerCase().substring(0, 3)}${lastName.toLowerCase()}${faker.datatype.number({ min: 10, max: 99 })}`,
    ];
    
    const username = faker.helpers.arrayElement(usernameVariations);
    
    // Generate secure password
    const password = generateSecurePassword();
    
    // Generate birth date (18-65 years old)
    const birthDate = faker.date.birthdate({ 
        min: 18, 
        max: 65, 
        mode: 'age' 
    });
    
    // Format birth date as MM/DD/YYYY
    const formattedBirthDate = `${String(birthDate.getMonth() + 1).padStart(2, '0')}/${String(birthDate.getDate()).padStart(2, '0')}/${birthDate.getFullYear()}`;
    
    // Generate phone number
    const phoneNumber = faker.phone.number('+1##########');
    
    // Generate recovery email
    const recoveryEmailProviders = ['outlook.com', 'yahoo.com', 'hotmail.com', 'icloud.com'];
    const recoveryProvider = faker.helpers.arrayElement(recoveryEmailProviders);
    const recoveryEmail = `${username}_recovery@${recoveryProvider}`;
    
    return {
        firstName,
        lastName,
        username,
        password,
        birthDate: formattedBirthDate,
        phoneNumber,
        recoveryEmail,
        fullName: `${firstName} ${lastName}`,
        // Additional metadata
        generatedAt: new Date().toISOString(),
        locale: faker.locale
    };
}

/**
 * Generate a secure password that meets Gmail requirements
 */
function generateSecurePassword() {
    // Gmail password requirements:
    // - At least 8 characters
    // - Mix of letters, numbers, and symbols
    
    const lowercase = 'abcdefghijklmnopqrstuvwxyz';
    const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const numbers = '**********';
    const symbols = '!@#$%^&*';
    
    let password = '';
    
    // Ensure at least one character from each category
    password += faker.helpers.arrayElement(lowercase);
    password += faker.helpers.arrayElement(uppercase);
    password += faker.helpers.arrayElement(numbers);
    password += faker.helpers.arrayElement(symbols);
    
    // Fill the rest with random characters
    const allChars = lowercase + uppercase + numbers + symbols;
    for (let i = 4; i < 12; i++) {
        password += faker.helpers.arrayElement(allChars);
    }
    
    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
}

/**
 * Generate additional profile data for more realistic accounts
 */
function generateProfileData() {
    return {
        interests: faker.helpers.arrayElements([
            'Technology', 'Sports', 'Music', 'Travel', 'Photography', 
            'Cooking', 'Reading', 'Gaming', 'Fitness', 'Art'
        ], faker.datatype.number({ min: 2, max: 5 })),
        
        occupation: faker.name.jobTitle(),
        company: faker.company.name(),
        
        address: {
            street: faker.address.streetAddress(),
            city: faker.address.city(),
            state: faker.address.stateAbbr(),
            zipCode: faker.address.zipCode(),
            country: 'US'
        },
        
        // Browser and device preferences
        preferredLanguage: 'en-US',
        timezone: faker.address.timeZone(),
        
        // Security questions (common ones)
        securityQuestions: [
            {
                question: "What was the name of your first pet?",
                answer: faker.animal.dog()
            },
            {
                question: "What city were you born in?",
                answer: faker.address.city()
            },
            {
                question: "What was your mother's maiden name?",
                answer: faker.name.lastName()
            }
        ]
    };
}

/**
 * Main function to generate and output data
 */
function main() {
    try {
        const personalInfo = generatePersonalInfo();
        const profileData = generateProfileData();
        
        const completeData = {
            ...personalInfo,
            profile: profileData
        };
        
        // Output as JSON for Python to parse
        console.log(JSON.stringify(completeData, null, 2));
        
    } catch (error) {
        console.error('Error generating fake data:', error);
        process.exit(1);
    }
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    generatePersonalInfo,
    generateProfileData,
    generateSecurePassword
};
