#!/usr/bin/env python3
"""
Gmail Form Field Analyzer
Advanced analysis of Gmail signup form fields to identify correct input targets
"""

import sys
import os
import time
import subprocess
import re
import json
from typing import Dict, List, Optional

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


class GmailFormAnalyzer:
    def __init__(self):
        self.genymotion = GenymotionManager()
        
    def get_running_device(self) -> Optional[tuple]:
        """Get first running device"""
        try:
            instances = self.genymotion.get_available_instances()
            
            for name, info in instances.items():
                if info.get('status') == 'running':
                    device_id = self.genymotion.get_device_adb_id(name)
                    return name, device_id
            
            return None
        except Exception as e:
            print(f"Error getting running device: {e}")
            return None
    
    def navigate_to_gmail_signup(self, device_id: str) -> bool:
        """Navigate to Gmail signup page and wait for load"""
        print("🌐 Navigating to Gmail signup page...")
        
        try:
            # Clear any existing browser state
            subprocess.run(f'adb -s {device_id} shell am force-stop com.android.browser', shell=True, timeout=5)
            subprocess.run(f'adb -s {device_id} shell am force-stop com.android.chrome', shell=True, timeout=5)
            time.sleep(2)
            
            # Open Gmail signup page
            url = "https://accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp"
            cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "{url}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                print(f"❌ Failed to open browser: {result.stderr}")
                return False
            
            print("⏳ Waiting for page to fully load...")
            time.sleep(15)  # Give more time for page to load
            
            return True
            
        except Exception as e:
            print(f"❌ Navigation error: {e}")
            return False
    
    def get_detailed_ui_analysis(self, device_id: str) -> Dict:
        """Get detailed UI analysis with better field identification"""
        print("🔍 Performing detailed UI analysis...")
        
        try:
            # Get UI dump
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=20)
            
            if result.returncode != 0:
                return {"error": "Failed to get UI dump"}
            
            ui_content = result.stdout
            
            # Save UI dump for debugging
            with open('/tmp/gmail_ui_dump.xml', 'w') as f:
                f.write(ui_content)
            print("📄 UI dump saved: /tmp/gmail_ui_dump.xml")
            
            analysis = {
                "all_input_fields": [],
                "potential_name_fields": [],
                "potential_username_fields": [],
                "potential_password_fields": [],
                "buttons": [],
                "page_indicators": []
            }
            
            # Find all input-like elements
            input_patterns = [
                r'<node[^>]*class="android\.widget\.EditText"[^>]*>',
                r'<node[^>]*class="android\.webkit\.WebView"[^>]*>',
                r'<node[^>]*focusable="true"[^>]*clickable="true"[^>]*>'
            ]
            
            all_elements = []
            for pattern in input_patterns:
                matches = re.findall(pattern, ui_content)
                all_elements.extend(matches)
            
            # Analyze each element
            for i, element in enumerate(all_elements):
                field_info = self._analyze_element(element, i, ui_content)
                if field_info:
                    analysis["all_input_fields"].append(field_info)
                    
                    # Categorize fields based on context
                    self._categorize_field(field_info, analysis)
            
            # Find buttons
            button_patterns = [
                r'<node[^>]*class="android\.widget\.Button"[^>]*text="([^"]*)"[^>]*>',
                r'<node[^>]*clickable="true"[^>]*text="([^"]*)"[^>]*>'
            ]
            
            for pattern in button_patterns:
                button_matches = re.findall(pattern, ui_content)
                for button_text in button_matches:
                    if button_text.strip() and len(button_text) < 50:
                        analysis["buttons"].append(button_text)
            
            # Look for page indicators
            page_keywords = [
                "create your google account", "first name", "last name", 
                "choose your username", "create a strong password"
            ]
            
            for keyword in page_keywords:
                if keyword.lower() in ui_content.lower():
                    analysis["page_indicators"].append(keyword)
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}
    
    def _analyze_element(self, element_xml: str, index: int, full_ui: str) -> Optional[Dict]:
        """Analyze individual UI element"""
        try:
            info = {
                "index": index,
                "bounds": None,
                "text": "",
                "content_desc": "",
                "resource_id": "",
                "class": "",
                "clickable": False,
                "focusable": False,
                "enabled": True,
                "context_hints": []
            }
            
            # Extract basic attributes
            class_match = re.search(r'class="([^"]*)"', element_xml)
            if class_match:
                info["class"] = class_match.group(1)
            
            bounds_match = re.search(r'bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', element_xml)
            if bounds_match:
                x1, y1, x2, y2 = bounds_match.groups()
                info["bounds"] = {
                    "x1": int(x1), "y1": int(y1), "x2": int(x2), "y2": int(y2),
                    "center_x": (int(x1) + int(x2)) // 2,
                    "center_y": (int(y1) + int(y2)) // 2,
                    "width": int(x2) - int(x1),
                    "height": int(y2) - int(y1)
                }
            
            text_match = re.search(r'text="([^"]*)"', element_xml)
            if text_match:
                info["text"] = text_match.group(1)
            
            desc_match = re.search(r'content-desc="([^"]*)"', element_xml)
            if desc_match:
                info["content_desc"] = desc_match.group(1)
            
            resource_match = re.search(r'resource-id="([^"]*)"', element_xml)
            if resource_match:
                info["resource_id"] = resource_match.group(1)
            
            info["clickable"] = 'clickable="true"' in element_xml
            info["focusable"] = 'focusable="true"' in element_xml
            info["enabled"] = 'enabled="false"' not in element_xml
            
            # Look for context hints around this element
            if info["bounds"]:
                info["context_hints"] = self._find_context_hints(info["bounds"], full_ui)
            
            # Only return if it looks like an input field
            if (info["class"] == "android.widget.EditText" or 
                (info["clickable"] and info["focusable"] and info["bounds"] and 
                 info["bounds"]["width"] > 100 and info["bounds"]["height"] > 30)):
                return info
            
            return None
            
        except Exception as e:
            print(f"Error analyzing element: {e}")
            return None
    
    def _find_context_hints(self, bounds: Dict, ui_content: str) -> List[str]:
        """Find text hints near the input field"""
        hints = []
        
        try:
            # Look for TextView elements near this field
            textview_pattern = r'<node[^>]*class="android\.widget\.TextView"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"[^>]*text="([^"]*)"[^>]*>'
            textview_matches = re.findall(textview_pattern, ui_content)
            
            field_center_x = bounds["center_x"]
            field_center_y = bounds["center_y"]
            
            for match in textview_matches:
                tx1, ty1, tx2, ty2, text = match
                text_center_x = (int(tx1) + int(tx2)) // 2
                text_center_y = (int(ty1) + int(ty2)) // 2
                
                # Check if text is near the field (within 200 pixels)
                distance = ((text_center_x - field_center_x) ** 2 + (text_center_y - field_center_y) ** 2) ** 0.5
                
                if distance < 200 and text.strip() and len(text) < 100:
                    hints.append(text.strip())
            
        except Exception as e:
            print(f"Error finding context hints: {e}")
        
        return hints
    
    def _categorize_field(self, field_info: Dict, analysis: Dict):
        """Categorize field based on context and hints"""
        text = field_info["text"].lower()
        desc = field_info["content_desc"].lower()
        resource_id = field_info["resource_id"].lower()
        hints = " ".join(field_info["context_hints"]).lower()
        
        all_text = f"{text} {desc} {resource_id} {hints}"
        
        # Check for name fields
        name_keywords = ["first name", "firstname", "given name", "name", "first"]
        if any(keyword in all_text for keyword in name_keywords):
            analysis["potential_name_fields"].append(field_info)
        
        # Check for surname fields
        surname_keywords = ["last name", "lastname", "surname", "family name", "last"]
        if any(keyword in all_text for keyword in surname_keywords):
            analysis["potential_name_fields"].append(field_info)
        
        # Check for username fields
        username_keywords = ["username", "choose username", "email", "@"]
        if any(keyword in all_text for keyword in username_keywords):
            analysis["potential_username_fields"].append(field_info)
        
        # Check for password fields
        password_keywords = ["password", "create password", "strong password"]
        if any(keyword in all_text for keyword in password_keywords):
            analysis["potential_password_fields"].append(field_info)
    
    def test_targeted_input(self, device_id: str, field_info: Dict, test_text: str) -> Dict:
        """Test input on a specific field with better targeting"""
        print(f"🎯 Testing targeted input on field {field_info['index']}")
        print(f"   Class: {field_info['class']}")
        print(f"   Text: '{field_info['text']}'")
        print(f"   Hints: {field_info['context_hints']}")
        
        if not field_info["bounds"]:
            return {"success": False, "error": "No bounds"}
        
        bounds = field_info["bounds"]
        center_x = bounds["center_x"]
        center_y = bounds["center_y"]
        
        try:
            # Multiple tap strategy for better focus
            print(f"   📍 Tapping at ({center_x}, {center_y})")
            
            # First tap
            subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', shell=True, timeout=5)
            time.sleep(0.5)
            
            # Second tap to ensure focus
            subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', shell=True, timeout=5)
            time.sleep(1)
            
            # Clear any existing text
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_CTRL_A', shell=True, timeout=5)
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_DEL', shell=True, timeout=5)
            time.sleep(0.5)
            
            # Input the test text
            print(f"   ⌨️ Inputting: '{test_text}'")
            input_result = subprocess.run(f'adb -s {device_id} shell input text "{test_text}"', 
                                        shell=True, capture_output=True, text=True, timeout=10)
            
            success = input_result.returncode == 0
            
            if success:
                print(f"   ✅ Input successful")
            else:
                print(f"   ❌ Input failed: {input_result.stderr}")
            
            time.sleep(1)
            
            return {
                "success": success,
                "field_info": field_info,
                "test_text": test_text,
                "error": input_result.stderr if not success else None
            }
            
        except Exception as e:
            print(f"   ❌ Exception: {e}")
            return {"success": False, "error": str(e)}
    
    def run_analysis(self) -> Dict:
        """Run complete Gmail form analysis"""
        print("🔬 Gmail Form Field Analysis")
        print("=" * 50)
        
        # Get running device
        device_info = self.get_running_device()
        if not device_info:
            return {"error": "No running device found"}
        
        device_name, device_id = device_info
        print(f"📱 Using device: {device_name} ({device_id})")
        
        # Navigate to Gmail signup
        if not self.navigate_to_gmail_signup(device_id):
            return {"error": "Failed to navigate to Gmail signup"}
        
        # Take screenshot
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_form_analysis.png', 
                         shell=True, timeout=10)
            print("📸 Screenshot: /tmp/gmail_form_analysis.png")
        except:
            pass
        
        # Analyze UI
        analysis = self.get_detailed_ui_analysis(device_id)
        if "error" in analysis:
            return analysis
        
        print(f"\n📊 Analysis Results:")
        print(f"   Total input fields: {len(analysis['all_input_fields'])}")
        print(f"   Potential name fields: {len(analysis['potential_name_fields'])}")
        print(f"   Potential username fields: {len(analysis['potential_username_fields'])}")
        print(f"   Potential password fields: {len(analysis['potential_password_fields'])}")
        print(f"   Buttons found: {len(analysis['buttons'])}")
        print(f"   Page indicators: {analysis['page_indicators']}")
        
        # Test input on categorized fields
        input_results = []
        
        # Test name fields
        for i, field in enumerate(analysis['potential_name_fields'][:2]):
            test_text = f"TestName{i+1}"
            result = self.test_targeted_input(device_id, field, test_text)
            input_results.append(result)
        
        # Test username fields
        for i, field in enumerate(analysis['potential_username_fields'][:1]):
            test_text = f"testuser{i+1}"
            result = self.test_targeted_input(device_id, field, test_text)
            input_results.append(result)
        
        return {
            "device_name": device_name,
            "device_id": device_id,
            "analysis": analysis,
            "input_results": input_results
        }


def main():
    """Main analysis execution"""
    analyzer = GmailFormAnalyzer()
    results = analyzer.run_analysis()
    
    if "error" in results:
        print(f"❌ Analysis failed: {results['error']}")
        return 1
    
    print(f"\n📋 Final Results:")
    print(f"   Device: {results['device_name']}")
    
    successful_inputs = sum(1 for r in results['input_results'] if r.get('success'))
    total_tests = len(results['input_results'])
    
    print(f"   Input success rate: {successful_inputs}/{total_tests}")
    
    if successful_inputs == 0:
        print(f"\n⚠️ No successful inputs - this indicates the Gmail form issue!")
        print(f"   Check /tmp/gmail_form_analysis.png and /tmp/gmail_ui_dump.xml")
    
    return 0


if __name__ == "__main__":
    exit(main())
