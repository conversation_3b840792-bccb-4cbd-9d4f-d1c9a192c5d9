#!/usr/bin/env python3
"""
BlueStacks Automation System
Main entry point for the undetectable mobile automation framework
"""

import os
import sys
import argparse
import asyncio
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from device_manager import DeviceProfileManager
from automation_client import StealthAutomationClient
from location_spoofer import LocationSpoofer
from database import DatabaseManager
from api_server import app
import uvicorn


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    log_file = "logs/automation.log"
    os.makedirs("logs", exist_ok=True)
    
    # Remove default logger
    logger.remove()
    
    # Add console logger
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # Add file logger
    logger.add(
        log_file,
        level=log_level,
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days"
    )


def load_environment():
    """Load environment variables"""
    # Try to load .env file
    env_file = Path(".env")
    if env_file.exists():
        load_dotenv(env_file)
        logger.info("Loaded environment from .env file")
    else:
        logger.warning(".env file not found, using default values")


class AutomationOrchestrator:
    """Main orchestrator for the automation system"""
    
    def __init__(self):
        self.device_manager = DeviceProfileManager()
        self.automation_client = StealthAutomationClient(self.device_manager)
        self.location_spoofer = LocationSpoofer()
        self.db_manager = DatabaseManager()
        
    def run_automation_example(self, app_package: str = None):
        """Run a simple automation example"""
        logger.info("Starting automation example")
        
        try:
            # Start automation session
            success = self.automation_client.start_session(app_package)
            if not success:
                logger.error("Failed to start automation session")
                return False
            
            session_info = self.automation_client.get_session_info()
            logger.info(f"Session started: {session_info}")
            
            # Log session in database
            self.db_manager.create_automation_session(session_info)
            
            # Set random location
            self.location_spoofer.set_random_location()
            current_location = self.location_spoofer.get_current_location()
            if current_location:
                logger.info(f"Location set to: {current_location.latitude}, {current_location.longitude}")
            
            # Perform some random human activities
            logger.info("Performing random human activities...")
            self.automation_client.perform_random_human_activity(duration=60)
            
            # Check if session rotation is needed
            if self.automation_client.should_rotate_session():
                logger.info("Rotating session for anti-detection")
                self.automation_client.rotate_session(app_package)
            
            # End session
            self.automation_client.end_session()
            logger.info("Automation example completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Automation example failed: {e}")
            return False
    
    def start_dashboard_server(self, host: str = "0.0.0.0", port: int = 8000):
        """Start the web dashboard server"""
        logger.info(f"Starting dashboard server on {host}:{port}")
        
        try:
            uvicorn.run(
                app,
                host=host,
                port=port,
                log_level="info",
                access_log=True
            )
        except Exception as e:
            logger.error(f"Failed to start dashboard server: {e}")
    
    def cleanup(self):
        """Cleanup resources"""
        logger.info("Cleaning up automation system")
        
        try:
            # End any active sessions
            if self.automation_client.current_session_id:
                self.automation_client.end_session()
            
            # Cleanup device manager
            self.device_manager.cleanup_all_sessions()
            
            # Disable location spoofing
            self.location_spoofer.disable_mock_location()
            
            logger.info("Cleanup completed")
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="BlueStacks Automation System")
    parser.add_argument("--mode", choices=["dashboard", "example", "test"], 
                       default="dashboard", help="Operation mode")
    parser.add_argument("--app-package", type=str, help="Target app package name")
    parser.add_argument("--host", type=str, default="0.0.0.0", 
                       help="Dashboard server host")
    parser.add_argument("--port", type=int, default=8000, 
                       help="Dashboard server port")
    parser.add_argument("--log-level", type=str, default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    args = parser.parse_args()
    
    # Setup environment and logging
    load_environment()
    setup_logging(args.log_level)
    
    logger.info("Starting BlueStacks Automation System")
    logger.info(f"Mode: {args.mode}")
    
    # Create orchestrator
    orchestrator = AutomationOrchestrator()
    
    try:
        if args.mode == "dashboard":
            # Start dashboard server
            orchestrator.start_dashboard_server(args.host, args.port)
            
        elif args.mode == "example":
            # Run automation example
            success = orchestrator.run_automation_example(args.app_package)
            sys.exit(0 if success else 1)
            
        elif args.mode == "test":
            # Run system tests
            logger.info("Running system tests...")
            
            # Test device manager
            logger.info("Testing device manager...")
            session_id, profile = orchestrator.device_manager.create_new_session()
            logger.info(f"Created test session: {session_id}")
            
            # Test location spoofer
            logger.info("Testing location spoofer...")
            orchestrator.location_spoofer.set_city_location("new_york")
            
            # Test database
            logger.info("Testing database...")
            test_session_data = {
                "session_id": session_id,
                "device_name": profile.get("deviceName"),
                "device_model": profile.get("deviceModel"),
                "platform_version": profile.get("platformVersion"),
                "android_id": profile.get("android_id"),
                "imei": profile.get("imei"),
                "advertising_id": profile.get("advertising_id"),
                "location_name": "new_york",
                "latitude": 40.7128,
                "longitude": -74.0060
            }
            orchestrator.db_manager.create_automation_session(test_session_data)
            
            logger.info("All tests completed successfully")
            
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)
    finally:
        orchestrator.cleanup()


if __name__ == "__main__":
    main()