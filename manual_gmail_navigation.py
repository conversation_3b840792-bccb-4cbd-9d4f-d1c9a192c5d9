#!/usr/bin/env python3
"""
Manual Gmail Navigation
Manually navigate to Gmail by typing URL in address bar
"""

import sys
import os
import time
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


def manual_gmail_navigation():
    """Manually navigate to Gmail signup page"""
    print("🔧 Manual Gmail Navigation")
    print("=" * 50)

    # Initialize components
    genymotion = GenymotionManager()

    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()

    running_device = None
    device_id = None

    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break

    if not running_device:
        print("❌ No running device found")
        return False

    # Step 1: Open browser
    print("\n🌐 Step 1: Opening browser...")

    # Try to open Chrome first
    chrome_cmd = f'adb -s {device_id} shell am start -n com.android.chrome/com.google.android.apps.chrome.Main'
    chrome_result = subprocess.run(chrome_cmd, shell=True, capture_output=True, text=True, timeout=10)

    if chrome_result.returncode == 0:
        print("✅ Chrome opened successfully")
        time.sleep(3)
    else:
        print("🔘 Chrome not available, trying default browser...")
        # Try default browser
        browser_cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "https://google.com"'
        browser_result = subprocess.run(browser_cmd, shell=True, capture_output=True, text=True, timeout=10)

        if browser_result.returncode == 0:
            print("✅ Default browser opened")
            time.sleep(5)
        else:
            print("❌ Could not open browser")
            return False

    # Take screenshot after browser opens
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/browser_opened.png',
                     shell=True, timeout=10)
        print("📸 Browser opened: /tmp/browser_opened.png")
    except:
        pass

    # Step 2: Click address bar and type Gmail URL
    print("\n📝 Step 2: Typing Gmail URL in address bar...")

    # Click on address bar (usually at the top)
    address_bar_coords = [(400, 100), (300, 120), (400, 150)]

    for i, (x, y) in enumerate(address_bar_coords):
        print(f"   Trying address bar position {i+1}: ({x}, {y})")
        subprocess.run(f'adb -s {device_id} shell input tap {x} {y}', shell=True, timeout=5)
        time.sleep(2)

        # Clear existing text
        subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_CTRL_A', shell=True, timeout=5)
        subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_DEL', shell=True, timeout=5)
        time.sleep(1)

        # Type Gmail direct signup URL
        gmail_url = "accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp"
        print(f"   Typing direct signup URL: {gmail_url}")
        subprocess.run(f'adb -s {device_id} shell input text "{gmail_url}"', shell=True, timeout=10)
        time.sleep(2)

        # Press Enter
        subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_ENTER', shell=True, timeout=5)
        time.sleep(10)  # Wait for page to load

        # Check if we reached Gmail signup form directly
        if check_for_signup_form(device_id):
            print(f"✅ Successfully navigated directly to Gmail signup form via position {i+1}")
            break
        elif check_for_gmail_page(device_id):
            print(f"✅ Reached Gmail page via position {i+1}, checking for signup form...")
            break
    else:
        print("❌ Could not navigate to Gmail via address bar")
        return False

    # Take screenshot after navigation
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_page.png',
                     shell=True, timeout=10)
        print("📸 Gmail page: /tmp/gmail_page.png")
    except:
        pass

    # Step 3: Check if we're directly on signup form or need to navigate
    print("\n🔍 Step 3: Checking page type...")

    if check_for_signup_form(device_id):
        print("✅ Already on Gmail signup form! No need to click Create Account.")

        # Take final screenshot
        try:
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/signup_form_direct.png',
                         shell=True, timeout=10)
            print("📸 Direct signup form: /tmp/signup_form_direct.png")
        except:
            pass

        return True

    elif check_for_create_account(device_id):
        print("✅ Found Create Account option, need to click it...")

        # Try to click Create Account
        create_account_coords = [(119, 849), (150, 850), (100, 800), (200, 900)]

        for i, (x, y) in enumerate(create_account_coords):
            print(f"   Trying Create Account position {i+1}: ({x}, {y})")
            subprocess.run(f'adb -s {device_id} shell input tap {x} {y}', shell=True, timeout=5)
            time.sleep(3)

            # Check if menu appeared
            if check_for_personal_use_menu(device_id):
                print(f"✅ Successfully clicked Create Account at position {i+1}")

                # Take screenshot of menu
                try:
                    subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/create_account_menu.png',
                                 shell=True, timeout=10)
                    print("📸 Create Account menu: /tmp/create_account_menu.png")
                except:
                    pass

                # Step 4: Click "For my personal use"
                print("\n👤 Step 4: Clicking 'For my personal use'...")

                personal_use_coords = [(143, 620), (150, 620), (200, 620)]

                for j, (px, py) in enumerate(personal_use_coords):
                    print(f"   Trying personal use position {j+1}: ({px}, {py})")
                    subprocess.run(f'adb -s {device_id} shell input tap {px} {py}', shell=True, timeout=5)
                    time.sleep(5)

                    # Check if we reached signup form
                    if check_for_signup_form(device_id):
                        print(f"✅ Successfully clicked 'For my personal use' at position {j+1}")

                        # Take final screenshot
                        try:
                            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/signup_form_final.png',
                                         shell=True, timeout=10)
                            print("📸 Signup form: /tmp/signup_form_final.png")
                        except:
                            pass

                        return True

                print("❌ Could not click 'For my personal use'")
                return False

        print("❌ Could not click Create Account")
        return False
    else:
        print("❌ Could not find Create Account option")
        return False


def check_for_gmail_page(device_id: str) -> bool:
    """Check if we're on a Gmail page"""
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            ui_content = result.stdout.lower()
            gmail_indicators = ['accounts.google.com', 'google account', 'sign in', 'create account']
            return any(indicator in ui_content for indicator in gmail_indicators)

        return False
    except:
        return False


def check_for_create_account(device_id: str) -> bool:
    """Check if Create Account option is visible"""
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            ui_content = result.stdout.lower()
            return 'create account' in ui_content

        return False
    except:
        return False


def check_for_personal_use_menu(device_id: str) -> bool:
    """Check if the personal use menu appeared"""
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            ui_content = result.stdout.lower()
            menu_indicators = ['for my personal use', 'for my child', 'for work']
            return any(indicator in ui_content for indicator in menu_indicators)

        return False
    except:
        return False


def check_for_signup_form(device_id: str) -> bool:
    """Check if we're on the signup form"""
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

        if result.returncode == 0:
            ui_content = result.stdout.lower()
            signup_indicators = ['first name', 'last name', 'choose username']
            return any(indicator in ui_content for indicator in signup_indicators)

        return False
    except:
        return False


def main():
    """Main test execution"""
    success = manual_gmail_navigation()

    print("\n" + "=" * 50)
    if success:
        print("✅ Manual Gmail navigation SUCCESSFUL!")
        print("🎉 Successfully reached Gmail signup form!")
    else:
        print("❌ Manual Gmail navigation FAILED")
        print("🔍 Check the screenshots for debugging:")
        print("   - /tmp/browser_opened.png")
        print("   - /tmp/gmail_page.png")
        print("   - /tmp/create_account_menu.png")
        print("   - /tmp/signup_form_final.png")

    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
