#!/bin/bash

# Genymotion Automation Runner Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found. Please run setup.sh first."
    exit 1
fi

# Activate virtual environment
source venv/bin/activate

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found. Creating from .env.example..."
    cp .env.example .env
    print_warning "Please edit .env file with your configurations before running."
fi

# Parse command line arguments
MODE="dashboard"
APP_PACKAGE=""
HOST="0.0.0.0"
PORT="8000"
LOG_LEVEL="INFO"

while [[ $# -gt 0 ]]; do
    case $1 in
        --mode)
            MODE="$2"
            shift 2
            ;;
        --app-package)
            APP_PACKAGE="$2"
            shift 2
            ;;
        --host)
            HOST="$2"
            shift 2
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo "Options:"
            echo "  --mode MODE           Operation mode: dashboard, example, test (default: dashboard)"
            echo "  --app-package PKG     Target app package name"
            echo "  --host HOST           Dashboard server host (default: 0.0.0.0)"
            echo "  --port PORT           Dashboard server port (default: 8000)"
            echo "  --log-level LEVEL     Logging level: DEBUG, INFO, WARNING, ERROR (default: INFO)"
            echo "  --help                Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Create logs directory
mkdir -p logs

print_status "Starting Genymotion Automation System..."
print_status "Mode: $MODE"

# Build command
CMD="python main.py --mode $MODE --host $HOST --port $PORT --log-level $LOG_LEVEL"

if [ ! -z "$APP_PACKAGE" ]; then
    CMD="$CMD --app-package $APP_PACKAGE"
fi

# Run based on mode
case $MODE in
    "dashboard")
        print_status "Starting web dashboard on http://$HOST:$PORT"
        print_status "Press Ctrl+C to stop"
        $CMD
        ;;
    "example")
        print_status "Running automation example"
        if [ ! -z "$APP_PACKAGE" ]; then
            print_status "Target app: $APP_PACKAGE"
        fi
        $CMD
        ;;
    "test")
        print_status "Running system tests"
        $CMD
        ;;
    *)
        print_error "Invalid mode: $MODE"
        print_error "Valid modes: dashboard, example, test"
        exit 1
        ;;
esac

print_success "Automation system finished."