#!/usr/bin/env python3
"""
Quick Genymotion Browser Automation Scenario Runner

This script provides a simple command-line interface to run the complete
Genymotion browser automation scenario.

Usage:
    python3 run_genymotion_scenario.py [options]

Options:
    --device-name NAME      Name for the virtual device (default: GoogleTestDevice)
    --android-version VER   Android version (default: 13)
    --device-model MODEL    Device model (default: Google Pixel 6)
    --search-query QUERY    Google search query (default: Genymotion Android automation)
    --keep-running         Keep the instance running after scenario
    --verbose              Enable verbose logging
    --help                 Show this help message
"""

import argparse
import sys
import os
import time
import subprocess
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from examples.genymotion_browser_automation_scenario import GenymotionBrowserAutomationScenario
from loguru import logger


def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    logger.remove()  # Remove default handler

    if verbose:
        logger.add(sys.stdout, level="DEBUG",
                  format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    else:
        logger.add(sys.stdout, level="INFO",
                  format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Run Genymotion Browser Automation Scenario",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Run with default settings
    python3 run_genymotion_scenario.py

    # Use custom device name and search query
    python3 run_genymotion_scenario.py --device-name "MyTestDevice" --search-query "Python automation"

    # Use Samsung Galaxy S10 with Android 12
    python3 run_genymotion_scenario.py --device-model "Samsung Galaxy S10" --android-version "12"

    # Keep instance running after scenario
    python3 run_genymotion_scenario.py --keep-running
        """
    )

    parser.add_argument(
        "--device-name",
        default="GoogleTestDevice",
        help="Name for the virtual device (default: GoogleTestDevice)"
    )

    parser.add_argument(
        "--android-version",
        default="13",
        choices=["11", "12", "13", "14"],
        help="Android version (default: 13)"
    )

    parser.add_argument(
        "--device-model",
        default="Google Pixel 6",
        choices=[
            "Google Pixel 6", "Google Pixel 7", "Google Pixel 8",
            "Samsung Galaxy S10", "Samsung Galaxy S23", "Samsung Galaxy S24",
            "Custom Phone", "Custom Tablet"
        ],
        help="Device model (default: Google Pixel 6)"
    )

    parser.add_argument(
        "--search-query",
        default="Genymotion Android automation",
        help="Google search query (default: Genymotion Android automation)"
    )

    parser.add_argument(
        "--keep-running",
        action="store_true",
        help="Keep the instance running after scenario completion"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )

    return parser.parse_args()


def get_device_config(device_model: str, android_version: str) -> dict:
    """Get device configuration based on model"""

    device_configs = {
        "Google Pixel 6": {
            "hardware_profile": "Google Pixel 6",
            "screen_resolution": "1080x2340",
            "dpi": 411,
            "deviceManufacturer": "Google",
            "deviceModel": "Pixel 6"
        },
        "Google Pixel 7": {
            "hardware_profile": "Google Pixel 7",
            "screen_resolution": "1080x2400",
            "dpi": 416,
            "deviceManufacturer": "Google",
            "deviceModel": "Pixel 7"
        },
        "Google Pixel 8": {
            "hardware_profile": "Google Pixel 8",
            "screen_resolution": "1080x2400",
            "dpi": 428,
            "deviceManufacturer": "Google",
            "deviceModel": "Pixel 8"
        },
        "Samsung Galaxy S10": {
            "hardware_profile": "Samsung Galaxy S10",
            "screen_resolution": "1440x3040",
            "dpi": 550,
            "deviceManufacturer": "Samsung",
            "deviceModel": "SM-G973F"
        },
        "Samsung Galaxy S23": {
            "hardware_profile": "Samsung Galaxy S23",
            "screen_resolution": "1080x2340",
            "dpi": 425,
            "deviceManufacturer": "Samsung",
            "deviceModel": "SM-S911B"
        },
        "Samsung Galaxy S24": {
            "hardware_profile": "Samsung Galaxy S24",
            "screen_resolution": "1080x2340",
            "dpi": 416,
            "deviceManufacturer": "Samsung",
            "deviceModel": "SM-S921B"
        },
        "Custom Phone": {
            "hardware_profile": "Custom Phone",
            "screen_resolution": "1080x1920",
            "dpi": 420,
            "deviceManufacturer": "Generic",
            "deviceModel": "Custom Phone"
        },
        "Custom Tablet": {
            "hardware_profile": "Custom Tablet",
            "screen_resolution": "1536x2048",
            "dpi": 320,
            "deviceManufacturer": "Generic",
            "deviceModel": "Custom Tablet"
        }
    }

    config = device_configs.get(device_model, device_configs["Custom Phone"])
    config.update({
        "android_version": android_version,
        "device_name": device_model,
        "platformVersion": android_version
    })

    return config


class CustomGenymotionScenario(GenymotionBrowserAutomationScenario):
    """Extended scenario class with customization options"""

    def __init__(self, device_name: str, device_config: dict, search_query: str, keep_running: bool = False):
        super().__init__()
        self.instance_name = device_name
        self.device_config = device_config
        self.search_query = search_query
        self.keep_running = keep_running

    def _perform_google_search(self) -> bool:
        """Perform a custom Google search"""
        logger.info(f"🔍 Performing Google search: '{self.search_query}'")

        try:
            if self.device_id:
                # Tap on search box (approximate coordinates)
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'tap', '540', '400'
                ], timeout=5)

                time.sleep(2)

                # Type search query
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'text', self.search_query
                ], timeout=5)

                time.sleep(2)

                # Press Enter
                subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_ENTER'
                ], timeout=5)

                logger.info(f"✅ Search performed: '{self.search_query}'")
                time.sleep(3)  # Wait for results
                return True
            else:
                logger.error("❌ No device ID available")
                return False

        except Exception as e:
            logger.error(f"❌ Error performing search: {e}")
            return False

    def _cleanup(self) -> bool:
        """Custom cleanup with keep-running option"""
        logger.info("🧹 Cleaning up...")

        try:
            # Stop Appium server
            if self.appium_manager:
                self.appium_manager.stop_server()
                logger.info("✅ Appium server stopped")

            # Conditionally stop the instance
            if not self.keep_running and self.instance_name:
                self.genymotion_manager.stop_instance(self.instance_name)
                logger.info(f"✅ Instance '{self.instance_name}' stopped")
            elif self.keep_running:
                logger.info(f"💡 Instance '{self.instance_name}' kept running as requested")
                logger.info(f"💡 To stop it later, run: gmtool admin stop \"{self.instance_name}\"")

            logger.info("✅ Cleanup completed")
            return True

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            return False


def main():
    """Main function"""
    args = parse_arguments()

    # Setup logging
    setup_logging(args.verbose)

    # Print banner
    logger.info("=" * 70)
    logger.info("🚀 GENYMOTION BROWSER AUTOMATION SCENARIO RUNNER")
    logger.info("=" * 70)
    logger.info(f"📱 Device: {args.device_model}")
    logger.info(f"🤖 Android: {args.android_version}")
    logger.info(f"📝 Instance Name: {args.device_name}")
    logger.info(f"🔍 Search Query: {args.search_query}")
    logger.info(f"🔄 Keep Running: {args.keep_running}")
    logger.info("=" * 70)

    # Get device configuration
    device_config = get_device_config(args.device_model, args.android_version)

    # Create and run scenario
    scenario = CustomGenymotionScenario(
        device_name=args.device_name,
        device_config=device_config,
        search_query=args.search_query,
        keep_running=args.keep_running
    )

    success = scenario.run_complete_scenario()

    if success:
        logger.info("🎉 Scenario completed successfully!")
        if args.keep_running:
            logger.info("💡 The Genymotion instance is still running for further testing")
            logger.info(f"💡 To stop it: gmtool admin stop \"{args.device_name}\"")
    else:
        logger.error("❌ Scenario failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
