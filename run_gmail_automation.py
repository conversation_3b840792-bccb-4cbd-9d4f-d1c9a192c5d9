#!/usr/bin/env python3
"""
Gmail Account Creation Automation Runner
Runs the complete Gmail account creation process with device spoofing
"""

import sys
import os
import json
import argparse
from datetime import datetime
from loguru import logger

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator


def setup_logging(log_level: str = "INFO"):
    """Setup logging configuration"""
    # Remove default logger
    logger.remove()
    
    # Add console logger with colors
    logger.add(
        sys.stdout,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        colorize=True
    )
    
    # Add file logger
    log_file = f"logs/gmail_automation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    os.makedirs("logs", exist_ok=True)
    
    logger.add(
        log_file,
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB"
    )
    
    logger.info(f"📝 Logging to: {log_file}")


def save_result_to_json(result: dict, filename: str = None):
    """Save automation result to JSON file"""
    try:
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"gmail_automation_result_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(result, f, indent=2)
        
        logger.info(f"📄 Result saved to: {filename}")
        
    except Exception as e:
        logger.error(f"❌ Error saving result: {e}")


def run_single_account_creation():
    """Run single Gmail account creation"""
    try:
        logger.info("🚀 Starting Single Gmail Account Creation")
        logger.info("=" * 80)
        
        creator = GmailAccountCreator()
        result = creator.run_complete_automation()
        
        # Save result
        save_result_to_json(result)
        
        # Print summary
        if result['success']:
            logger.info("🎉 GMAIL ACCOUNT CREATION SUCCESSFUL!")
            logger.info(f"📧 Email: {result['email']}")
            logger.info(f"🔑 Password: {result['password']}")
            logger.info(f"👤 Name: {result['first_name']} {result['last_name']}")
            logger.info(f"📅 Birth Date: {result['birth_date']}")
        else:
            logger.error(f"❌ GMAIL ACCOUNT CREATION FAILED: {result['error']}")
        
        logger.info("=" * 80)
        return result
        
    except Exception as e:
        logger.error(f"❌ Single account creation failed: {e}")
        return {'success': False, 'error': str(e)}


def run_batch_account_creation(count: int):
    """Run batch Gmail account creation"""
    try:
        logger.info(f"🚀 Starting Batch Gmail Account Creation ({count} accounts)")
        logger.info("=" * 80)
        
        results = []
        successful_accounts = []
        failed_accounts = []
        
        for i in range(count):
            logger.info(f"📱 Creating account {i+1}/{count}...")
            
            creator = GmailAccountCreator()
            result = creator.run_complete_automation()
            results.append(result)
            
            if result['success']:
                successful_accounts.append(result)
                logger.info(f"✅ Account {i+1} created: {result['email']}")
            else:
                failed_accounts.append(result)
                logger.error(f"❌ Account {i+1} failed: {result['error']}")
            
            # Small delay between accounts
            if i < count - 1:
                logger.info("⏳ Waiting before next account...")
                import time
                time.sleep(30)  # 30 second delay between accounts
        
        # Save batch results
        batch_result = {
            'batch_info': {
                'total_requested': count,
                'successful': len(successful_accounts),
                'failed': len(failed_accounts),
                'success_rate': len(successful_accounts) / count * 100,
                'created_at': datetime.now().isoformat()
            },
            'successful_accounts': successful_accounts,
            'failed_accounts': failed_accounts,
            'all_results': results
        }
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_result_to_json(batch_result, f"gmail_batch_result_{timestamp}.json")
        
        # Print summary
        logger.info("📊 BATCH CREATION SUMMARY")
        logger.info(f"   Total Requested: {count}")
        logger.info(f"   Successful: {len(successful_accounts)}")
        logger.info(f"   Failed: {len(failed_accounts)}")
        logger.info(f"   Success Rate: {len(successful_accounts) / count * 100:.1f}%")
        
        if successful_accounts:
            logger.info("✅ Successful Accounts:")
            for acc in successful_accounts:
                logger.info(f"   📧 {acc['email']} - {acc['first_name']} {acc['last_name']}")
        
        logger.info("=" * 80)
        return batch_result
        
    except Exception as e:
        logger.error(f"❌ Batch account creation failed: {e}")
        return {'success': False, 'error': str(e)}


def test_faker_generation():
    """Test Faker.js data generation"""
    try:
        logger.info("🎭 Testing Faker.js Data Generation")
        logger.info("=" * 50)
        
        creator = GmailAccountCreator()
        
        for i in range(3):
            logger.info(f"🔄 Generating test data {i+1}...")
            info = creator.generate_personal_info()
            
            logger.info(f"   Name: {info.first_name} {info.last_name}")
            logger.info(f"   Username: {info.username}")
            logger.info(f"   Birth Date: {info.birth_date}")
            logger.info(f"   Phone: {info.phone_number}")
            logger.info(f"   Recovery: {info.recovery_email}")
            logger.info("")
        
        logger.info("✅ Faker.js generation test completed")
        
    except Exception as e:
        logger.error(f"❌ Faker test failed: {e}")


def main():
    """Main function with command line arguments"""
    parser = argparse.ArgumentParser(description="Gmail Account Creation Automation")
    parser.add_argument('--mode', choices=['single', 'batch', 'test'], default='single',
                       help='Automation mode (default: single)')
    parser.add_argument('--count', type=int, default=1,
                       help='Number of accounts to create in batch mode (default: 1)')
    parser.add_argument('--log-level', choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'], default='INFO',
                       help='Logging level (default: INFO)')
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logging(args.log_level)
    
    try:
        if args.mode == 'test':
            test_faker_generation()
        elif args.mode == 'single':
            run_single_account_creation()
        elif args.mode == 'batch':
            if args.count < 1:
                logger.error("❌ Batch count must be at least 1")
                return
            run_batch_account_creation(args.count)
        
    except KeyboardInterrupt:
        logger.warning("⚠️ Automation interrupted by user")
    except Exception as e:
        logger.error(f"❌ Automation failed: {e}")


if __name__ == "__main__":
    main()
