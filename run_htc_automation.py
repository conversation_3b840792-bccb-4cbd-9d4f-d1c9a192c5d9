#!/usr/bin/env python3
"""
HTC One Android 14.0 + Google.com Automation Runner

This script runs the complete automation scenario that:
1. Creates an HTC One Android 14.0 virtual device
2. Starts the device and waits for boot completion
3. Launches the default Android browser
4. Navigates to Google.com
5. Performs search automation

Usage:
    python3 run_htc_automation.py [options]

Options:
    --instance-name NAME    Custom name for the virtual device
    --auto                  Run without confirmation prompts
    --keep-running         Keep device running after completion
    --verbose              Enable verbose logging
"""

import asyncio
import argparse
import sys
from pathlib import Path
from loguru import logger

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.automation_scenarios import HTCOneGoogleScenario


def setup_logging(verbose: bool = False):
    """Setup logging configuration"""
    logger.remove()  # Remove default handler
    
    if verbose:
        logger.add(sys.stdout, level="DEBUG", 
                  format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>")
    else:
        logger.add(sys.stdout, level="INFO",
                  format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <level>{message}</level>")


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Run HTC One Android 14.0 + Google.com Automation Scenario",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
    # Run with default settings
    python3 run_htc_automation.py
    
    # Run with custom instance name
    python3 run_htc_automation.py --instance-name "MyHTC"
    
    # Run automatically without prompts
    python3 run_htc_automation.py --auto
    
    # Keep device running after completion
    python3 run_htc_automation.py --keep-running --verbose
        """
    )
    
    parser.add_argument(
        "--instance-name",
        help="Custom name for the virtual device"
    )
    
    parser.add_argument(
        "--auto",
        action="store_true",
        help="Run without confirmation prompts"
    )
    
    parser.add_argument(
        "--keep-running",
        action="store_true",
        help="Keep device running after scenario completion"
    )
    
    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="Enable verbose logging"
    )
    
    return parser.parse_args()


class ProgressTracker:
    """Simple progress tracker for command line output"""
    
    def __init__(self):
        self.current_step = ""
        self.current_progress = 0
    
    def update(self, data):
        """Update progress display"""
        step = data.get("step", "")
        progress = data.get("progress", 0)
        message = data.get("message", "")
        
        if step != self.current_step or progress != self.current_progress:
            self.current_step = step
            self.current_progress = progress
            
            # Create progress bar
            bar_length = 30
            filled_length = int(bar_length * progress // 100)
            bar = '█' * filled_length + '-' * (bar_length - filled_length)
            
            logger.info(f"[{bar}] {progress:3d}% | {step}: {message}")


async def run_automation_scenario(args):
    """Run the complete automation scenario"""
    
    # Create progress tracker
    progress_tracker = ProgressTracker()
    
    # Create scenario instance
    scenario = HTCOneGoogleScenario(
        instance_name=args.instance_name,
        progress_callback=progress_tracker.update
    )
    
    logger.info("🚀 Starting HTC One Android 14.0 + Google.com Automation")
    logger.info("=" * 70)
    logger.info(f"📱 Instance Name: {scenario.instance_name}")
    logger.info(f"🤖 Android Version: 14.0")
    logger.info(f"📱 Device Model: HTC One")
    logger.info(f"🌐 Target: Google.com")
    logger.info("=" * 70)
    
    try:
        # Run the complete scenario
        result = await scenario.run_complete_scenario()
        
        logger.info("\n" + "=" * 70)
        if result["success"]:
            logger.info("🎉 AUTOMATION SCENARIO COMPLETED SUCCESSFULLY!")
            logger.info(f"✅ Device ID: {result.get('device_id', 'N/A')}")
            logger.info(f"✅ Instance Name: {result.get('instance_name', 'N/A')}")
            logger.info(f"✅ Message: {result.get('message', 'Success')}")
            
            if not args.keep_running:
                logger.info("\n💡 Device Management:")
                logger.info(f"   To stop device: gmtool admin stop \"{scenario.instance_name}\"")
                logger.info(f"   To delete device: gmtool admin delete \"{scenario.instance_name}\"")
            else:
                logger.info(f"\n💡 Device '{scenario.instance_name}' is still running for further testing")
                
            logger.info("\n🌐 Browser Automation Results:")
            logger.info("   ✅ HTC One device created and started")
            logger.info("   ✅ Android 14.0 system booted successfully")
            logger.info("   ✅ Default browser launched")
            logger.info("   ✅ Google.com loaded successfully")
            logger.info("   ✅ Search automation completed")
            
        else:
            logger.error("❌ AUTOMATION SCENARIO FAILED!")
            logger.error(f"❌ Error: {result.get('error', 'Unknown error')}")
            
            # Provide troubleshooting tips
            logger.info("\n🔧 Troubleshooting Tips:")
            logger.info("   1. Ensure Genymotion Desktop is installed and running")
            logger.info("   2. Check that you have sufficient system resources (4GB+ RAM)")
            logger.info("   3. Verify ADB is installed and accessible")
            logger.info("   4. Try creating the device manually via Genymotion Desktop first")
            
        logger.info("=" * 70)
        
        return result["success"]
        
    except KeyboardInterrupt:
        logger.warning("\n⚠️ Automation interrupted by user")
        return False
    except Exception as e:
        logger.error(f"\n❌ Automation failed with exception: {e}")
        return False
    finally:
        # Cleanup if needed
        if not args.keep_running:
            try:
                logger.info("🧹 Cleaning up resources...")
                # Note: We don't stop the device by default to allow manual inspection
            except Exception as e:
                logger.warning(f"⚠️ Cleanup warning: {e}")


def main():
    """Main function"""
    args = parse_arguments()
    
    # Setup logging
    setup_logging(args.verbose)
    
    # Print banner
    logger.info("=" * 70)
    logger.info("🤖 HTC ONE ANDROID 14.0 AUTOMATION SCENARIO")
    logger.info("=" * 70)
    logger.warning("⚠️ This will create a REAL virtual device!")
    logger.warning("⚠️ Ensure you have sufficient system resources")
    logger.info("=" * 70)
    
    # Check for auto-run flag
    if not args.auto:
        try:
            response = input("\n🤔 Do you want to proceed with the automation? (y/N): ")
            if response.lower() != 'y':
                logger.info("❌ Automation cancelled by user")
                return
        except KeyboardInterrupt:
            logger.info("\n❌ Automation cancelled by user")
            return
    else:
        logger.info("🚀 Auto-run mode enabled, proceeding with automation...")
    
    # Run the automation scenario
    try:
        success = asyncio.run(run_automation_scenario(args))
        
        if success:
            logger.info("\n🎉 Automation completed successfully!")
            sys.exit(0)
        else:
            logger.error("\n❌ Automation failed!")
            sys.exit(1)
            
    except KeyboardInterrupt:
        logger.info("\n❌ Automation interrupted")
        sys.exit(1)
    except Exception as e:
        logger.error(f"\n❌ Automation error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
