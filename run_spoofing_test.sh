#!/bin/bash

# End-to-End Anti-Detection Spoofing Test Runner
# Production-ready test execution script

echo "🚀 Starting End-to-End Anti-Detection Spoofing Test"
echo "=================================================="

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 is not installed or not in PATH"
    exit 1
fi

# Check if required directories exist
if [ ! -d "src" ]; then
    echo "❌ src directory not found. Please run from project root."
    exit 1
fi

# Set up environment
export PYTHONPATH="${PYTHONPATH}:$(pwd)/src"

# Create logs directory if it doesn't exist
mkdir -p logs

# Generate timestamp for log file
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/spoofing_test_${TIMESTAMP}.log"

echo "📝 Test logs will be saved to: ${LOG_FILE}"
echo ""

# Run the test with logging
echo "🔧 Executing end-to-end spoofing test..."
python3 test_end_to_end_spoofing.py 2>&1 | tee "${LOG_FILE}"

# Capture exit code
EXIT_CODE=${PIPESTATUS[0]}

echo ""
echo "=================================================="

if [ $EXIT_CODE -eq 0 ]; then
    echo "✅ End-to-End Spoofing Test PASSED"
    echo "📊 All anti-detection measures are working correctly"
else
    echo "❌ End-to-End Spoofing Test FAILED"
    echo "🔍 Check the logs for detailed error information"
fi

echo "📝 Full test log: ${LOG_FILE}"
echo "📄 Test results JSON files are saved in the current directory"
echo "=================================================="

exit $EXIT_CODE
