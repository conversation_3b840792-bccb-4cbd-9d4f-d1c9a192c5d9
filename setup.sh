#!/bin/bash

# BlueStacks Automation Setup Script

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Setting up BlueStacks Automation System..."

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    print_error "Python 3 is not installed. Please install Python 3.8+ first."
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
print_status "Python version: $PYTHON_VERSION"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    print_status "Creating virtual environment..."
    python3 -m venv venv
    if [ $? -eq 0 ]; then
        print_success "Virtual environment created successfully"
    else
        print_error "Failed to create virtual environment"
        exit 1
    fi
else
    print_status "Virtual environment already exists"
fi

# Activate virtual environment
print_status "Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
print_status "Upgrading pip..."
pip install --upgrade pip

# Install requirements
print_status "Installing Python dependencies..."
pip install -r requirements.txt

if [ $? -eq 0 ]; then
    print_success "Dependencies installed successfully"
else
    print_error "Failed to install dependencies"
    exit 1
fi

# Create .env file if it doesn't exist
if [ ! -f ".env" ]; then
    print_status "Creating .env file from template..."
    cp .env.example .env
    print_success ".env file created"
    print_warning "Please edit .env file with your specific configurations"
else
    print_status ".env file already exists"
fi

# Create logs directory
mkdir -p logs
print_status "Created logs directory"

# Check for Node.js and Appium
print_status "Checking dependencies..."

if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js found: $NODE_VERSION"
    
    if command -v appium &> /dev/null; then
        APPIUM_VERSION=$(appium --version)
        print_success "Appium found: $APPIUM_VERSION"
    else
        print_warning "Appium not found. Install with: npm install -g appium"
        print_warning "Also install: npm install -g appium-doctor"
    fi
else
    print_warning "Node.js not found. Please install Node.js first"
    print_warning "Then install Appium: npm install -g appium"
fi

# Check for ADB
if command -v adb &> /dev/null; then
    print_success "ADB found"
else
    print_warning "ADB not found. Please install Android SDK Platform Tools"
fi

# Check for BlueStacks
BLUESTACKS_PATH="/Applications/BlueStacks.app"
if [ -d "$BLUESTACKS_PATH" ]; then
    print_success "BlueStacks found at $BLUESTACKS_PATH"
else
    print_warning "BlueStacks not found at default location"
    print_warning "Please install BlueStacks or update BLUESTACKS_PATH in .env"
fi

# Make run script executable
chmod +x run.sh
print_success "Made run.sh executable"

print_success "Setup completed successfully!"
echo
print_status "Next steps:"
echo "1. Edit .env file with your configurations"
echo "2. Ensure BlueStacks is installed and running"
echo "3. Install Appium if not already installed: npm install -g appium"
echo "4. Run the system: ./run.sh --mode dashboard"
echo
print_status "Quick start commands:"
echo "  ./run.sh --mode dashboard    # Start web dashboard"
echo "  ./run.sh --mode example      # Run automation example"
echo "  ./run.sh --mode test         # Run system tests"