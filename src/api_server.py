from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import json
import asyncio
import uuid
import re
import time
from datetime import datetime
import os
from loguru import logger

from database import DatabaseManager
from device_manager import DeviceProfileManager
from automation_client import StealthAutomationClient
from location_spoofer import LocationSpoofer
from gmail_account_creator import GmailAccountCreator


class TaskRequest(BaseModel):
    task_type: str
    parameters: Dict[str, Any]
    app_package: Optional[str] = None


class SessionRequest(BaseModel):
    app_package: Optional[str] = None
    instance_name: str = "Pixel"


class LocationRequest(BaseModel):
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    city_name: Optional[str] = None


class GmailCreationRequest(BaseModel):
    count: Optional[int] = 1
    use_turkish_data: Optional[bool] = True
    cleanup_device: Optional[bool] = True
    save_to_file: Optional[bool] = True


class GmailBatchRequest(BaseModel):
    count: int
    delay_between_accounts: Optional[int] = 30  # seconds
    use_turkish_data: Optional[bool] = True
    cleanup_devices: Optional[bool] = True


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("WebSocket client connected")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info("WebSocket client disconnected")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: Dict):
        if self.active_connections:
            message_str = json.dumps(message)
            for connection in self.active_connections:
                try:
                    await connection.send_text(message_str)
                except:
                    # Remove broken connections
                    self.active_connections.remove(connection)


app = FastAPI(title="Genymotion Automation Dashboard", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize managers
db_manager = DatabaseManager()
device_manager = DeviceProfileManager()
automation_client = None
location_spoofer = LocationSpoofer()
connection_manager = ConnectionManager()


def get_automation_client():
    global automation_client
    if automation_client is None:
        automation_client = StealthAutomationClient(device_manager)
    return automation_client


@app.get("/")
async def read_root():
    """Serve the simplified dashboard HTML"""
    return FileResponse("dashboard/index.html")


@app.get("/api/status")
async def get_status():
    """Get overall system status"""
    client = get_automation_client()

    return {
        "appium_server": device_manager.appium_manager.get_server_status(),
        "genymotion": {
            "running": True,  # This should be checked dynamically
            "current_profile": device_manager.genymotion_manager.get_current_profile()
        },
        "automation_client": {
            "active": client.current_session_id is not None,
            "session_id": client.current_session_id,
            "action_count": client.action_count
        },
        "database": {
            "connected": True,  # Should check database connection
            "active_sessions": len(db_manager.get_active_sessions())
        }
    }


@app.get("/api/sessions")
async def get_sessions():
    """Get all active and recent sessions"""
    active_sessions = db_manager.get_active_sessions()
    session_history = db_manager.get_session_history(limit=20)

    return {
        "active_sessions": active_sessions,
        "session_history": session_history
    }


@app.get("/api/sessions/{session_id}")
async def get_session_details(session_id: str):
    """Get detailed information about a specific session"""
    session_logs = db_manager.get_session_logs(session_id, limit=100)
    session_tasks = db_manager.get_session_tasks(session_id)

    return {
        "session_id": session_id,
        "logs": session_logs,
        "tasks": session_tasks
    }


@app.post("/api/sessions/start")
async def start_automation_session(request: SessionRequest):
    """Start a new automation session"""
    try:
        client = get_automation_client()

        success = client.start_session(request.app_package, request.instance_name)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to start automation session")

        # Log session creation in database
        session_info = client.get_session_info(for_database=True)
        if session_info:
            db_success = db_manager.create_automation_session(session_info)
            if db_success:
                db_manager.log_automation_event(
                    session_info['session_id'],
                    "INFO",
                    "Automation session started"
                )
            else:
                logger.warning("Failed to log session to database, but continuing with session")
        else:
            logger.warning("No session info available for database logging")

        # Get JSON-serializable session info for API response
        api_session_info = client.get_session_info(for_database=False)  # This returns ISO strings

        # Broadcast update to WebSocket clients
        await connection_manager.broadcast({
            "type": "session_started",
            "data": api_session_info
        })

        return {"success": True, "session_info": api_session_info}

    except Exception as e:
        logger.error(f"Failed to start session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/stop")
async def stop_automation_session():
    """Stop the current automation session"""
    try:
        client = get_automation_client()

        if client.current_session_id:
            session_id = client.current_session_id
            client.end_session()

            # Update database
            db_manager.update_session_status(session_id, "completed", datetime.utcnow())
            db_manager.log_automation_event(session_id, "INFO", "Automation session stopped")

            # Broadcast update
            await connection_manager.broadcast({
                "type": "session_stopped",
                "data": {"session_id": session_id}
            })

            return {"success": True, "message": "Session stopped"}
        else:
            return {"success": False, "message": "No active session"}

    except Exception as e:
        logger.error(f"Failed to stop session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/kill-all")
async def kill_all_sessions():
    """Kill all active automation sessions and clean up resources"""
    try:
        client = get_automation_client()
        killed_sessions = []

        # Get all active sessions from database
        active_sessions = db_manager.get_active_sessions()

        # Stop current session if exists
        if client.current_session_id:
            session_id = client.current_session_id
            client.end_session()
            killed_sessions.append(session_id)
            logger.info(f"Stopped current session: {session_id}")

        # End all active sessions in database
        for session in active_sessions:
            session_id = session.get('session_id')
            if session_id:
                db_manager.end_automation_session(session_id)
                db_manager.log_automation_event(session_id, "INFO", "Session killed by kill-all command")
                if session_id not in killed_sessions:
                    killed_sessions.append(session_id)

        # Kill Appium server to ensure clean state
        try:
            appium_manager = client.device_manager.appium_manager
            if appium_manager.is_running:
                appium_manager.stop_server()
                logger.info("Stopped Appium server")
        except Exception as appium_error:
            logger.warning(f"Failed to stop Appium server: {appium_error}")

        # Clear any device manager sessions
        try:
            client.device_manager.active_sessions.clear()
            logger.info("Cleared device manager sessions")
        except Exception as device_error:
            logger.warning(f"Failed to clear device sessions: {device_error}")

        # Reset automation client
        client.current_session_id = None
        client.driver = None

        # Broadcast update
        await connection_manager.broadcast({
            "type": "all_sessions_killed",
            "data": {
                "killed_sessions": killed_sessions,
                "count": len(killed_sessions)
            }
        })

        logger.info(f"Killed {len(killed_sessions)} sessions: {killed_sessions}")

        return {
            "success": True,
            "message": f"Killed {len(killed_sessions)} sessions",
            "killed_sessions": killed_sessions
        }

    except Exception as e:
        logger.error(f"Failed to kill all sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/rotate")
async def rotate_automation_session(request: SessionRequest):
    """Rotate the current automation session for anti-detection"""
    try:
        client = get_automation_client()

        if not client.current_session_id:
            return await start_automation_session(request)

        old_session_id = client.current_session_id
        success = client.rotate_session(request.app_package, request.instance_name)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to rotate session")

        # Update database
        db_manager.update_session_status(old_session_id, "rotated", datetime.utcnow())

        new_session_info = client.get_session_info(for_database=True)
        if new_session_info:
            db_manager.create_automation_session(new_session_info)
            db_manager.log_automation_event(
                new_session_info['session_id'],
                "INFO",
                f"Session rotated from {old_session_id}"
            )

        # Get JSON-serializable session info for API response
        api_session_info = client.get_session_info(for_database=False)  # This returns ISO strings

        # Broadcast update
        await connection_manager.broadcast({
            "type": "session_rotated",
            "data": {
                "old_session_id": old_session_id,
                "new_session_info": api_session_info
            }
        })

        return {"success": True, "session_info": api_session_info}

    except Exception as e:
        logger.error(f"Failed to rotate session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/tasks/execute")
async def execute_task(request: TaskRequest):
    """Execute a specific automation task"""
    try:
        client = get_automation_client()

        if not client.current_session_id:
            raise HTTPException(status_code=400, detail="No active automation session")

        task_id = str(uuid.uuid4())
        task_name = f"{request.task_type}_{int(datetime.now().timestamp())}"

        # Log task creation
        task_data = {
            "task_id": task_id,
            "session_id": client.current_session_id,
            "task_name": task_name,
            "task_type": request.task_type,
            "parameters": request.parameters,
            "start_time": datetime.utcnow()
        }

        db_manager.create_automation_task(task_data)
        db_manager.update_task_status(task_id, "running")

        # Execute task based on type
        result = await execute_automation_task(client, request.task_type, request.parameters)

        # Update task status
        end_time = datetime.utcnow()
        if result["success"]:
            db_manager.update_task_status(task_id, "completed", end_time)
        else:
            db_manager.update_task_status(task_id, "failed", end_time, result.get("error"))

        # Log task completion
        db_manager.log_automation_event(
            client.current_session_id,
            "INFO" if result["success"] else "ERROR",
            f"Task {request.task_type} {'completed' if result['success'] else 'failed'}",
            task_id
        )

        # Broadcast update
        await connection_manager.broadcast({
            "type": "task_completed",
            "data": {
                "task_id": task_id,
                "task_type": request.task_type,
                "success": result["success"],
                "result": result
            }
        })

        return {"success": True, "task_id": task_id, "result": result}

    except Exception as e:
        logger.error(f"Failed to execute task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_automation_task(client: StealthAutomationClient, task_type: str, parameters: Dict) -> Dict:
    """Execute the actual automation task"""
    try:
        if task_type == "tap":
            x = parameters.get("x")
            y = parameters.get("y")
            if x is None or y is None:
                return {"success": False, "error": "Missing x or y coordinates"}

            success = client.human_tap(x, y)
            return {"success": success}

        elif task_type == "swipe":
            start_x = parameters.get("start_x")
            start_y = parameters.get("start_y")
            end_x = parameters.get("end_x")
            end_y = parameters.get("end_y")

            if None in [start_x, start_y, end_x, end_y]:
                return {"success": False, "error": "Missing swipe coordinates"}

            success = client.human_swipe(start_x, start_y, end_x, end_y)
            return {"success": success}

        elif task_type == "scroll":
            direction = parameters.get("direction", "down")
            success = client.human_scroll(direction)
            return {"success": success}

        elif task_type == "type":
            text = parameters.get("text")
            if not text:
                return {"success": False, "error": "Missing text parameter"}

            success = client.human_type(text)
            return {"success": success}

        elif task_type == "random_activity":
            duration = parameters.get("duration", 30)
            client.perform_random_human_activity(duration)
            return {"success": True}

        else:
            return {"success": False, "error": f"Unknown task type: {task_type}"}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/api/location/set")
async def set_location(request: LocationRequest):
    """Set device location"""
    try:
        if request.city_name:
            success = location_spoofer.set_city_location(request.city_name)
        elif request.latitude is not None and request.longitude is not None:
            success = location_spoofer.set_location(request.latitude, request.longitude)
        else:
            success = location_spoofer.set_random_location()

        if success:
            current_location = location_spoofer.get_current_location()

            # Save to database if there's an active session
            client = get_automation_client()
            if client.current_session_id and current_location:
                db_manager.save_location_point({
                    "session_id": client.current_session_id,
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude,
                    "altitude": current_location.altitude,
                    "accuracy": current_location.accuracy
                })

            # Broadcast update
            await connection_manager.broadcast({
                "type": "location_updated",
                "data": {
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude
                }
            })

            return {"success": True, "location": current_location.__dict__}
        else:
            raise HTTPException(status_code=500, detail="Failed to set location")

    except Exception as e:
        logger.error(f"Failed to set location: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/logs")
async def get_logs(limit: int = 100, level: Optional[str] = None):
    """Get recent logs"""
    logs = db_manager.get_recent_logs(limit=limit, level=level)
    return {"logs": logs}


@app.get("/api/device-profiles")
async def get_device_profiles():
    """Get available device profiles"""
    try:
        config_path = "config/device_profiles.yaml"

        # Try relative to current directory first
        if os.path.exists(config_path):
            config_file = config_path
        else:
            # Try relative to project root (one level up from src)
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(project_root, config_path)

        with open(config_file, 'r') as f:
            import yaml
            config = yaml.safe_load(f)

        return {
            "device_profiles": list(config.get('device_profiles', {}).keys()),
            "locations": list(config.get('locations', {}).keys())
        }
    except Exception as e:
        logger.error(f"Failed to get device profiles: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/instances")
async def get_instances():
    """Get available Genymotion instances"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager
        instances = genymotion_manager.get_available_instances()

        return {
            "success": True,
            "instances": instances
        }
    except Exception as e:
        logger.error(f"Failed to get instances: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/devices/running")
async def list_running_devices():
    """List only running Genymotion devices for Gmail automation"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager
        instances = genymotion_manager.get_available_instances()

        # Filter for running devices
        running_devices = []
        for instance in instances:
            if instance.get('state') == 'On':
                # Get additional info for running devices
                device_info = {
                    'name': instance.get('name'),
                    'state': instance.get('state'),
                    'android_version': instance.get('android_version', 'Android 14.0'),
                    'adb_id': genymotion_manager.get_device_adb_id(instance.get('name')),
                    'suitable_for_gmail': True  # All running devices are suitable
                }
                running_devices.append(device_info)

        return {
            "running_devices": running_devices,
            "count": len(running_devices)
        }
    except Exception as e:
        logger.error(f"Failed to list running devices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances")
async def create_instance(request: dict):
    """Create a new Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        instance_name = request.get("name", f"Device_{int(time.time())}")
        device_profile = request.get("device_profile", "Custom Phone")
        android_version = request.get("android_version", "14")  # Only Android 14.0 image available

        # Extract clean hardware profile name for MODEL property (without resolution info)
        clean_hardware_profile = device_profile  # This is already the clean name from the API

        logger.info(f"Creating instance: {instance_name}")

        # Broadcast progress update
        await connection_manager.broadcast({
            "type": "device_creation_progress",
            "data": {
                "instance_name": instance_name,
                "stage": "creating",
                "message": f"Creating device '{instance_name}'..."
            }
        })

        success = genymotion_manager.create_new_instance(instance_name, android_version)

        if success:
            # Broadcast creation success
            await connection_manager.broadcast({
                "type": "device_creation_progress",
                "data": {
                    "instance_name": instance_name,
                    "stage": "created",
                    "message": f"Device '{instance_name}' created successfully. Setting device properties..."
                }
            })

            # Set Genymotion system properties BEFORE starting the device
            logger.info(f"Setting Genymotion system properties for: {instance_name}")
            logger.info(f"Using hardware profile name for MODEL: {clean_hardware_profile}")
            genymotion_manager._set_genymotion_model_properties(instance_name, clean_hardware_profile)

            # Generate phone number for this device
            phone_number = genymotion_manager.generate_random_phone_number()
            logger.info(f"Generated phone number for device: {phone_number}")

            # Broadcast starting
            await connection_manager.broadcast({
                "type": "device_creation_progress",
                "data": {
                    "instance_name": instance_name,
                    "stage": "starting",
                    "message": f"Device properties set. Starting device '{instance_name}'..."
                }
            })

            # Start the device automatically after creation for customization
            logger.info(f"Starting instance for customization: {instance_name}")
            start_success = genymotion_manager.start_instance_by_name(instance_name)

            if start_success:
                # Customize all device identifiers immediately after starting (before full Android boot)
                logger.info(f"Customizing device identifiers immediately after device start")
                logger.info(f"Phone number: {phone_number}")
                genymotion_manager.customize_device_identifiers(
                    instance_name,
                    randomize_all=True,  # Randomize Android ID and IMEI for anti-detection
                    phone_number=phone_number
                )

            if start_success:
                # Broadcast device started
                await connection_manager.broadcast({
                    "type": "device_creation_progress",
                    "data": {
                        "instance_name": instance_name,
                        "stage": "started",
                        "message": f"Device '{instance_name}' started successfully. Waiting for Android to boot..."
                    }
                })

                # Wait a moment for device to be fully ready
                import asyncio
                await asyncio.sleep(5)

                # Broadcast customization start
                await connection_manager.broadcast({
                    "type": "device_creation_progress",
                    "data": {
                        "instance_name": instance_name,
                        "stage": "customizing",
                        "message": f"Customizing device properties for '{instance_name}'..."
                    }
                })

                # Automatically customize device properties after creation and starting
                try:
                    # Use the pre-generated phone number
                    customization_success = genymotion_manager.customize_android_device_properties(
                        instance_name, instance_name, phone_number
                    )
                    if customization_success:
                        # Broadcast reboot start
                        await connection_manager.broadcast({
                            "type": "device_creation_progress",
                            "data": {
                                "instance_name": instance_name,
                                "stage": "rebooting",
                                "message": f"Android properties set. Rebooting '{instance_name}' to apply changes..."
                            }
                        })

                        # Wait additional time for device reboot to complete
                        logger.info(f"Waiting for device reboot to complete...")
                        await asyncio.sleep(30)  # Wait for reboot to complete

                        # Restart the device to ensure Genymotion properties take effect
                        await connection_manager.broadcast({
                            "type": "device_creation_progress",
                            "data": {
                                "instance_name": instance_name,
                                "stage": "restarting",
                                "message": f"Restarting '{instance_name}' to apply all device properties..."
                            }
                        })

                        # Stop and start the device to ensure all properties are applied
                        logger.info(f"Restarting device to apply all properties: {instance_name}")
                        genymotion_manager.stop_instance(instance_name)
                        await asyncio.sleep(5)  # Wait for stop
                        genymotion_manager.start_instance_by_name(instance_name)
                        await asyncio.sleep(10)  # Wait for restart

                        # Broadcast completion
                        await connection_manager.broadcast({
                            "type": "device_creation_progress",
                            "data": {
                                "instance_name": instance_name,
                                "stage": "completed",
                                "message": f"Device '{instance_name}' is ready! Custom name, phone number, and model applied."
                            }
                        })

                        logger.info(f"Device properties customized for {instance_name}: name={instance_name}, phone={phone_number}")
                        return {
                            "success": True,
                            "message": f"Instance '{instance_name}' created, customized, and rebooted successfully",
                            "instance_name": instance_name,
                            "device_name": instance_name,
                            "phone_number": phone_number
                        }
                    else:
                        # Broadcast customization failure
                        await connection_manager.broadcast({
                            "type": "device_creation_progress",
                            "data": {
                                "instance_name": instance_name,
                                "stage": "error",
                                "message": f"Failed to customize device properties for '{instance_name}'. Device created but not customized."
                            }
                        })

                        logger.warning(f"Failed to customize device properties for {instance_name}")
                        return {
                            "success": True,
                            "message": f"Instance '{instance_name}' created successfully (customization failed)",
                            "instance_name": instance_name
                        }
                except Exception as e:
                    # Broadcast customization error
                    await connection_manager.broadcast({
                        "type": "device_creation_progress",
                        "data": {
                            "instance_name": instance_name,
                            "stage": "error",
                            "message": f"Customization error for '{instance_name}': {str(e)}"
                        }
                    })

                    logger.warning(f"Device customization failed for {instance_name}: {e}")
                    return {
                        "success": True,
                        "message": f"Instance '{instance_name}' created successfully (customization failed)",
                        "instance_name": instance_name
                    }
            else:
                # Broadcast start failure
                await connection_manager.broadcast({
                    "type": "device_creation_progress",
                    "data": {
                        "instance_name": instance_name,
                        "stage": "error",
                        "message": f"Failed to start device '{instance_name}' for customization. Device created but not started."
                    }
                })

                logger.warning(f"Failed to start instance for customization: {instance_name}")
                return {
                    "success": True,
                    "message": f"Instance '{instance_name}' created successfully (auto-start failed)",
                    "instance_name": instance_name
                }
        else:
            # Broadcast creation failure
            await connection_manager.broadcast({
                "type": "device_creation_progress",
                "data": {
                    "instance_name": instance_name,
                    "stage": "error",
                    "message": f"Failed to create device '{instance_name}'. Please check Genymotion Desktop."
                }
            })

            raise HTTPException(status_code=500, detail="Failed to create instance")

    except Exception as e:
        logger.error(f"Error creating instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/customize")
async def customize_device_properties(instance_name: str, request: dict):
    """Customize Android device properties like device name and phone number"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        device_name = request.get("device_name")
        phone_number = request.get("phone_number")

        logger.info(f"Customizing device properties for: {instance_name}")

        # Generate random phone number if not provided
        if phone_number is None:
            phone_number = genymotion_manager.generate_random_phone_number()

        # Use instance name as device name if not provided
        if device_name is None:
            device_name = instance_name

        success = genymotion_manager.customize_android_device_properties(
            instance_name, device_name, phone_number
        )

        if success:
            return {
                "success": True,
                "message": f"Device properties customized for '{instance_name}'",
                "device_name": device_name,
                "phone_number": phone_number
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to customize device properties")

    except Exception as e:
        logger.error(f"Error customizing device properties: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/instances/{instance_name}")
async def delete_instance(instance_name: str):
    """Delete a Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        logger.info(f"Deleting instance: {instance_name}")

        # Stop the instance first if it's running
        stop_result = genymotion_manager._execute_gmtool(['admin', 'stop', instance_name])

        # Delete the instance
        delete_result = genymotion_manager._execute_gmtool(['admin', 'delete', instance_name])

        if delete_result and delete_result.returncode == 0:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' deleted successfully"
            }
        else:
            error_msg = delete_result.stderr if delete_result else "Unknown error"
            raise HTTPException(status_code=500, detail=f"Failed to delete instance: {error_msg}")

    except Exception as e:
        logger.error(f"Error deleting instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))





@app.post("/api/instances/{instance_name}/stop")
async def stop_instance(instance_name: str):
    """Stop a Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        logger.info(f"Stopping instance: {instance_name}")

        success = genymotion_manager.stop_instance(instance_name)

        if success:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' stopped successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to stop instance")

    except Exception as e:
        logger.error(f"Error stopping instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/proxy")
async def set_instance_proxy(instance_name: str, request: dict):
    """Set proxy for a Genymotion instance"""
    try:
        proxy = request.get("proxy", "")

        logger.info(f"Setting proxy for {instance_name}: {proxy}")

        # For now, just store the proxy setting
        # In a real implementation, you would configure the device proxy
        return {
            "success": True,
            "message": f"Proxy set for '{instance_name}': {proxy or 'none'}"
        }

    except Exception as e:
        logger.error(f"Error setting proxy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/hardware-profiles")
async def get_hardware_profiles():
    """Get available hardware profiles from Genymotion"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        # Get hardware profiles using gmtool
        result = genymotion_manager._execute_gmtool(['admin', 'hwprofiles'])

        profiles = []
        if result and result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines:
                # Skip header and separator lines
                if (line.strip() and
                    not line.startswith('UUID') and
                    not line.startswith('----') and
                    len(line.split()) >= 4):

                    # Parse the line using fixed-width parsing
                    # Format: UUID (36 chars + 2 spaces) NAME (variable) DISPLAY (variable) SOURCE
                    if len(line) > 40:  # Minimum line length
                        uuid = line[:36].strip()
                        rest_of_line = line[36:].strip()

                        # Split the rest and find where display specs start (look for resolution pattern)
                        parts = rest_of_line.split()
                        display_start = -1

                        # Look for pattern like "1080 x 1920 dpi 480"
                        for i in range(len(parts) - 3):
                            if (parts[i].isdigit() and
                                parts[i + 1] == 'x' and
                                parts[i + 2].isdigit() and
                                i + 3 < len(parts) and
                                'dpi' in parts[i + 3]):
                                display_start = i
                                break

                        if display_start > 0:
                            name = ' '.join(parts[:display_start]).strip()
                            # Get display info (resolution + dpi + number)
                            display_end = display_start + 4 if display_start + 4 < len(parts) else len(parts)
                            display = ' '.join(parts[display_start:display_end])

                            if name and uuid:  # Only add if we have valid name and uuid
                                profiles.append({
                                    'name': name,
                                    'display': display,
                                    'uuid': uuid
                                })

        # Add fallback profiles if none found
        if not profiles:
            profiles = [
                {'name': 'Custom Phone', 'display': '768 x 1280 dpi 320', 'uuid': 'fallback'},
                {'name': 'Google Pixel', 'display': '1080 x 1920 dpi 420', 'uuid': 'fallback'},
                {'name': 'HTC One', 'display': '1080 x 1920 dpi 480', 'uuid': 'fallback'}
            ]

        return {
            "success": True,
            "profiles": profiles
        }

    except Exception as e:
        logger.error(f"Error getting hardware profiles: {e}")
        # Return fallback profiles on error
        return {
            "success": True,
            "profiles": [
                {'name': 'Custom Phone', 'display': '768 x 1280 dpi 320', 'uuid': 'fallback'},
                {'name': 'Google Pixel', 'display': '1080 x 1920 dpi 420', 'uuid': 'fallback'},
                {'name': 'HTC One', 'display': '1080 x 1920 dpi 480', 'uuid': 'fallback'}
            ]
        }


@app.post("/api/instances/{instance_name}/start")
async def start_instance(instance_name: str):
    """Start a specific Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        # Check if instance already exists and is running
        instances = genymotion_manager.get_available_instances()
        if instance_name in instances:
            instance_info = instances[instance_name]
            if instance_info.get('status') == 'running':
                return {
                    "success": True,
                    "message": f"Instance {instance_name} is already running"
                }

        success = genymotion_manager.start_instance_by_name(instance_name)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_started",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance {instance_name} started successfully"
            }
        else:
            # If starting failed, provide helpful message
            return {
                "success": False,
                "message": f"Failed to start instance '{instance_name}'. Instance may not exist. Please create it first using Genymotion Desktop."
            }

    except Exception as e:
        logger.error(f"Failed to start instance {instance_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/stop")
async def stop_instance(instance_name: str):
    """Stop a specific Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.stop_instance(instance_name)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_stopped",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance {instance_name} stopped successfully"
            }
        else:
            return {
                "success": False,
                "message": f"Failed to stop instance {instance_name}"
            }

    except Exception as e:
        logger.error(f"Failed to stop instance {instance_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/stop-all")
async def stop_all_instances():
    """Stop all running Genymotion instances"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager
        instances = genymotion_manager.get_available_instances()

        stopped_instances = []
        for instance_name, instance_info in instances.items():
            if instance_info.get('status') == 'running':
                success = genymotion_manager.stop_instance(instance_name)
                if success:
                    stopped_instances.append(instance_name)

        # Broadcast update
        await connection_manager.broadcast({
            "type": "all_instances_stopped",
            "data": {"stopped_instances": stopped_instances}
        })

        return {
            "success": True,
            "message": f"Stopped {len(stopped_instances)} instances",
            "stopped_instances": stopped_instances
        }

    except Exception as e:
        logger.error(f"Failed to stop all instances: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/open-manager")
async def open_genymotion_manager():
    """Open Genymotion Desktop application"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.open_genymotion_manager()

        if success:
            return {
                "success": True,
                "message": "Genymotion Desktop opened successfully"
            }
        else:
            return {
                "success": False,
                "message": "Failed to open Genymotion Desktop. The application may not be installed. Please install Genymotion from https://www.genymotion.com/",
                "alternatives": [
                    "Install Genymotion Desktop from https://www.genymotion.com/",
                    "Open Genymotion from Applications folder",
                    "Check Genymotion installation"
                ]
            }

    except Exception as e:
        logger.error(f"Failed to open Multi-Instance Manager: {e}")
        return {
            "success": False,
            "message": f"Error opening Multi-Instance Manager: {str(e)}",
            "alternatives": [
                "Open Genymotion manually from Applications",
                "Restart your Mac if Genymotion is unresponsive",
                "Reinstall Genymotion if issues persist"
            ]
        }


@app.post("/api/instances/create")
async def create_new_instance(request: dict):
    """Create a new Genymotion instance programmatically"""
    try:
        instance_name = request.get('instance_name', '').strip()
        android_version = request.get('android_version', '14')  # Only Android 14.0 image available

        if not instance_name:
            return {
                "success": False,
                "message": "Instance name is required"
            }

        # Validate instance name
        if not re.match(r'^[a-zA-Z0-9_-]+$', instance_name):
            return {
                "success": False,
                "message": "Instance name can only contain letters, numbers, underscores, and hyphens"
            }

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        # Check if instance already exists
        instances = genymotion_manager.get_available_instances()
        if instance_name in instances:
            return {
                "success": False,
                "message": f"Instance '{instance_name}' already exists"
            }

        success = genymotion_manager.create_new_instance(instance_name, android_version)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_created",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance '{instance_name}' created successfully"
            }
        else:
            return {
                "success": False,
                "message": f"Failed to create instance '{instance_name}'. Please create it manually using Genymotion Desktop.",
                "instructions": [
                    "1. Open Genymotion Desktop from Applications folder",
                    "2. Click the '+' button to add a new virtual device",
                    "3. Choose a device template or create custom",
                    f"4. Name the instance '{instance_name}'",
                    "5. Select Android version and settings",
                    "6. Click Create and wait for completion"
                ]
            }

    except Exception as e:
        logger.error(f"Failed to create instance: {e}")
        return {
            "success": False,
            "message": f"Error creating instance: {str(e)}"
        }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await connection_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo received data for now
            await connection_manager.send_personal_message(f"Received: {data}", websocket)
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)


# Genymotion-specific advanced features
@app.post("/api/genymotion/gps")
async def set_gps_location(request: dict):
    """Set GPS location using Genymotion Shell"""
    try:
        latitude = request.get('latitude')
        longitude = request.get('longitude')
        device_name = request.get('device_name')

        if latitude is None or longitude is None:
            return {"success": False, "message": "Latitude and longitude are required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.set_gps_location(float(latitude), float(longitude), device_name)

        if success:
            return {
                "success": True,
                "message": f"GPS location set to {latitude}, {longitude}"
            }
        else:
            return {
                "success": False,
                "message": "Failed to set GPS location"
            }
    except Exception as e:
        logger.error(f"Failed to set GPS location: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/genymotion/battery")
async def set_battery_level(request: dict):
    """Set battery level using Genymotion Shell"""
    try:
        level = request.get('level')
        device_name = request.get('device_name')

        if level is None:
            return {"success": False, "message": "Battery level is required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.set_battery_level(int(level), device_name)

        if success:
            return {
                "success": True,
                "message": f"Battery level set to {level}%"
            }
        else:
            return {
                "success": False,
                "message": "Failed to set battery level"
            }
    except Exception as e:
        logger.error(f"Failed to set battery level: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/genymotion/rotate")
async def rotate_device(request: dict):
    """Rotate device using Genymotion Shell"""
    try:
        orientation = request.get('orientation')
        device_name = request.get('device_name')

        if not orientation:
            return {"success": False, "message": "Orientation is required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.rotate_device(orientation, device_name)

        if success:
            return {
                "success": True,
                "message": f"Device rotated to {orientation}"
            }
        else:
            return {
                "success": False,
                "message": "Failed to rotate device"
            }
    except Exception as e:
        logger.error(f"Failed to rotate device: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Automation Scenarios API
@app.post("/api/scenarios/htc-one-google")
async def run_htc_one_google_scenario(request: dict):
    """Run complete HTC One + Google.com automation scenario"""
    try:
        instance_name = request.get('instance_name')

        # Import here to avoid circular imports
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from src.automation_scenarios import HTCOneGoogleScenario

        # Create progress tracking
        progress_data = {"current": {}}

        def progress_callback(data):
            progress_data["current"] = data
            # Broadcast progress to WebSocket clients
            asyncio.create_task(connection_manager.broadcast({
                "type": "scenario_progress",
                "scenario": "htc_one_google",
                "data": data
            }))

        # Create and run scenario
        scenario = HTCOneGoogleScenario(
            instance_name=instance_name,
            progress_callback=progress_callback
        )

        # Run scenario
        result = await scenario.run_complete_scenario()

        # Broadcast completion
        await connection_manager.broadcast({
            "type": "scenario_complete",
            "scenario": "htc_one_google",
            "result": result
        })

        return result

    except Exception as e:
        logger.error(f"Failed to run HTC One scenario: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/scenarios/available")
async def get_available_scenarios():
    """Get list of available automation scenarios"""
    scenarios = [
        {
            "id": "htc_one_google",
            "name": "HTC One + Google.com",
            "description": "Create HTC One Android 14.0 device and perform Google search automation",
            "duration": "2-4 minutes",
            "requirements": ["4GB+ RAM", "Genymotion Desktop", "ADB"],
            "steps": [
                "Create HTC One virtual device",
                "Start device and wait for boot",
                "Launch default Android browser",
                "Navigate to Google.com",
                "Perform search automation"
            ]
        }
    ]

    return {"scenarios": scenarios}


# Gmail Account Creation API
@app.post("/api/gmail/create")
async def create_gmail_account(request: GmailCreationRequest):
    """Create a single Gmail account with device spoofing"""
    try:
        logger.info(f"🚀 API: Starting Gmail account creation (count: {request.count})")

        # Initialize Gmail creator
        gmail_creator = GmailAccountCreator()

        results = []
        successful_accounts = []
        failed_accounts = []

        for i in range(request.count):
            logger.info(f"📱 API: Creating account {i+1}/{request.count}")

            # Run the automation
            result = gmail_creator.run_complete_automation()
            results.append(result)

            if result['success']:
                successful_accounts.append(result)
                logger.info(f"✅ API: Account {i+1} created successfully: {result['email']}")

                # Broadcast success to WebSocket clients
                await connection_manager.broadcast({
                    "type": "gmail_account_created",
                    "data": {
                        "email": result['email'],
                        "name": f"{result['first_name']} {result['last_name']}",
                        "account_number": i+1,
                        "total_accounts": request.count
                    }
                })
            else:
                failed_accounts.append(result)
                logger.error(f"❌ API: Account {i+1} creation failed: {result['error']}")

                # Broadcast failure to WebSocket clients
                await connection_manager.broadcast({
                    "type": "gmail_account_failed",
                    "data": {
                        "error": result['error'],
                        "account_number": i+1,
                        "total_accounts": request.count
                    }
                })

        # Prepare response
        response = {
            "success": len(successful_accounts) > 0,
            "total_requested": request.count,
            "successful_count": len(successful_accounts),
            "failed_count": len(failed_accounts),
            "success_rate": (len(successful_accounts) / request.count * 100) if request.count > 0 else 0,
            "successful_accounts": successful_accounts,
            "failed_accounts": failed_accounts,
            "created_at": datetime.now().isoformat()
        }

        logger.info(f"📊 API: Gmail creation completed - {len(successful_accounts)}/{request.count} successful")

        return response

    except Exception as e:
        logger.error(f"❌ API: Gmail account creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/gmail/create-on-existing")
async def create_gmail_on_existing_device(request: dict):
    """Create Gmail account on an existing device"""
    try:
        device_name = request.get('device_name')
        if not device_name:
            raise HTTPException(status_code=400, detail="device_name is required")

        logger.info(f"🚀 API: Creating Gmail account on existing device: {device_name}")

        # Create progress tracking
        progress_data = {"current": {}}

        def progress_callback(data):
            progress_data["current"] = data
            # Broadcast progress to WebSocket clients
            asyncio.create_task(connection_manager.broadcast({
                "type": "gmail_progress",
                "device_name": device_name,
                "data": data
            }))

        # Create Gmail account creator with progress callback
        gmail_creator = GmailAccountCreator()
        gmail_creator.progress_callback = progress_callback

        # Run automation on existing device
        result = gmail_creator.run_automation_on_existing_device(device_name)

        # Broadcast completion
        await connection_manager.broadcast({
            "type": "gmail_complete",
            "device_name": device_name,
            "result": result
        })

        if result['success']:
            logger.info(f"✅ API: Gmail account created on {device_name}: {result['email']}")
        else:
            logger.error(f"❌ API: Gmail creation failed on {device_name}: {result['error']}")

        return result

    except Exception as e:
        logger.error(f"❌ API: Gmail creation on existing device failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/gmail/batch")
async def create_gmail_accounts_batch(request: GmailBatchRequest):
    """Create multiple Gmail accounts in batch with delays"""
    try:
        logger.info(f"🚀 API: Starting batch Gmail creation ({request.count} accounts)")

        # Validate request
        if request.count < 1 or request.count > 10:  # Limit to prevent abuse
            raise HTTPException(status_code=400, detail="Count must be between 1 and 10")

        gmail_creator = GmailAccountCreator()

        results = []
        successful_accounts = []
        failed_accounts = []

        for i in range(request.count):
            logger.info(f"📱 API: Creating batch account {i+1}/{request.count}")

            # Broadcast progress to WebSocket clients
            await connection_manager.broadcast({
                "type": "gmail_batch_progress",
                "data": {
                    "current": i+1,
                    "total": request.count,
                    "status": "creating"
                }
            })

            # Run the automation
            result = gmail_creator.run_complete_automation()
            results.append(result)

            if result['success']:
                successful_accounts.append(result)
                logger.info(f"✅ API: Batch account {i+1} created: {result['email']}")
            else:
                failed_accounts.append(result)
                logger.error(f"❌ API: Batch account {i+1} failed: {result['error']}")

            # Add delay between accounts (except for the last one)
            if i < request.count - 1:
                logger.info(f"⏳ API: Waiting {request.delay_between_accounts}s before next account...")
                await asyncio.sleep(request.delay_between_accounts)

        # Prepare batch response
        batch_response = {
            "success": len(successful_accounts) > 0,
            "batch_info": {
                "total_requested": request.count,
                "successful": len(successful_accounts),
                "failed": len(failed_accounts),
                "success_rate": (len(successful_accounts) / request.count * 100),
                "delay_used": request.delay_between_accounts,
                "created_at": datetime.now().isoformat()
            },
            "successful_accounts": successful_accounts,
            "failed_accounts": failed_accounts,
            "all_results": results
        }

        # Broadcast completion to WebSocket clients
        await connection_manager.broadcast({
            "type": "gmail_batch_completed",
            "data": batch_response["batch_info"]
        })

        logger.info(f"📊 API: Batch Gmail creation completed - {len(successful_accounts)}/{request.count} successful")

        return batch_response

    except Exception as e:
        logger.error(f"❌ API: Batch Gmail creation failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/gmail/accounts")
async def get_created_accounts(limit: int = 50):
    """Get list of created Gmail accounts"""
    try:
        # Read from the accounts file
        accounts_file = "created_accounts.txt"
        accounts = []

        if os.path.exists(accounts_file):
            with open(accounts_file, 'r') as f:
                lines = f.readlines()

            # Parse account lines (skip comments)
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    try:
                        parts = line.split(':')
                        if len(parts) >= 6:
                            accounts.append({
                                "email": parts[0],
                                "password": parts[1],
                                "first_name": parts[2],
                                "last_name": parts[3],
                                "birth_date": parts[4],
                                "created_at": parts[5]
                            })
                    except Exception as e:
                        logger.debug(f"Error parsing account line: {e}")
                        continue

        # Limit results and reverse to show newest first
        accounts = accounts[-limit:] if len(accounts) > limit else accounts
        accounts.reverse()

        return {
            "success": True,
            "total_accounts": len(accounts),
            "accounts": accounts
        }

    except Exception as e:
        logger.error(f"❌ API: Failed to get accounts: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/gmail/test-data")
async def test_turkish_data_generation():
    """Test Turkish data generation without creating accounts"""
    try:
        logger.info("🎭 API: Testing Turkish data generation")

        gmail_creator = GmailAccountCreator()

        # Generate 3 test profiles
        test_profiles = []
        for i in range(3):
            info = gmail_creator.generate_personal_info()
            test_profiles.append({
                "first_name": info.first_name,
                "last_name": info.last_name,
                "username": info.username,
                "birth_date": info.birth_date,
                "phone_number": info.phone_number,
                "recovery_email": info.recovery_email
            })

        return {
            "success": True,
            "test_profiles": test_profiles,
            "generated_at": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"❌ API: Turkish data test failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/scenarios/{scenario_id}/stop")
async def stop_scenario(scenario_id: str, request: dict):
    """Stop a running automation scenario"""
    try:
        instance_name = request.get('instance_name')

        if scenario_id == "htc_one_google":
            from src.automation_scenarios import HTCOneGoogleScenario
            scenario = HTCOneGoogleScenario(instance_name=instance_name)
            success = scenario.stop_device()

            if success:
                await connection_manager.broadcast({
                    "type": "scenario_stopped",
                    "scenario": scenario_id,
                    "instance_name": instance_name
                })

                return {
                    "success": True,
                    "message": f"Scenario {scenario_id} stopped successfully"
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to stop scenario {scenario_id}"
                }
        else:
            return {
                "success": False,
                "message": f"Unknown scenario: {scenario_id}"
            }

    except Exception as e:
        logger.error(f"Failed to stop scenario {scenario_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/scenarios/{scenario_id}/cleanup")
async def cleanup_scenario(scenario_id: str, request: dict):
    """Cleanup resources from an automation scenario"""
    try:
        instance_name = request.get('instance_name')

        if scenario_id == "htc_one_google":
            from src.automation_scenarios import HTCOneGoogleScenario
            scenario = HTCOneGoogleScenario(instance_name=instance_name)

            # Stop and delete device
            stop_success = scenario.stop_device()
            delete_success = scenario.delete_device()

            await connection_manager.broadcast({
                "type": "scenario_cleanup",
                "scenario": scenario_id,
                "instance_name": instance_name,
                "stopped": stop_success,
                "deleted": delete_success
            })

            return {
                "success": stop_success or delete_success,
                "message": f"Scenario {scenario_id} cleanup completed",
                "stopped": stop_success,
                "deleted": delete_success
            }
        else:
            return {
                "success": False,
                "message": f"Unknown scenario: {scenario_id}"
            }

    except Exception as e:
        logger.error(f"Failed to cleanup scenario {scenario_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Mount static files for dashboard
app.mount("/static", StaticFiles(directory="dashboard/static"), name="static")


if __name__ == "__main__":
    import uvicorn

    host = os.getenv('DASHBOARD_HOST', '0.0.0.0')
    port = int(os.getenv('DASHBOARD_PORT', 8000))

    logger.info(f"Starting dashboard server on {host}:{port}")
    uvicorn.run(app, host=host, port=port)