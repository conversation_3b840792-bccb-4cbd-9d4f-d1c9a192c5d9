from fastapi import <PERSON><PERSON><PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse, FileResponse
from pydantic import BaseModel
from typing import List, Dict, Optional, Any
import json
import asyncio
import uuid
import re
import time
from datetime import datetime
import os
from loguru import logger

from database import DatabaseManager
from device_manager import DeviceProfileManager
from automation_client import StealthAutomationClient
from location_spoofer import LocationSpoofer


class TaskRequest(BaseModel):
    task_type: str
    parameters: Dict[str, Any]
    app_package: Optional[str] = None


class SessionRequest(BaseModel):
    app_package: Optional[str] = None
    instance_name: str = "Pixel"


class LocationRequest(BaseModel):
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    city_name: Optional[str] = None


class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info("WebSocket client connected")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.remove(websocket)
        logger.info("WebSocket client disconnected")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)

    async def broadcast(self, message: Dict):
        if self.active_connections:
            message_str = json.dumps(message)
            for connection in self.active_connections:
                try:
                    await connection.send_text(message_str)
                except:
                    # Remove broken connections
                    self.active_connections.remove(connection)


app = FastAPI(title="Genymotion Automation Dashboard", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize managers
db_manager = DatabaseManager()
device_manager = DeviceProfileManager()
automation_client = None
location_spoofer = LocationSpoofer()
connection_manager = ConnectionManager()


def get_automation_client():
    global automation_client
    if automation_client is None:
        automation_client = StealthAutomationClient(device_manager)
    return automation_client


@app.get("/")
async def read_root():
    """Serve the simplified dashboard HTML"""
    return FileResponse("dashboard/simple_index.html")


@app.get("/api/status")
async def get_status():
    """Get overall system status"""
    client = get_automation_client()

    return {
        "appium_server": device_manager.appium_manager.get_server_status(),
        "genymotion": {
            "running": True,  # This should be checked dynamically
            "current_profile": device_manager.genymotion_manager.get_current_profile()
        },
        "automation_client": {
            "active": client.current_session_id is not None,
            "session_id": client.current_session_id,
            "action_count": client.action_count
        },
        "database": {
            "connected": True,  # Should check database connection
            "active_sessions": len(db_manager.get_active_sessions())
        }
    }


@app.get("/api/sessions")
async def get_sessions():
    """Get all active and recent sessions"""
    active_sessions = db_manager.get_active_sessions()
    session_history = db_manager.get_session_history(limit=20)

    return {
        "active_sessions": active_sessions,
        "session_history": session_history
    }


@app.get("/api/sessions/{session_id}")
async def get_session_details(session_id: str):
    """Get detailed information about a specific session"""
    session_logs = db_manager.get_session_logs(session_id, limit=100)
    session_tasks = db_manager.get_session_tasks(session_id)

    return {
        "session_id": session_id,
        "logs": session_logs,
        "tasks": session_tasks
    }


@app.post("/api/sessions/start")
async def start_automation_session(request: SessionRequest):
    """Start a new automation session"""
    try:
        client = get_automation_client()

        success = client.start_session(request.app_package, request.instance_name)
        if not success:
            raise HTTPException(status_code=500, detail="Failed to start automation session")

        # Log session creation in database
        session_info = client.get_session_info(for_database=True)
        if session_info:
            db_success = db_manager.create_automation_session(session_info)
            if db_success:
                db_manager.log_automation_event(
                    session_info['session_id'],
                    "INFO",
                    "Automation session started"
                )
            else:
                logger.warning("Failed to log session to database, but continuing with session")
        else:
            logger.warning("No session info available for database logging")

        # Get JSON-serializable session info for API response
        api_session_info = client.get_session_info(for_database=False)  # This returns ISO strings

        # Broadcast update to WebSocket clients
        await connection_manager.broadcast({
            "type": "session_started",
            "data": api_session_info
        })

        return {"success": True, "session_info": api_session_info}

    except Exception as e:
        logger.error(f"Failed to start session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/stop")
async def stop_automation_session():
    """Stop the current automation session"""
    try:
        client = get_automation_client()

        if client.current_session_id:
            session_id = client.current_session_id
            client.end_session()

            # Update database
            db_manager.update_session_status(session_id, "completed", datetime.utcnow())
            db_manager.log_automation_event(session_id, "INFO", "Automation session stopped")

            # Broadcast update
            await connection_manager.broadcast({
                "type": "session_stopped",
                "data": {"session_id": session_id}
            })

            return {"success": True, "message": "Session stopped"}
        else:
            return {"success": False, "message": "No active session"}

    except Exception as e:
        logger.error(f"Failed to stop session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/kill-all")
async def kill_all_sessions():
    """Kill all active automation sessions and clean up resources"""
    try:
        client = get_automation_client()
        killed_sessions = []

        # Get all active sessions from database
        active_sessions = db_manager.get_active_sessions()

        # Stop current session if exists
        if client.current_session_id:
            session_id = client.current_session_id
            client.end_session()
            killed_sessions.append(session_id)
            logger.info(f"Stopped current session: {session_id}")

        # End all active sessions in database
        for session in active_sessions:
            session_id = session.get('session_id')
            if session_id:
                db_manager.end_automation_session(session_id)
                db_manager.log_automation_event(session_id, "INFO", "Session killed by kill-all command")
                if session_id not in killed_sessions:
                    killed_sessions.append(session_id)

        # Kill Appium server to ensure clean state
        try:
            appium_manager = client.device_manager.appium_manager
            if appium_manager.is_running:
                appium_manager.stop_server()
                logger.info("Stopped Appium server")
        except Exception as appium_error:
            logger.warning(f"Failed to stop Appium server: {appium_error}")

        # Clear any device manager sessions
        try:
            client.device_manager.active_sessions.clear()
            logger.info("Cleared device manager sessions")
        except Exception as device_error:
            logger.warning(f"Failed to clear device sessions: {device_error}")

        # Reset automation client
        client.current_session_id = None
        client.driver = None

        # Broadcast update
        await connection_manager.broadcast({
            "type": "all_sessions_killed",
            "data": {
                "killed_sessions": killed_sessions,
                "count": len(killed_sessions)
            }
        })

        logger.info(f"Killed {len(killed_sessions)} sessions: {killed_sessions}")

        return {
            "success": True,
            "message": f"Killed {len(killed_sessions)} sessions",
            "killed_sessions": killed_sessions
        }

    except Exception as e:
        logger.error(f"Failed to kill all sessions: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/sessions/rotate")
async def rotate_automation_session(request: SessionRequest):
    """Rotate the current automation session for anti-detection"""
    try:
        client = get_automation_client()

        if not client.current_session_id:
            return await start_automation_session(request)

        old_session_id = client.current_session_id
        success = client.rotate_session(request.app_package, request.instance_name)

        if not success:
            raise HTTPException(status_code=500, detail="Failed to rotate session")

        # Update database
        db_manager.update_session_status(old_session_id, "rotated", datetime.utcnow())

        new_session_info = client.get_session_info(for_database=True)
        if new_session_info:
            db_manager.create_automation_session(new_session_info)
            db_manager.log_automation_event(
                new_session_info['session_id'],
                "INFO",
                f"Session rotated from {old_session_id}"
            )

        # Get JSON-serializable session info for API response
        api_session_info = client.get_session_info(for_database=False)  # This returns ISO strings

        # Broadcast update
        await connection_manager.broadcast({
            "type": "session_rotated",
            "data": {
                "old_session_id": old_session_id,
                "new_session_info": api_session_info
            }
        })

        return {"success": True, "session_info": api_session_info}

    except Exception as e:
        logger.error(f"Failed to rotate session: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/tasks/execute")
async def execute_task(request: TaskRequest):
    """Execute a specific automation task"""
    try:
        client = get_automation_client()

        if not client.current_session_id:
            raise HTTPException(status_code=400, detail="No active automation session")

        task_id = str(uuid.uuid4())
        task_name = f"{request.task_type}_{int(datetime.now().timestamp())}"

        # Log task creation
        task_data = {
            "task_id": task_id,
            "session_id": client.current_session_id,
            "task_name": task_name,
            "task_type": request.task_type,
            "parameters": request.parameters,
            "start_time": datetime.utcnow()
        }

        db_manager.create_automation_task(task_data)
        db_manager.update_task_status(task_id, "running")

        # Execute task based on type
        result = await execute_automation_task(client, request.task_type, request.parameters)

        # Update task status
        end_time = datetime.utcnow()
        if result["success"]:
            db_manager.update_task_status(task_id, "completed", end_time)
        else:
            db_manager.update_task_status(task_id, "failed", end_time, result.get("error"))

        # Log task completion
        db_manager.log_automation_event(
            client.current_session_id,
            "INFO" if result["success"] else "ERROR",
            f"Task {request.task_type} {'completed' if result['success'] else 'failed'}",
            task_id
        )

        # Broadcast update
        await connection_manager.broadcast({
            "type": "task_completed",
            "data": {
                "task_id": task_id,
                "task_type": request.task_type,
                "success": result["success"],
                "result": result
            }
        })

        return {"success": True, "task_id": task_id, "result": result}

    except Exception as e:
        logger.error(f"Failed to execute task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


async def execute_automation_task(client: StealthAutomationClient, task_type: str, parameters: Dict) -> Dict:
    """Execute the actual automation task"""
    try:
        if task_type == "tap":
            x = parameters.get("x")
            y = parameters.get("y")
            if x is None or y is None:
                return {"success": False, "error": "Missing x or y coordinates"}

            success = client.human_tap(x, y)
            return {"success": success}

        elif task_type == "swipe":
            start_x = parameters.get("start_x")
            start_y = parameters.get("start_y")
            end_x = parameters.get("end_x")
            end_y = parameters.get("end_y")

            if None in [start_x, start_y, end_x, end_y]:
                return {"success": False, "error": "Missing swipe coordinates"}

            success = client.human_swipe(start_x, start_y, end_x, end_y)
            return {"success": success}

        elif task_type == "scroll":
            direction = parameters.get("direction", "down")
            success = client.human_scroll(direction)
            return {"success": success}

        elif task_type == "type":
            text = parameters.get("text")
            if not text:
                return {"success": False, "error": "Missing text parameter"}

            success = client.human_type(text)
            return {"success": success}

        elif task_type == "random_activity":
            duration = parameters.get("duration", 30)
            client.perform_random_human_activity(duration)
            return {"success": True}

        else:
            return {"success": False, "error": f"Unknown task type: {task_type}"}

    except Exception as e:
        return {"success": False, "error": str(e)}


@app.post("/api/location/set")
async def set_location(request: LocationRequest):
    """Set device location"""
    try:
        if request.city_name:
            success = location_spoofer.set_city_location(request.city_name)
        elif request.latitude is not None and request.longitude is not None:
            success = location_spoofer.set_location(request.latitude, request.longitude)
        else:
            success = location_spoofer.set_random_location()

        if success:
            current_location = location_spoofer.get_current_location()

            # Save to database if there's an active session
            client = get_automation_client()
            if client.current_session_id and current_location:
                db_manager.save_location_point({
                    "session_id": client.current_session_id,
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude,
                    "altitude": current_location.altitude,
                    "accuracy": current_location.accuracy
                })

            # Broadcast update
            await connection_manager.broadcast({
                "type": "location_updated",
                "data": {
                    "latitude": current_location.latitude,
                    "longitude": current_location.longitude
                }
            })

            return {"success": True, "location": current_location.__dict__}
        else:
            raise HTTPException(status_code=500, detail="Failed to set location")

    except Exception as e:
        logger.error(f"Failed to set location: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/logs")
async def get_logs(limit: int = 100, level: Optional[str] = None):
    """Get recent logs"""
    logs = db_manager.get_recent_logs(limit=limit, level=level)
    return {"logs": logs}


@app.get("/api/device-profiles")
async def get_device_profiles():
    """Get available device profiles"""
    try:
        config_path = "config/device_profiles.yaml"

        # Try relative to current directory first
        if os.path.exists(config_path):
            config_file = config_path
        else:
            # Try relative to project root (one level up from src)
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            config_file = os.path.join(project_root, config_path)

        with open(config_file, 'r') as f:
            import yaml
            config = yaml.safe_load(f)

        return {
            "device_profiles": list(config.get('device_profiles', {}).keys()),
            "locations": list(config.get('locations', {}).keys())
        }
    except Exception as e:
        logger.error(f"Failed to get device profiles: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.get("/api/instances")
async def get_instances():
    """Get available Genymotion instances"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager
        instances = genymotion_manager.get_available_instances()

        return {
            "success": True,
            "instances": instances
        }
    except Exception as e:
        logger.error(f"Failed to get instances: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances")
async def create_instance(request: dict):
    """Create a new Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        instance_name = request.get("name", f"Device_{int(time.time())}")
        device_profile = request.get("device_profile", "Custom Phone")
        android_version = request.get("android_version", "14")

        logger.info(f"Creating instance: {instance_name}")

        success = genymotion_manager.create_new_instance(instance_name, android_version)

        if success:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' created successfully",
                "instance_name": instance_name
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to create instance")

    except Exception as e:
        logger.error(f"Error creating instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.delete("/api/instances/{instance_name}")
async def delete_instance(instance_name: str):
    """Delete a Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        logger.info(f"Deleting instance: {instance_name}")

        # Stop the instance first if it's running
        stop_result = genymotion_manager._execute_gmtool(['admin', 'stop', instance_name])

        # Delete the instance
        delete_result = genymotion_manager._execute_gmtool(['admin', 'delete', instance_name])

        if delete_result and delete_result.returncode == 0:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' deleted successfully"
            }
        else:
            error_msg = delete_result.stderr if delete_result else "Unknown error"
            raise HTTPException(status_code=500, detail=f"Failed to delete instance: {error_msg}")

    except Exception as e:
        logger.error(f"Error deleting instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/start")
async def start_instance(instance_name: str):
    """Start a Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        logger.info(f"Starting instance: {instance_name}")

        success = genymotion_manager.start_instance(instance_name)

        if success:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' started successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to start instance")

    except Exception as e:
        logger.error(f"Error starting instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/stop")
async def stop_instance(instance_name: str):
    """Stop a Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        logger.info(f"Stopping instance: {instance_name}")

        success = genymotion_manager.stop_instance(instance_name)

        if success:
            return {
                "success": True,
                "message": f"Instance '{instance_name}' stopped successfully"
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to stop instance")

    except Exception as e:
        logger.error(f"Error stopping instance: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/proxy")
async def set_instance_proxy(instance_name: str, request: dict):
    """Set proxy for a Genymotion instance"""
    try:
        proxy = request.get("proxy", "")

        logger.info(f"Setting proxy for {instance_name}: {proxy}")

        # For now, just store the proxy setting
        # In a real implementation, you would configure the device proxy
        return {
            "success": True,
            "message": f"Proxy set for '{instance_name}': {proxy or 'none'}"
        }

    except Exception as e:
        logger.error(f"Error setting proxy: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/start")
async def start_instance(instance_name: str):
    """Start a specific Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        # Check if instance already exists and is running
        instances = genymotion_manager.get_available_instances()
        if instance_name in instances:
            instance_info = instances[instance_name]
            if instance_info.get('status') == 'running':
                return {
                    "success": True,
                    "message": f"Instance {instance_name} is already running"
                }

        success = genymotion_manager.start_instance_by_name(instance_name)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_started",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance {instance_name} started successfully"
            }
        else:
            # If starting failed, provide helpful message
            return {
                "success": False,
                "message": f"Failed to start instance '{instance_name}'. Instance may not exist. Please create it first using Genymotion Desktop."
            }

    except Exception as e:
        logger.error(f"Failed to start instance {instance_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/{instance_name}/stop")
async def stop_instance(instance_name: str):
    """Stop a specific Genymotion instance"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.stop_instance(instance_name)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_stopped",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance {instance_name} stopped successfully"
            }
        else:
            return {
                "success": False,
                "message": f"Failed to stop instance {instance_name}"
            }

    except Exception as e:
        logger.error(f"Failed to stop instance {instance_name}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/stop-all")
async def stop_all_instances():
    """Stop all running Genymotion instances"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager
        instances = genymotion_manager.get_available_instances()

        stopped_instances = []
        for instance_name, instance_info in instances.items():
            if instance_info.get('status') == 'running':
                success = genymotion_manager.stop_instance(instance_name)
                if success:
                    stopped_instances.append(instance_name)

        # Broadcast update
        await connection_manager.broadcast({
            "type": "all_instances_stopped",
            "data": {"stopped_instances": stopped_instances}
        })

        return {
            "success": True,
            "message": f"Stopped {len(stopped_instances)} instances",
            "stopped_instances": stopped_instances
        }

    except Exception as e:
        logger.error(f"Failed to stop all instances: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@app.post("/api/instances/open-manager")
async def open_genymotion_manager():
    """Open Genymotion Desktop application"""
    try:
        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.open_genymotion_manager()

        if success:
            return {
                "success": True,
                "message": "Genymotion Desktop opened successfully"
            }
        else:
            return {
                "success": False,
                "message": "Failed to open Genymotion Desktop. The application may not be installed. Please install Genymotion from https://www.genymotion.com/",
                "alternatives": [
                    "Install Genymotion Desktop from https://www.genymotion.com/",
                    "Open Genymotion from Applications folder",
                    "Check Genymotion installation"
                ]
            }

    except Exception as e:
        logger.error(f"Failed to open Multi-Instance Manager: {e}")
        return {
            "success": False,
            "message": f"Error opening Multi-Instance Manager: {str(e)}",
            "alternatives": [
                "Open Genymotion manually from Applications",
                "Restart your Mac if Genymotion is unresponsive",
                "Reinstall Genymotion if issues persist"
            ]
        }


@app.post("/api/instances/create")
async def create_new_instance(request: dict):
    """Create a new Genymotion instance programmatically"""
    try:
        instance_name = request.get('instance_name', '').strip()
        android_version = request.get('android_version', '11')

        if not instance_name:
            return {
                "success": False,
                "message": "Instance name is required"
            }

        # Validate instance name
        if not re.match(r'^[a-zA-Z0-9_-]+$', instance_name):
            return {
                "success": False,
                "message": "Instance name can only contain letters, numbers, underscores, and hyphens"
            }

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        # Check if instance already exists
        instances = genymotion_manager.get_available_instances()
        if instance_name in instances:
            return {
                "success": False,
                "message": f"Instance '{instance_name}' already exists"
            }

        success = genymotion_manager.create_new_instance(instance_name, android_version)

        if success:
            # Broadcast update
            await connection_manager.broadcast({
                "type": "instance_created",
                "data": {"instance_name": instance_name}
            })

            return {
                "success": True,
                "message": f"Instance '{instance_name}' created successfully"
            }
        else:
            return {
                "success": False,
                "message": f"Failed to create instance '{instance_name}'. Please create it manually using Genymotion Desktop.",
                "instructions": [
                    "1. Open Genymotion Desktop from Applications folder",
                    "2. Click the '+' button to add a new virtual device",
                    "3. Choose a device template or create custom",
                    f"4. Name the instance '{instance_name}'",
                    "5. Select Android version and settings",
                    "6. Click Create and wait for completion"
                ]
            }

    except Exception as e:
        logger.error(f"Failed to create instance: {e}")
        return {
            "success": False,
            "message": f"Error creating instance: {str(e)}"
        }


@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await connection_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_text()
            # Echo received data for now
            await connection_manager.send_personal_message(f"Received: {data}", websocket)
    except WebSocketDisconnect:
        connection_manager.disconnect(websocket)


# Genymotion-specific advanced features
@app.post("/api/genymotion/gps")
async def set_gps_location(request: dict):
    """Set GPS location using Genymotion Shell"""
    try:
        latitude = request.get('latitude')
        longitude = request.get('longitude')
        device_name = request.get('device_name')

        if latitude is None or longitude is None:
            return {"success": False, "message": "Latitude and longitude are required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.set_gps_location(float(latitude), float(longitude), device_name)

        if success:
            return {
                "success": True,
                "message": f"GPS location set to {latitude}, {longitude}"
            }
        else:
            return {
                "success": False,
                "message": "Failed to set GPS location"
            }
    except Exception as e:
        logger.error(f"Failed to set GPS location: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/genymotion/battery")
async def set_battery_level(request: dict):
    """Set battery level using Genymotion Shell"""
    try:
        level = request.get('level')
        device_name = request.get('device_name')

        if level is None:
            return {"success": False, "message": "Battery level is required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.set_battery_level(int(level), device_name)

        if success:
            return {
                "success": True,
                "message": f"Battery level set to {level}%"
            }
        else:
            return {
                "success": False,
                "message": "Failed to set battery level"
            }
    except Exception as e:
        logger.error(f"Failed to set battery level: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/genymotion/rotate")
async def rotate_device(request: dict):
    """Rotate device using Genymotion Shell"""
    try:
        orientation = request.get('orientation')
        device_name = request.get('device_name')

        if not orientation:
            return {"success": False, "message": "Orientation is required"}

        client = get_automation_client()
        genymotion_manager = client.device_manager.genymotion_manager

        success = genymotion_manager.rotate_device(orientation, device_name)

        if success:
            return {
                "success": True,
                "message": f"Device rotated to {orientation}"
            }
        else:
            return {
                "success": False,
                "message": "Failed to rotate device"
            }
    except Exception as e:
        logger.error(f"Failed to rotate device: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Automation Scenarios API
@app.post("/api/scenarios/htc-one-google")
async def run_htc_one_google_scenario(request: dict):
    """Run complete HTC One + Google.com automation scenario"""
    try:
        instance_name = request.get('instance_name')

        # Import here to avoid circular imports
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from src.automation_scenarios import HTCOneGoogleScenario

        # Create progress tracking
        progress_data = {"current": {}}

        def progress_callback(data):
            progress_data["current"] = data
            # Broadcast progress to WebSocket clients
            asyncio.create_task(connection_manager.broadcast({
                "type": "scenario_progress",
                "scenario": "htc_one_google",
                "data": data
            }))

        # Create and run scenario
        scenario = HTCOneGoogleScenario(
            instance_name=instance_name,
            progress_callback=progress_callback
        )

        # Run scenario
        result = await scenario.run_complete_scenario()

        # Broadcast completion
        await connection_manager.broadcast({
            "type": "scenario_complete",
            "scenario": "htc_one_google",
            "result": result
        })

        return result

    except Exception as e:
        logger.error(f"Failed to run HTC One scenario: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/scenarios/available")
async def get_available_scenarios():
    """Get list of available automation scenarios"""
    scenarios = [
        {
            "id": "htc_one_google",
            "name": "HTC One + Google.com",
            "description": "Create HTC One Android 14.0 device and perform Google search automation",
            "duration": "2-4 minutes",
            "requirements": ["4GB+ RAM", "Genymotion Desktop", "ADB"],
            "steps": [
                "Create HTC One virtual device",
                "Start device and wait for boot",
                "Launch default Android browser",
                "Navigate to Google.com",
                "Perform search automation"
            ]
        }
    ]

    return {"scenarios": scenarios}

@app.post("/api/scenarios/{scenario_id}/stop")
async def stop_scenario(scenario_id: str, request: dict):
    """Stop a running automation scenario"""
    try:
        instance_name = request.get('instance_name')

        if scenario_id == "htc_one_google":
            from src.automation_scenarios import HTCOneGoogleScenario
            scenario = HTCOneGoogleScenario(instance_name=instance_name)
            success = scenario.stop_device()

            if success:
                await connection_manager.broadcast({
                    "type": "scenario_stopped",
                    "scenario": scenario_id,
                    "instance_name": instance_name
                })

                return {
                    "success": True,
                    "message": f"Scenario {scenario_id} stopped successfully"
                }
            else:
                return {
                    "success": False,
                    "message": f"Failed to stop scenario {scenario_id}"
                }
        else:
            return {
                "success": False,
                "message": f"Unknown scenario: {scenario_id}"
            }

    except Exception as e:
        logger.error(f"Failed to stop scenario {scenario_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/scenarios/{scenario_id}/cleanup")
async def cleanup_scenario(scenario_id: str, request: dict):
    """Cleanup resources from an automation scenario"""
    try:
        instance_name = request.get('instance_name')

        if scenario_id == "htc_one_google":
            from src.automation_scenarios import HTCOneGoogleScenario
            scenario = HTCOneGoogleScenario(instance_name=instance_name)

            # Stop and delete device
            stop_success = scenario.stop_device()
            delete_success = scenario.delete_device()

            await connection_manager.broadcast({
                "type": "scenario_cleanup",
                "scenario": scenario_id,
                "instance_name": instance_name,
                "stopped": stop_success,
                "deleted": delete_success
            })

            return {
                "success": stop_success or delete_success,
                "message": f"Scenario {scenario_id} cleanup completed",
                "stopped": stop_success,
                "deleted": delete_success
            }
        else:
            return {
                "success": False,
                "message": f"Unknown scenario: {scenario_id}"
            }

    except Exception as e:
        logger.error(f"Failed to cleanup scenario {scenario_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))


# Mount static files for dashboard
app.mount("/static", StaticFiles(directory="dashboard/static"), name="static")


if __name__ == "__main__":
    import uvicorn

    host = os.getenv('DASHBOARD_HOST', '0.0.0.0')
    port = int(os.getenv('DASHBOARD_PORT', 8000))

    logger.info(f"Starting dashboard server on {host}:{port}")
    uvicorn.run(app, host=host, port=port)