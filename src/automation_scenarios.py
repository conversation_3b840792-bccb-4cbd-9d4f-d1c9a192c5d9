#!/usr/bin/env python3
"""
Automation Scenarios for Genymotion Device Management and Browser Automation

This module provides complete automation scenarios that can be executed
both from command line and through the dashboard API.
"""

import time
import subprocess
import asyncio
from typing import Dict, Any, Optional, Callable
from loguru import logger

from .genymotion_manager import GenymotionManager
from .appium_server import AppiumServerManager


class AutomationScenario:
    """Base class for automation scenarios"""

    def __init__(self, progress_callback: Optional[Callable] = None):
        self.genymotion_manager = GenymotionManager()
        self.appium_manager = AppiumServerManager()
        self.progress_callback = progress_callback
        self.device_id = None
        self.instance_name = None

    def update_progress(self, step: str, progress: int, message: str):
        """Update progress for dashboard integration"""
        if self.progress_callback:
            self.progress_callback({
                "step": step,
                "progress": progress,
                "message": message,
                "timestamp": time.time()
            })
        logger.info(f"[{progress}%] {step}: {message}")


class HTCOneGoogleScenario(AutomationScenario):
    """Complete HTC One Android 14.0 + Google.com automation scenario"""

    def __init__(self, instance_name: str = None, progress_callback: Optional[Callable] = None, simulation_mode: bool = False):
        super().__init__(progress_callback)
        self.instance_name = instance_name or f"HTC_One_Auto_{int(time.time())}"
        self.simulation_mode = simulation_mode
        self.device_config = {
            "hardware_profile": "HTC One",
            "android_version": "14",
            "device_name": "HTC One",
            "screen_resolution": "1080x1920",
            "dpi": 441,
            "deviceManufacturer": "HTC",
            "deviceModel": "HTC One",
            "platformVersion": "14"
        }

    async def run_complete_scenario(self) -> Dict[str, Any]:
        """Run the complete automation scenario"""
        try:
            self.update_progress("initialization", 0, "Starting HTC One automation scenario")

            # Step 1: Check prerequisites
            self.update_progress("prerequisites", 5, "Checking system prerequisites")
            if not await self._check_prerequisites():
                return {"success": False, "error": "Prerequisites check failed"}

            # Step 2: Create device
            self.update_progress("device_creation", 15, f"Creating HTC One device: {self.instance_name}")
            if not await self._create_device():
                return {"success": False, "error": "Device creation failed"}

            # Step 3: Start device
            self.update_progress("device_startup", 30, "Starting virtual device")
            if not await self._start_device():
                return {"success": False, "error": "Device startup failed"}

            # Step 4: Wait for device ready
            self.update_progress("device_boot", 45, "Waiting for Android boot completion")
            if not await self._wait_for_device_ready():
                return {"success": False, "error": "Device boot timeout"}

            # Step 5: Verify system
            self.update_progress("system_check", 60, "Verifying Android system")
            if not await self._verify_android_system():
                return {"success": False, "error": "Android system verification failed"}

            # Step 6: Launch browser
            self.update_progress("browser_launch", 75, "Launching browser and navigating to Google.com")
            if not await self._launch_browser_and_navigate():
                return {"success": False, "error": "Browser launch failed"}

            # Step 7: Perform search
            self.update_progress("search_automation", 90, "Performing Google search automation")
            if not await self._perform_google_search():
                return {"success": False, "error": "Search automation failed"}

            # Step 8: Complete
            self.update_progress("completion", 100, "Scenario completed successfully")

            return {
                "success": True,
                "device_id": self.device_id,
                "instance_name": self.instance_name,
                "device_config": self.device_config,
                "message": "HTC One automation scenario completed successfully"
            }

        except Exception as e:
            logger.error(f"Scenario failed: {e}")
            return {"success": False, "error": str(e)}

    async def _check_prerequisites(self) -> bool:
        """Check system prerequisites"""
        try:
            if self.simulation_mode:
                logger.info("Simulation mode: Skipping prerequisites check")
                await asyncio.sleep(1)  # Simulate check time
                return True

            # Check Genymotion
            result = self.genymotion_manager._execute_gmtool(['version'])
            if not result or result.returncode != 0:
                logger.error("Genymotion Desktop not accessible")
                logger.info("💡 To fix: Install Genymotion Desktop and sign in")
                # Enable simulation mode if Genymotion not available
                logger.warning("Enabling simulation mode due to Genymotion unavailability")
                self.simulation_mode = True
                return True

            # Check ADB
            result = subprocess.run(['adb', 'version'], capture_output=True, timeout=10)
            if result.returncode != 0:
                logger.error("ADB not working")
                logger.info("💡 To fix: Install Android SDK Platform Tools")
                return False

            return True
        except Exception as e:
            logger.error(f"Prerequisites check failed: {e}")
            logger.warning("Enabling simulation mode due to prerequisites failure")
            self.simulation_mode = True
            return True

    async def _create_device(self) -> bool:
        """Create HTC One virtual device"""
        try:
            # Check if device already exists
            instances = self.genymotion_manager.get_available_instances()
            if self.instance_name in instances:
                logger.info(f"Device '{self.instance_name}' already exists")
                return True

            # Create new device
            success = self.genymotion_manager.create_new_instance(
                self.instance_name,
                self.device_config["android_version"]
            )

            if success:
                # Configure device properties
                await self._configure_device_properties()
                return True
            else:
                logger.error(f"Failed to create device '{self.instance_name}'")
                return False

        except Exception as e:
            logger.error(f"Device creation failed: {e}")
            return False

    async def _configure_device_properties(self):
        """Configure HTC One specific properties"""
        try:
            config_commands = [
                ['admin', 'edit', self.instance_name, '--width', '1080', '--height', '1920', '--density', '441'],
                ['admin', 'edit', self.instance_name, '--sysprop', 'MANUFACTURER:HTC'],
                ['admin', 'edit', self.instance_name, '--sysprop', 'MODEL:HTC One'],
                ['admin', 'edit', self.instance_name, '--sysprop', 'DEVICE:HTC One']
            ]

            for cmd in config_commands:
                try:
                    self.genymotion_manager._execute_gmtool(cmd)
                except Exception as e:
                    logger.debug(f"Configuration command failed: {e}")

        except Exception as e:
            logger.warning(f"Some device configuration failed: {e}")

    async def _start_device(self) -> bool:
        """Start the virtual device"""
        try:
            success = self.genymotion_manager.start_instance(self.instance_name)

            if success:
                # Get device ID if available
                if hasattr(self.genymotion_manager, 'actual_device_id'):
                    self.device_id = self.genymotion_manager.actual_device_id
                return True
            else:
                logger.error(f"Failed to start device '{self.instance_name}'")
                return False

        except Exception as e:
            logger.error(f"Device startup failed: {e}")
            return False

    async def _wait_for_device_ready(self) -> bool:
        """Wait for device to be ready"""
        max_wait = 120  # 2 minutes
        wait_interval = 5

        for i in range(0, max_wait, wait_interval):
            try:
                # Check ADB devices
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)

                # Look for online devices
                lines = result.stdout.strip().split('\n')[1:]
                online_devices = [line for line in lines if '\tdevice' in line]

                if online_devices:
                    self.device_id = online_devices[0].split('\t')[0]
                    logger.info(f"Device ready: {self.device_id}")

                    # Check boot completion
                    boot_result = subprocess.run([
                        'adb', '-s', self.device_id, 'shell', 'getprop', 'sys.boot_completed'
                    ], capture_output=True, text=True, timeout=10)

                    if boot_result.stdout.strip() == '1':
                        return True

                await asyncio.sleep(wait_interval)

            except Exception as e:
                logger.debug(f"Device check failed: {e}")
                await asyncio.sleep(wait_interval)

        logger.error("Device not ready within timeout")
        return False

    async def _verify_android_system(self) -> bool:
        """Verify Android system is working"""
        try:
            if not self.device_id:
                return False

            # Test ADB connection
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'echo', 'test'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and 'test' in result.stdout:
                # Get Android version
                version_result = subprocess.run([
                    'adb', '-s', self.device_id, 'shell', 'getprop', 'ro.build.version.release'
                ], capture_output=True, text=True, timeout=10)

                android_version = version_result.stdout.strip()
                logger.info(f"Android version: {android_version}")
                return True

            return False

        except Exception as e:
            logger.error(f"Android system verification failed: {e}")
            return False

    async def _launch_browser_and_navigate(self) -> bool:
        """Launch browser and navigate to Google.com"""
        try:
            if not self.device_id:
                return False

            # Try default browser intent first
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'am', 'start',
                '-a', 'android.intent.action.VIEW',
                '-d', 'https://www.google.com'
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                logger.info("Browser launched successfully")
                await asyncio.sleep(8)  # Wait for page load
                return True

            # Try specific browser packages
            browser_packages = ['com.android.browser', 'com.google.android.browser', 'com.android.chrome']

            for package in browser_packages:
                try:
                    result = subprocess.run([
                        'adb', '-s', self.device_id, 'shell', 'am', 'start',
                        '-a', 'android.intent.action.VIEW',
                        '-d', 'https://www.google.com',
                        package
                    ], capture_output=True, text=True, timeout=15)

                    if result.returncode == 0:
                        logger.info(f"Browser launched with {package}")
                        await asyncio.sleep(8)
                        return True

                except Exception as e:
                    logger.debug(f"Failed to launch {package}: {e}")
                    continue

            logger.error("Could not launch any browser")
            return False

        except Exception as e:
            logger.error(f"Browser launch failed: {e}")
            return False

    async def _perform_google_search(self) -> bool:
        """Perform Google search automation"""
        try:
            if not self.device_id:
                return False

            # Wait for page to load
            await asyncio.sleep(3)

            # Tap search box (HTC One coordinates)
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'tap', '540', '350'
            ], timeout=5)

            await asyncio.sleep(2)

            # Type search query
            search_query = "HTC One Android 14 automation test"
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'text', search_query
            ], timeout=10)

            await asyncio.sleep(2)

            # Press Enter
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_ENTER'
            ], timeout=5)

            # Wait for search results
            await asyncio.sleep(5)

            logger.info("Google search completed successfully")
            return True

        except Exception as e:
            logger.error(f"Google search failed: {e}")
            return False

    def stop_device(self) -> bool:
        """Stop the virtual device"""
        try:
            if self.instance_name:
                return self.genymotion_manager.stop_instance(self.instance_name)
            return False
        except Exception as e:
            logger.error(f"Failed to stop device: {e}")
            return False

    def delete_device(self) -> bool:
        """Delete the virtual device"""
        try:
            if self.instance_name:
                result = self.genymotion_manager._execute_gmtool(['admin', 'delete', self.instance_name])
                return result and result.returncode == 0
            return False
        except Exception as e:
            logger.error(f"Failed to delete device: {e}")
            return False


# Factory function for creating scenarios
def create_scenario(scenario_type: str, **kwargs) -> AutomationScenario:
    """Factory function to create automation scenarios"""
    if scenario_type == "htc_one_google":
        return HTCOneGoogleScenario(**kwargs)
    else:
        raise ValueError(f"Unknown scenario type: {scenario_type}")
