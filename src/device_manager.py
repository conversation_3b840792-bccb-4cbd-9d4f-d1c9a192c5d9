import random
import time
import json
import uuid
import os
from typing import Dict, List, Optional, Tu<PERSON>
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import yaml
from loguru import logger
from genymotion_manager import GenymotionManager
from appium_server import AppiumServerManager


@dataclass
class DeviceSession:
    session_id: str
    device_profile: Dict
    location: Dict
    start_time: datetime
    end_time: Optional[datetime] = None
    actions_count: int = 0
    status: str = "active"  # active, completed, failed


class DeviceProfileManager:
    def __init__(self, config_path: str = "config/device_profiles.yaml"):
        self.config_path = config_path
        self.genymotion_manager = GenymotionManager(config_path)
        self.appium_manager = AppiumServerManager()
        self.active_sessions: Dict[str, DeviceSession] = {}
        self.session_history: List[DeviceSession] = []
        self.rotation_interval = 3600  # 1 hour default
        self.max_actions_per_session = 100

        # Backward compatibility
        self.bluestacks_manager = self.genymotion_manager

    def create_new_session(self, app_package: str = None) -> Tuple[str, Dict]:
        session_id = str(uuid.uuid4())

        # Get random device profile
        device_profile = self.genymotion_manager.get_random_device_profile()

        # Get random location
        location = self._get_random_location()

        # Create session
        session = DeviceSession(
            session_id=session_id,
            device_profile=device_profile,
            location=location,
            start_time=datetime.now()
        )

        self.active_sessions[session_id] = session

        logger.info(f"Created new device session: {session_id}")
        logger.info(f"Device: {device_profile['deviceName']}")
        logger.info(f"Location: {location.get('name', 'Unknown')}")

        return session_id, device_profile

    def _get_random_location(self) -> Dict:
        try:
            # Try relative to current directory first
            if os.path.exists(self.config_path):
                config_file = self.config_path
            else:
                # Try relative to project root (one level up from src)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = os.path.join(project_root, self.config_path)

            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)

            locations = config.get('locations', {})
            if not locations:
                return self._get_default_location()

            location_name = random.choice(list(locations.keys()))
            location_data = locations[location_name].copy()
            location_data['name'] = location_name

            # Add some randomness to coordinates for better stealth
            location_data['latitude'] += random.uniform(-0.01, 0.01)
            location_data['longitude'] += random.uniform(-0.01, 0.01)

            return location_data

        except Exception as e:
            logger.error(f"Failed to load locations: {e}")
            return self._get_default_location()

    def _get_default_location(self) -> Dict:
        return {
            'name': 'default',
            'latitude': 37.7749,
            'longitude': -122.4194,
            'accuracy': 10
        }

    def setup_device_session(self, session_id: str, instance_name: str = "Pixel") -> bool:
        if session_id not in self.active_sessions:
            logger.error(f"Session not found: {session_id}")
            return False

        session = self.active_sessions[session_id]

        try:
            # Configure Genymotion with device profile
            success = self.genymotion_manager.configure_genymotion_instance(
                instance_name, session.device_profile
            )

            if not success:
                logger.error("Failed to configure Genymotion instance")
                logger.warning("Continuing with default Genymotion configuration")

            # Start Genymotion instance
            success = self.genymotion_manager.start_instance(instance_name)
            if not success:
                logger.error("Failed to start Genymotion instance")
                logger.warning("Continuing anyway - Genymotion might already be running")

            # Update device profile with actual device ID if detected
            if self.genymotion_manager.actual_device_id:
                session.device_profile['udid'] = self.genymotion_manager.actual_device_id
                logger.info(f"Updated session device ID to: {self.genymotion_manager.actual_device_id}")

            # Wait for instance to be ready
            time.sleep(10)

            # Start Appium server if not running
            if not self.appium_manager.is_running:
                success = self.appium_manager.start_server()
                if not success:
                    logger.error("Appium server failed to start")
                    logger.warning("System will continue in simulation mode")
                else:
                    logger.info("Appium server started successfully")

            logger.info(f"Device session {session_id} setup completed")
            return True

        except Exception as e:
            logger.error(f"Failed to setup device session: {e}")
            logger.warning("System will continue in simulation mode")
            return True  # Return True to allow simulation mode

    def get_appium_capabilities(self, session_id: str, app_package: str = None) -> Optional[Dict]:
        if session_id not in self.active_sessions:
            logger.error(f"Session not found: {session_id}")
            return None

        session = self.active_sessions[session_id]
        return self.appium_manager.generate_capabilities(session.device_profile, app_package)

    def should_rotate_session(self, session_id: str) -> bool:
        if session_id not in self.active_sessions:
            return True

        session = self.active_sessions[session_id]
        current_time = datetime.now()

        # Check time-based rotation
        if (current_time - session.start_time).seconds > self.rotation_interval:
            logger.info(f"Session {session_id} expired due to time limit")
            return True

        # Check action-based rotation
        if session.actions_count >= self.max_actions_per_session:
            logger.info(f"Session {session_id} expired due to action limit")
            return True

        return False

    def increment_action_count(self, session_id: str):
        if session_id in self.active_sessions:
            self.active_sessions[session_id].actions_count += 1

    def end_session(self, session_id: str, status: str = "completed"):
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            session.end_time = datetime.now()
            session.status = status

            # Move to history
            self.session_history.append(session)
            del self.active_sessions[session_id]

            logger.info(f"Session {session_id} ended with status: {status}")

    def rotate_device_session(self, current_session_id: str, instance_name: str = "Pixel", app_package: str = None) -> Tuple[str, Dict]:
        logger.info(f"Rotating device session from {current_session_id}")

        # End current session
        self.end_session(current_session_id)

        # Reset Genymotion instance
        self.genymotion_manager.reset_instance(instance_name)

        # Restart Appium server for clean state
        self.appium_manager.restart_server()

        # Create new session
        new_session_id, device_profile = self.create_new_session(app_package)

        # Setup new session
        success = self.setup_device_session(new_session_id, instance_name)
        if not success:
            raise Exception("Failed to setup new device session")

        return new_session_id, device_profile

    def get_session_info(self, session_id: str, for_database: bool = False) -> Optional[Dict]:
        if session_id in self.active_sessions:
            session = self.active_sessions[session_id]
            return {
                "session_id": session.session_id,
                "device_name": session.device_profile.get("deviceName"),
                "device_model": session.device_profile.get("deviceModel"),
                "platform_version": session.device_profile.get("platformVersion"),
                "android_id": session.device_profile.get("android_id"),
                "imei": session.device_profile.get("imei"),
                "advertising_id": session.device_profile.get("advertising_id"),
                "location_name": session.location.get("name"),
                "latitude": session.location.get("latitude"),
                "longitude": session.location.get("longitude"),
                "start_time": session.start_time if for_database else session.start_time.isoformat(),
                "actions_count": session.actions_count,
                "status": session.status,
                "uptime_seconds": (datetime.now() - session.start_time).seconds,
                "app_package": None  # Will be set if provided
            }
        return None

    def get_all_active_sessions(self) -> List[Dict]:
        return [self.get_session_info(sid) for sid in self.active_sessions.keys()]

    def get_session_history(self, limit: int = 50) -> List[Dict]:
        recent_sessions = self.session_history[-limit:] if limit else self.session_history

        return [{
            "session_id": s.session_id,
            "device_name": s.device_profile.get("deviceName"),
            "device_model": s.device_profile.get("deviceModel"),
            "location": s.location,
            "start_time": s.start_time.isoformat(),
            "end_time": s.end_time.isoformat() if s.end_time else None,
            "actions_count": s.actions_count,
            "status": s.status,
            "duration_seconds": (s.end_time - s.start_time).seconds if s.end_time else None
        } for s in recent_sessions]

    def cleanup_all_sessions(self):
        logger.info("Cleaning up all active sessions")

        for session_id in list(self.active_sessions.keys()):
            self.end_session(session_id, "cleanup")

        # Stop Appium server
        self.appium_manager.stop_server()

        # Stop Genymotion instances
        self.genymotion_manager.stop_instance()

        logger.info("All sessions cleaned up")

    def get_device_fingerprint(self, session_id: str) -> Optional[Dict]:
        if session_id not in self.active_sessions:
            return None

        session = self.active_sessions[session_id]
        profile = session.device_profile

        return {
            "android_id": profile.get("android_id"),
            "imei": profile.get("imei"),
            "advertising_id": profile.get("advertising_id"),
            "device_model": profile.get("deviceModel"),
            "manufacturer": profile.get("deviceManufacturer"),
            "platform_version": profile.get("platformVersion"),
            "screen_resolution": profile.get("screen_resolution"),
            "dpi": profile.get("dpi"),
            "location": session.location
        }