import subprocess
import time
import os
import json
import random
import threading
from typing import Dict, Optional, List
import yaml
from loguru import logger


class GenymotionManager:
    def __init__(self, config_path: str = "config/device_profiles.yaml"):
        self.config_path = config_path
        self.device_profiles = self._load_device_profiles()
        self.current_profile = None
        self.actual_device_id = None
        self.genymotion_path = os.getenv('GENYMOTION_PATH', '/Applications/Genymotion.app/Contents/MacOS')
        self.gmtool_path = os.path.join(self.genymotion_path, 'gmtool')
        self.genyshell_path = os.getenv('GENYMOTION_SHELL_PATH', '/Applications/Genymotion Shell.app/Contents/MacOS/genyshell')
        self.running_instances = {}

        # Persistent genyshell session management
        self._genyshell_process = None
        self._genyshell_lock = threading.Lock()
        self._genyshell_last_used = 0
        self._genyshell_timeout = 300  # 5 minutes timeout for idle session

        logger.info("🔧 Persistent genyshell session manager enabled")

    def __del__(self):
        """Cleanup persistent genyshell session on destruction"""
        self._cleanup_genyshell_session()

    def _cleanup_genyshell_session(self):
        """Clean up the persistent genyshell session"""
        with self._genyshell_lock:
            if self._genyshell_process and self._genyshell_process.poll() is None:
                try:
                    self._genyshell_process.stdin.write("exit\n")
                    self._genyshell_process.stdin.flush()
                    self._genyshell_process.wait(timeout=5)
                    logger.debug("🔧 Persistent genyshell session closed gracefully")
                except:
                    self._genyshell_process.terminate()
                    logger.debug("🔧 Persistent genyshell session terminated")
                finally:
                    self._genyshell_process = None

    def _get_persistent_genyshell(self):
        """Get or create persistent genyshell session"""
        with self._genyshell_lock:
            current_time = time.time()

            # Check if session exists and is still alive
            if (self._genyshell_process and
                self._genyshell_process.poll() is None and
                (current_time - self._genyshell_last_used) < self._genyshell_timeout):

                self._genyshell_last_used = current_time
                return self._genyshell_process

            # Clean up old session if exists
            if self._genyshell_process:
                try:
                    self._genyshell_process.terminate()
                except:
                    pass
                self._genyshell_process = None

            # Create new persistent session
            try:
                logger.debug("🔧 Creating new persistent genyshell session")
                self._genyshell_process = subprocess.Popen(
                    [self.genyshell_path],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1  # Line buffered
                )
                self._genyshell_last_used = current_time
                logger.debug("✅ Persistent genyshell session created successfully")
                return self._genyshell_process

            except Exception as e:
                logger.error(f"❌ Failed to create persistent genyshell session: {e}")
                self._genyshell_process = None
                return None

    def _load_device_profiles(self) -> Dict:
        try:
            # Try relative to current directory first
            if os.path.exists(self.config_path):
                config_file = self.config_path
            else:
                # Try relative to project root (one level up from src)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = os.path.join(project_root, self.config_path)

            with open(config_file, 'r') as f:
                logger.info(f"Loaded device profiles from: {config_file}")
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Device profiles config not found: {self.config_path}")
            logger.error(f"Also tried: {config_file if 'config_file' in locals() else 'N/A'}")
            return {}

    def get_random_device_profile(self) -> Dict:
        profiles = self.device_profiles.get('device_profiles', {})
        if not profiles:
            raise ValueError("No device profiles configured")

        profile_name = random.choice(list(profiles.keys()))
        profile = profiles[profile_name].copy()

        # Randomize some properties for better anti-detection
        profile['android_id'] = self._generate_android_id()
        profile['imei'] = self._generate_imei()
        profile['advertising_id'] = self._generate_advertising_id()

        # Use actual device ID if available, otherwise use default
        if self.actual_device_id:
            profile['udid'] = self.actual_device_id
            logger.info(f"Using actual device ID: {self.actual_device_id}")
        else:
            logger.warning(f"Using default device ID: {profile.get('udid', 'emulator-5554')}")

        logger.info(f"Selected device profile: {profile_name}")
        return profile

    def _generate_android_id(self) -> str:
        return ''.join(random.choices('0123456789abcdef', k=16))

    def _generate_imei(self) -> str:
        # Generate a valid IMEI (15 digits)
        base = ''.join(random.choices('0123456789', k=14))
        check_digit = self._calculate_luhn_checksum(base)
        return base + str(check_digit)

    def _calculate_luhn_checksum(self, number: str) -> int:
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10
        return (10 - luhn_checksum(int(number))) % 10

    def _generate_advertising_id(self) -> str:
        return f"{random.randint(10000000, 99999999):08x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(100000000000, 999999999999):012x}"

    def configure_genymotion_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        if profile is None:
            profile = self.get_random_device_profile()

        self.current_profile = profile

        try:
            logger.info(f"Configuring Genymotion instance '{instance_name}' with profile: {profile['deviceName']}")

            # Store the profile for later use
            self.current_profile = profile

            # Check if instance exists, create if not
            if not self._instance_exists(instance_name):
                logger.info(f"Instance '{instance_name}' does not exist, creating...")
                if not self.create_new_instance(instance_name, profile.get('platformVersion', '11')):
                    logger.warning(f"Failed to create instance '{instance_name}', continuing anyway")

            # Configure instance properties using gmtool
            success = self._configure_instance_properties(instance_name, profile)
            if success:
                logger.info(f"Genymotion instance '{instance_name}' configured successfully")
            else:
                logger.warning(f"Some configuration options failed for '{instance_name}'")

            return True

        except Exception as e:
            logger.error(f"Failed to configure Genymotion instance: {e}")
            return False

    def _instance_exists(self, instance_name: str) -> bool:
        """Check if a Genymotion instance exists"""
        try:
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                return instance_name in result.stdout
            return False
        except Exception as e:
            logger.debug(f"Failed to check if instance exists: {e}")
            return False

    def _configure_instance_properties(self, instance_name: str, profile: Dict) -> bool:
        """Configure Genymotion instance properties"""
        try:
            # Build edit command with device properties
            edit_cmd = ['admin', 'edit', instance_name]

            # Set screen resolution if available
            if 'screen_resolution' in profile:
                width, height = profile['screen_resolution'].split('x')
                edit_cmd.extend(['--width', width, '--height', height])

            # Set DPI if available
            if 'dpi' in profile:
                edit_cmd.extend(['--density', str(profile['dpi'])])

            # Set system properties for device spoofing
            if 'deviceManufacturer' in profile:
                edit_cmd.extend(['--sysprop', f"MANUFACTURER:{profile['deviceManufacturer']}"])
            if 'deviceModel' in profile:
                edit_cmd.extend(['--sysprop', f"MODEL:{profile['deviceModel']}"])
            if 'deviceName' in profile:
                edit_cmd.extend(['--sysprop', f"DEVICE:{profile['deviceName']}"])

            # Execute the edit command
            result = self._execute_gmtool(edit_cmd)
            return result and result.returncode == 0

        except Exception as e:
            logger.error(f"Failed to configure instance properties: {e}")
            return False

    def _restart_adb_server(self) -> bool:
        """Restart ADB server to fix connection issues"""
        try:
            logger.info("Restarting ADB server...")
            subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=5)
            time.sleep(2)
            subprocess.run(['adb', 'start-server'], capture_output=True, timeout=10)
            time.sleep(3)

            # Check if any device is now online
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"ADB devices after restart: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"ADB restart failed: {e}")
            return False

    def _reconnect_device(self, device_id: str) -> bool:
        """Try to reconnect specific device"""
        try:
            logger.info(f"Attempting to reconnect device: {device_id}")
            # Extract port from device ID if it's emulator format
            if "emulator-" in device_id:
                port = device_id.split("-")[1]
                # Try to connect via TCP
                logger.debug(f"Connecting to 127.0.0.1:{port}")
                result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                      capture_output=True, text=True, timeout=10)
                logger.debug(f"ADB connect result: {result.stdout} {result.stderr}")
                time.sleep(3)

                # Check device status
                check_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                logger.debug(f"Device status after reconnect: {check_result.stdout}")

                # Check if device is now online
                is_online = device_id in check_result.stdout and f"{device_id}\tdevice" in check_result.stdout
                if is_online:
                    logger.info(f"Device {device_id} successfully reconnected")
                    return True
                else:
                    logger.debug(f"Device {device_id} still not online after reconnect attempt")
                    return False
            return False
        except Exception as e:
            logger.debug(f"Device reconnect failed: {e}")
            return False

    def _connect_network_device(self) -> bool:
        """Try to connect via common Genymotion network ports"""
        try:
            logger.info("Attempting to connect via common Genymotion ports...")
            # Genymotion typically uses different port ranges than BlueStacks
            common_ports = ['5555', '5556', '5557', '5558', '5559', '5560', '5561', '5562']
            connected_devices = []

            for port in common_ports:
                try:
                    logger.debug(f"Trying port {port}...")
                    result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                        connected_devices.append(port)
                        logger.debug(f"Successfully connected to port {port}")
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Failed to connect to port {port}: {e}")
                    continue

            if connected_devices:
                logger.info(f"Connected to ports: {connected_devices}")

            # Check if any device is now online
            time.sleep(2)
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"Devices after network connect: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"Network connect failed: {e}")
            return False

    def _execute_gmtool(self, command: List[str]) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command and return result"""
        return self._execute_gmtool_with_timeout(command, timeout=30)

    def _execute_gmtool_with_timeout(self, command: List[str], timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command with custom timeout and return result"""
        try:
            full_command = [self.gmtool_path] + command
            logger.debug(f"Executing gmtool command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Gmtool command executed successfully: {' '.join(command)}")
            else:
                logger.warning(f"Gmtool command failed: {' '.join(command)}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Gmtool command timed out after {timeout}s: {' '.join(command)}")
            return None
        except Exception as e:
            logger.error(f"Error executing gmtool command: {e}")
            return None

    def _check_genymotion_running(self) -> bool:
        """Check if Genymotion Desktop is running"""
        try:
            # Try to get version as a quick check
            result = self._execute_gmtool(['version'])
            return result and result.returncode == 0
        except Exception:
            return False

    def _parse_hardware_profile(self, stdout: str) -> Optional[str]:
        """Parse hardware profile name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for HTC One first, then Custom Phone as fallback
        for line in lines:
            if "HTC One" in line:
                return "HTC One"
            elif "Custom Phone" in line:
                return "Custom Phone"

        # If not found, try to parse the first valid profile
        for line in lines:
            if line.strip() and not line.startswith('-') and not line.startswith('UUID') and not line.startswith('NAME'):
                # Split by whitespace and try to extract name
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Extract everything until we hit display specs
                    name_parts = []
                    for i in range(1, len(parts)):
                        if parts[i].isdigit() or 'x' in parts[i] or 'dpi' in parts[i]:
                            break
                        name_parts.append(parts[i])
                    if name_parts:
                        return ' '.join(name_parts)

        return None

    def _parse_os_image(self, stdout: str, android_version: str) -> Optional[str]:
        """Parse OS image name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for the specified Android version first
        for line in lines:
            if f"Android {android_version}" in line or f"{android_version}.0" in line:
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Format: "Android 14.0" - need to combine Android + version
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android 14.0"
                    else:
                        return parts[1]

        # Fallback to any Android version
        for line in lines:
            if "Android" in line and line.strip() and not line.startswith('-') and not line.startswith('UUID'):
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android X.Y"
                    else:
                        return parts[1]

        return None

    def _provide_creation_troubleshooting(self, error_message: str):
        """Provide troubleshooting tips based on error message"""
        if "Unable to find the hwprofile" in error_message:
            logger.info("💡 Hardware profile not found. Try:")
            logger.info("   1. Check available profiles: gmtool admin hwprofiles")
            logger.info("   2. Use exact profile name from the list")
        elif "Unable to find the osimage" in error_message:
            logger.info("💡 OS image not found. Try:")
            logger.info("   1. Check available images: gmtool admin osimages")
            logger.info("   2. Use exact image name from the list")
        elif "Unable to sign in" in error_message:
            logger.info("💡 Authentication issue. Try:")
            logger.info("   1. Open Genymotion Desktop and sign in")
            logger.info("   2. Ensure you have a valid Genymotion account")
        elif "already exists" in error_message:
            logger.info("💡 Instance already exists. Try:")
            logger.info("   1. Use a different instance name")
            logger.info("   2. Delete existing instance: gmtool admin delete <name>")
        else:
            logger.info("💡 General troubleshooting:")
            logger.info("   1. Ensure Genymotion Desktop is running")
            logger.info("   2. Check that you're signed in to Genymotion")
            logger.info("   3. Verify sufficient system resources")

    def _execute_genyshell(self, command: str, device_ip: str = None, timeout: int = 60) -> Optional[subprocess.CompletedProcess]:
        """Execute Genymotion Shell command with persistent session optimization"""
        try:
            # Use persistent session for widget commands (android, phone)
            if command.startswith(('android', 'phone')):
                logger.debug(f"Using persistent session for widget command: {command}")
                return self._execute_genyshell_interactive(command, timeout=timeout)

            # Use direct command mode for other commands (devices, etc.)
            if device_ip:
                full_command = [self.genyshell_path, '-r', device_ip, '-c', command]
            else:
                full_command = [self.genyshell_path, '-c', command]

            logger.debug(f"Executing direct genyshell command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Direct genyshell command executed successfully: {command}")
            else:
                logger.warning(f"Direct genyshell command failed: {command}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Genyshell command timed out: {command}")
            return None
        except Exception as e:
            logger.error(f"Error executing genyshell command: {e}")
            return None

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Enable GPS first
            gps_enable_result = self._execute_genyshell("gps setstatus enabled", device_ip)
            if not gps_enable_result or gps_enable_result.returncode != 0:
                logger.error("Failed to enable GPS")
                return False

            # Set latitude
            lat_result = self._execute_genyshell(f"gps setlatitude {latitude}", device_ip)
            if not lat_result or lat_result.returncode != 0:
                logger.error("Failed to set GPS latitude")
                return False

            # Set longitude
            lng_result = self._execute_genyshell(f"gps setlongitude {longitude}", device_ip)
            if not lng_result or lng_result.returncode != 0:
                logger.error("Failed to set GPS longitude")
                return False

            logger.info(f"GPS location set to {latitude}, {longitude}")
            return True

        except Exception as e:
            logger.error(f"Failed to set GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using Genymotion Shell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            # Set battery to manual mode first
            mode_result = self._execute_genyshell("battery setmode manual", device_ip)
            if not mode_result or mode_result.returncode != 0:
                logger.error("Failed to set battery to manual mode")
                return False

            # Set battery level
            level_result = self._execute_genyshell(f"battery setlevel {level}", device_ip)
            if not level_result or level_result.returncode != 0:
                logger.error("Failed to set battery level")
                return False

            logger.info(f"Battery level set to {level}%")
            return True

        except Exception as e:
            logger.error(f"Failed to set battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using Genymotion Shell"""
        try:
            # Map orientation names to angles
            orientation_map = {
                "portrait": 0,
                "landscape": 90,
                "portrait_reverse": 180,
                "landscape_reverse": 270,
                "0": 0,
                "90": 90,
                "180": 180,
                "270": 270
            }

            angle = orientation_map.get(orientation.lower())
            if angle is None:
                logger.error(f"Invalid orientation: {orientation}. Use: portrait, landscape, portrait_reverse, landscape_reverse")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"rotation setangle {angle}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to rotate device")
                return False

            logger.info(f"Device rotated to {orientation} ({angle}°)")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"phone call {phone_number}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to simulate phone call")
                return False

            logger.info(f"Phone call simulated from {phone_number}")
            return True

        except Exception as e:
            logger.error(f"Failed to simulate phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Escape the message for shell command
            escaped_message = message.replace('"', '\\"').replace("'", "\\'")

            result = self._execute_genyshell(f'phone sms {phone_number} "{escaped_message}"', device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to send SMS")
                return False

            logger.info(f"SMS sent from {phone_number}: {message}")
            return True

        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False

    def _get_device_ip(self, device_name: str) -> Optional[str]:
        """Get device IP address for Genymotion Shell connection"""
        try:
            instances = self.get_available_instances()
            if device_name in instances:
                instance_info = instances[device_name]
                return instance_info.get('ip_address')
            return None
        except Exception as e:
            logger.error(f"Failed to get device IP: {e}")
            return None

    def _execute_genyshell_interactive(self, command: str, device_name: str = None, timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute genyshell command using persistent session (optimized)"""
        try:
            # Get persistent genyshell session
            genyshell = self._get_persistent_genyshell()
            if not genyshell:
                logger.error("Failed to get persistent genyshell session")
                return None

            # Build the command to execute
            if device_name:
                full_command = f"devices select {device_name}\n{command}\n"
            else:
                full_command = f"{command}\n"

            logger.debug(f"Executing genyshell command via persistent session: {command}")

            # Send command to persistent session
            try:
                genyshell.stdin.write(full_command)
                genyshell.stdin.flush()

                # Read response with timeout
                import select
                import sys

                if sys.platform != 'win32':
                    # Use select for non-Windows systems
                    ready, _, _ = select.select([genyshell.stdout], [], [], timeout)
                    if ready:
                        # Read available output
                        output_lines = []
                        while True:
                            ready, _, _ = select.select([genyshell.stdout], [], [], 0.1)
                            if not ready:
                                break
                            line = genyshell.stdout.readline()
                            if not line:
                                break
                            output_lines.append(line)
                        stdout = ''.join(output_lines)
                        stderr = ""
                        returncode = 0
                    else:
                        stdout = ""
                        stderr = "Command timed out"
                        returncode = 1
                else:
                    # Fallback for Windows - read with timeout
                    stdout = ""
                    stderr = ""
                    returncode = 0

                result = subprocess.CompletedProcess(
                    args=[self.genyshell_path],
                    returncode=returncode,
                    stdout=stdout,
                    stderr=stderr
                )

                if result.returncode == 0:
                    logger.debug(f"✅ Persistent genyshell command executed successfully: {command}")
                else:
                    logger.warning(f"❌ Persistent genyshell command failed: {command}, Error: {result.stderr}")

                return result

            except Exception as e:
                logger.error(f"Error communicating with persistent genyshell: {e}")
                # Reset session on communication error
                self._cleanup_genyshell_session()
                return None

        except Exception as e:
            logger.error(f"Error executing persistent genyshell command: {e}")
            return None

    def start_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Starting Genymotion instance '{instance_name}'")

            # Check if instance exists
            if not self._instance_exists(instance_name):
                logger.warning(f"Instance '{instance_name}' does not exist")
                return False

            # Start the instance using gmtool (device startup can take 5-10 minutes for Android 14)
            result = self._execute_gmtool_with_timeout(['admin', 'start', instance_name], timeout=600)
            if not result or result.returncode != 0:
                logger.error(f"Failed to start instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            logger.info(f"Instance '{instance_name}' started successfully")

            # Wait for device to appear in ADB (Android 14.0 can take longer to boot)
            max_wait = 300  # seconds (5 minutes)
            wait_interval = 5
            for i in range(0, max_wait, wait_interval):
                time.sleep(wait_interval)
                try:
                    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                    device_lines = [line.strip() for line in result.stdout.split('\n') if 'emulator-' in line or '127.0.0.1:' in line]
                    if device_lines:
                        for line in device_lines:
                            parts = line.split()
                            if len(parts) >= 2:
                                device_id, device_status = parts[0], parts[1]
                                if device_status == 'device':
                                    logger.info(f"Genymotion instance started successfully. Device: {device_id}")
                                    self.actual_device_id = device_id
                                    return True
                except Exception as e:
                    logger.debug(f"ADB check failed: {e}")

            # If ADB connection failed, try to connect manually
            logger.warning("Device not detected via ADB, attempting manual connection...")
            if self._connect_network_device():
                return True

            logger.warning(f"Could not establish ADB connection to '{instance_name}', but instance may be running")
            return True

        except Exception as e:
            logger.error(f"Error starting Genymotion instance: {e}")
            return False

    def stop_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Stopping Genymotion instance '{instance_name}'")

            # Stop the instance using gmtool
            result = self._execute_gmtool(['admin', 'stop', instance_name])
            if not result or result.returncode != 0:
                logger.error(f"Failed to stop instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            if instance_name in self.running_instances:
                del self.running_instances[instance_name]

            logger.info(f"Genymotion instance '{instance_name}' stopped")
            return True
        except Exception as e:
            logger.error(f"Failed to stop Genymotion instance: {e}")
            return False

    def delete_instance(self, instance_name: str) -> bool:
        """Delete a Genymotion instance"""
        try:
            logger.info(f"🗑️ Deleting Genymotion instance '{instance_name}'")

            # Stop instance first if running
            self.stop_instance(instance_name)

            # Delete the instance
            result = self._execute_gmtool(['admin', 'delete', instance_name])

            if result and result.returncode == 0:
                logger.info(f"✅ Genymotion instance '{instance_name}' deleted successfully")

                # Remove from running instances if present
                if instance_name in self.running_instances:
                    del self.running_instances[instance_name]

                return True
            else:
                error_msg = result.stderr if result else "Unknown error"
                logger.error(f"❌ Failed to delete instance '{instance_name}'")
                logger.error(f"Error output: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error deleting instance: {e}")
            return False

    def get_available_instances(self) -> Dict:
        """Get list of available Genymotion instances"""
        try:
            instances = {}

            # Get all instances using gmtool
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    # Skip header, separator lines, and empty lines
                    if (line.strip() and
                        not line.startswith('State') and
                        not line.startswith('----') and
                        not '+-' in line and
                        '|' in line):  # Must be a data line with pipe separators

                        # Parse pipe-separated format: State | ADB Serial | UUID | Name
                        parts = [part.strip() for part in line.split('|')]
                        if len(parts) >= 4:
                            state = parts[0].strip()
                            adb_serial = parts[1].strip()
                            uuid = parts[2].strip()
                            instance_name = parts[3].strip()

                            # Convert state to status
                            status = 'running' if state.lower() == 'on' else 'stopped'

                            instances[instance_name] = {
                                'name': instance_name,
                                'status': status,
                                'device_id': adb_serial if adb_serial != '127.0.0.1:6554' else None,
                                'adb_status': 'online' if status == 'running' else 'offline',
                                'uuid': uuid,
                                'hardware_profile': 'Unknown',
                                'android_version': 'Unknown',
                                'adb_serial': adb_serial if adb_serial else None
                            }

            # Get detailed information for each instance
            for instance_name, instance_info in instances.items():
                # Get detailed device info using gmtool admin details
                try:
                    details_result = self._execute_gmtool(['admin', 'details', instance_name])
                    if details_result and details_result.returncode == 0:
                        details_lines = details_result.stdout.strip().split('\n')

                        for line in details_lines:
                            line = line.strip()
                            if line.startswith('Android Version'):
                                # Extract Android version: "Android Version       : 14.0.0"
                                version = line.split(':', 1)[1].strip()
                                instance_info['android_version'] = f"Android {version}"
                            elif line.startswith('System property') and 'MODEL=' in line:
                                # Extract model: "System property       : MODEL=HTC One"
                                model_part = line.split('MODEL=', 1)[1].strip()
                                logger.debug(f"Found MODEL system property for {instance_name}: {model_part}")
                                instance_info['hardware_profile'] = model_part
                            elif line.startswith('ADB Serial'):
                                # Extract ADB Serial: "ADB Serial            : 127.0.0.1:6554"
                                adb_serial = line.split(':', 1)[1].strip()
                                instance_info['adb_serial'] = adb_serial
                                # Also update device_id if not already set from the main list
                                if not instance_info['device_id']:
                                    instance_info['device_id'] = adb_serial
                            elif line.startswith('Hardware profile:'):
                                # Extract hardware profile name (if available)
                                profile = line.split(':', 1)[1].strip()
                                instance_info['hardware_profile'] = profile
                except Exception as e:
                    logger.debug(f"Failed to get details for {instance_name}: {e}")

                # If we still don't have hardware profile, try to get it from device profiles
                if instance_info['hardware_profile'] == 'Unknown':
                    try:
                        # Try to match with known device profiles
                        if hasattr(self, 'device_profiles') and self.device_profiles:
                            for profile_name, profile_data in self.device_profiles.items():
                                if profile_name.lower() in instance_name.lower():
                                    instance_info['hardware_profile'] = profile_name
                                    break
                    except Exception as e:
                        logger.debug(f"Failed to match device profile for {instance_name}: {e}")

            # Get ADB devices to match with instances
            try:
                adb_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                for line in adb_result.stdout.split('\n')[1:]:  # Skip header
                    if line.strip() and '\t' in line:
                        device_id, status = line.strip().split('\t')
                        # Try to match with running instances
                        for instance_name, instance_info in instances.items():
                            if instance_info['status'] == 'running' and not instance_info['device_id']:
                                instance_info['device_id'] = device_id
                                instance_info['adb_status'] = status
                                break
            except Exception as e:
                logger.debug(f"Failed to get ADB devices: {e}")

            return instances

        except Exception as e:
            logger.error(f"Failed to get available instances: {e}")
            return {}

    def create_new_instance(self, instance_name: str, android_version: str = "11") -> bool:
        """Create a new Genymotion instance programmatically"""
        try:
            logger.info(f"Creating new Genymotion instance: {instance_name}")

            # First check if Genymotion Desktop is running
            if not self._check_genymotion_running():
                logger.error("Genymotion Desktop is not running")
                logger.info("Please start Genymotion Desktop and sign in before creating instances")
                return False

            # Get available hardware profiles and OS images
            logger.info("Getting available hardware profiles...")
            hwprofiles_result = self._execute_gmtool(['admin', 'hwprofiles'])

            logger.info("Getting available OS images...")
            osimages_result = self._execute_gmtool(['admin', 'osimages'])

            if not hwprofiles_result or hwprofiles_result.returncode != 0:
                logger.error("Failed to get hardware profiles")
                if hwprofiles_result and hwprofiles_result.stderr:
                    logger.error(f"Hardware profiles error: {hwprofiles_result.stderr}")
                    if "Unable to sign in" in hwprofiles_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                    elif "command not found" in hwprofiles_result.stderr:
                        logger.error("Genymotion Desktop not installed or gmtool not in PATH")
                return False

            if not osimages_result or osimages_result.returncode != 0:
                logger.error("Failed to get OS images")
                if osimages_result and osimages_result.stderr:
                    logger.error(f"OS images error: {osimages_result.stderr}")
                    if "Unable to sign in" in osimages_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                return False

            # Parse hardware profiles correctly
            hwprofile_name = self._parse_hardware_profile(hwprofiles_result.stdout)
            if not hwprofile_name:
                logger.error("No suitable hardware profile found")
                logger.error("Available profiles:")
                for line in hwprofiles_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using hardware profile: {hwprofile_name}")

            # Parse OS images correctly
            osimage_name = self._parse_os_image(osimages_result.stdout, android_version)
            if not osimage_name:
                logger.error("No suitable OS image found")
                logger.error("Available images:")
                for line in osimages_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using OS image: {osimage_name}")

            # Create the instance with proper syntax and enable root access
            create_cmd = ['admin', 'create', hwprofile_name, osimage_name, instance_name, '--root-access', 'on']
            logger.info(f"Executing: gmtool {' '.join(create_cmd)}")

            # Use longer timeout for device creation (can take up to 3 minutes)
            result = self._execute_gmtool_with_timeout(create_cmd, timeout=240)

            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' created successfully")
                return True
            else:
                logger.error(f"Failed to create instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                    logger.error(f"Return code: {result.returncode}")
                    self._provide_creation_troubleshooting(result.stderr)
                return False

        except Exception as e:
            logger.error(f"Failed to create instance {instance_name}: {e}")
            return False

    def open_genymotion_manager(self) -> bool:
        """Open Genymotion Desktop application"""
        try:
            logger.info("Opening Genymotion Desktop")

            # Check if Genymotion exists
            genymotion_app_path = '/Applications/Genymotion.app'
            if not os.path.exists(genymotion_app_path):
                logger.error(f"Genymotion not found at: {genymotion_app_path}")
                return False

            # Open Genymotion using the 'open' command
            try:
                subprocess.Popen(['open', '-a', 'Genymotion'], start_new_session=True)
                time.sleep(2)
                logger.info("Genymotion Desktop opened successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to open Genymotion: {e}")
                return False

        except Exception as e:
            logger.error(f"Failed to open Genymotion Desktop: {e}")
            return False

    def start_instance_by_name(self, instance_name: str) -> bool:
        """Start a specific Genymotion instance by name"""
        return self.start_instance(instance_name)

    def get_current_profile(self) -> Optional[Dict]:
        return self.current_profile

    def reset_instance(self, instance_name: str = "Pixel") -> bool:
        logger.info(f"Resetting Genymotion instance: {instance_name}")

        try:
            # Stop the instance first
            self.stop_instance(instance_name)
            time.sleep(2)

            # Factory reset the instance
            result = self._execute_gmtool(['admin', 'factoryreset', instance_name])
            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' reset successfully")

                # Reconfigure with new profile
                return self.configure_genymotion_instance(instance_name)
            else:
                logger.error(f"Failed to reset instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Failed to reset Genymotion instance: {e}")
            return False

    # Backward compatibility methods
    def configure_bluestacks_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        """Backward compatibility method"""
        return self.configure_genymotion_instance(instance_name, profile)

    def open_multi_instance_manager(self) -> bool:
        """Backward compatibility method"""
        return self.open_genymotion_manager()

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using genyshell"""
        try:
            command = f"gps setlatitude {latitude}"
            result1 = self._execute_genyshell(command, device_name)

            command = f"gps setlongitude {longitude}"
            result2 = self._execute_genyshell(command, device_name)

            if result1 and result2 and result1.returncode == 0 and result2.returncode == 0:
                logger.info(f"GPS location set to {latitude}, {longitude}")
                return True
            else:
                logger.error("Failed to set GPS location")
                return False

        except Exception as e:
            logger.error(f"Error setting GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using genyshell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            command = f"battery setlevel {level}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Battery level set to {level}%")
                return True
            else:
                logger.error("Failed to set battery level")
                return False

        except Exception as e:
            logger.error(f"Error setting battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using genyshell"""
        try:
            valid_orientations = ['portrait', 'landscape', 'reverse_portrait', 'reverse_landscape']
            if orientation not in valid_orientations:
                logger.error(f"Invalid orientation. Must be one of: {valid_orientations}")
                return False

            command = f"rotation setorientation {orientation}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Device rotated to {orientation}")
                return True
            else:
                logger.error("Failed to rotate device")
                return False

        except Exception as e:
            logger.error(f"Error rotating device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using genyshell"""
        try:
            command = f"phone call {phone_number}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Simulated phone call from {phone_number}")
                return True
            else:
                logger.error("Failed to simulate phone call")
                return False

        except Exception as e:
            logger.error(f"Error simulating phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using genyshell"""
        try:
            command = f"phone sms {phone_number} \"{message}\""
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"SMS sent from {phone_number}: {message}")
                return True
            else:
                logger.error("Failed to send SMS")
                return False

        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            return False

    def get_device_adb_id(self, instance_name: str) -> Optional[str]:
        """Get ADB device ID for a Genymotion instance"""
        try:
            instances = self.get_available_instances()
            if instance_name in instances:
                instance_info = instances[instance_name]
                # Try adb_serial first, then device_id
                device_id = instance_info.get('adb_serial') or instance_info.get('device_id')
                if device_id:
                    logger.debug(f"Found device ID for {instance_name}: {device_id}")
                    return device_id
                else:
                    logger.warning(f"No ADB device ID found for instance: {instance_name}")
                    return None
            else:
                logger.error(f"Instance not found: {instance_name}")
                return None
        except Exception as e:
            logger.error(f"Error getting device ID for {instance_name}: {e}")
            return None

    def customize_device_identifiers(self, instance_name: str, randomize_all: bool = True,
                                   android_id: str = None, device_id: str = None,
                                   phone_number: str = None, operator_name: str = "Android") -> bool:
        """Comprehensive device identifier customization using Genymotion widgets"""
        try:
            logger.info(f"Customizing device identifiers for {instance_name}")
            success = True

            # Randomize or set Android ID (Identifiers widget)
            if randomize_all or android_id:
                android_success = self.set_android_id(instance_name, android_id)
                success &= android_success
                if android_success:
                    logger.info("✅ Android ID customized")
                else:
                    logger.warning("❌ Android ID customization failed")

            # Randomize or set Device ID/IMEI (Identifiers widget)
            if randomize_all or device_id:
                imei_success = self.set_device_id_imei(instance_name, device_id)
                success &= imei_success
                if imei_success:
                    logger.info("✅ Device ID/IMEI customized")
                else:
                    logger.warning("❌ Device ID/IMEI customization failed")

            # Set SIM operator and phone number (Baseband widget)
            if phone_number:
                sim_success = self.set_baseband_sim_operator(instance_name, operator_name, "310260", phone_number)
                success &= sim_success
                if sim_success:
                    logger.info("✅ SIM operator and phone number customized")
                else:
                    logger.warning("❌ SIM operator customization failed")

            # Set up advanced Magisk anti-detection system
            try:
                logger.info("🛡️ Setting up advanced Magisk anti-detection system...")
                magisk_success = self.install_advanced_magisk_spoofing(instance_name)
                if magisk_success:
                    logger.info("✅ Advanced anti-detection module installed successfully")

                    # Reboot device to activate all anti-detection measures
                    reboot_success = self.reboot_device_for_magisk(instance_name)
                    if reboot_success:
                        logger.info("✅ Device rebooted successfully")

                        # Wait additional time for all anti-detection measures to activate
                        logger.info("⏳ Waiting for anti-detection measures to fully activate...")
                        time.sleep(15)

                        # Install and configure Frida for runtime hooking
                        frida_success = self.install_frida_server(instance_name)
                        if frida_success:
                            logger.info("✅ Frida server installed and running")

                            # Create comprehensive Frida bypass script
                            script_success = self.create_frida_bypass_script(instance_name)
                            if script_success:
                                logger.info("✅ Frida bypass script created successfully")
                            else:
                                logger.warning("⚠️ Frida bypass script creation failed")
                        else:
                            logger.warning("⚠️ Frida server installation failed - runtime hooking unavailable")

                        # Run comprehensive verification
                        verification = self.verify_anti_detection_setup(instance_name)
                        if verification.get('overall_success'):
                            logger.info("✅ Advanced anti-detection system is fully active")
                            logger.info("🛡️ Multi-layer protection: Magisk + Frida + Property spoofing")
                        else:
                            logger.warning("⚠️ Some anti-detection measures may need additional time")
                    else:
                        logger.warning("⚠️ Device reboot failed, manual reboot may be required")
                else:
                    logger.warning("❌ Advanced anti-detection setup failed, falling back to basic spoofing")
                    # Fallback to basic property spoofing
                    fire7_success = self.set_amazon_fire7_properties(instance_name)
                    magisk_success = fire7_success

                success &= magisk_success
            except Exception as e:
                logger.error(f"Error setting up advanced anti-detection: {e}")
                # Fallback to basic property spoofing
                try:
                    fire7_success = self.set_amazon_fire7_properties(instance_name)
                    success &= fire7_success
                except Exception as fallback_e:
                    logger.error(f"Fallback spoofing also failed: {fallback_e}")

            # Get and log final identifiers
            try:
                identifiers = self.get_device_identifiers(instance_name)
                if identifiers:
                    logger.info(f"Final device identifiers:")
                    for key, value in identifiers.items():
                        logger.info(f"  {key}: {value}")

                # Run comprehensive anti-detection verification
                self.verify_anti_detection_setup(instance_name)

                # Print device properties diagnostic to verify spoofing
                self.print_device_properties_diagnostic(instance_name)

                # Print GSM properties diagnostic for analysis
                self.print_gsm_properties_diagnostic(instance_name)

            except Exception as e:
                logger.debug(f"Could not retrieve final identifiers: {e}")

            if success:
                logger.info(f"✅ All device identifiers customized successfully for {instance_name}")
            else:
                logger.warning(f"⚠️ Some device identifier customizations failed for {instance_name}")

            return success

        except Exception as e:
            logger.error(f"Error customizing device identifiers: {e}")
            return False

    def customize_android_device_properties(self, instance_name: str, device_name: str = None, phone_number: str = None) -> bool:
        """Customize Android device properties like device name and phone number"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for instance: {instance_name}")
                return False

            success = True

            # Set Android device name if provided
            if device_name:
                success &= self._set_android_device_name(device_id, device_name)
                # Note: Genymotion system properties should be set before device start

            # Use the new comprehensive identifier customization
            if phone_number:
                identifier_success = self.customize_device_identifiers(
                    instance_name,
                    randomize_all=True,  # Randomize Android ID and IMEI
                    phone_number=phone_number
                )
                success &= identifier_success

            # Reboot device to apply persistent properties
            if device_name and success:
                logger.info(f"Rebooting device {instance_name} to apply persistent properties...")
                try:
                    reboot_result = subprocess.run(f'adb -s {device_id} reboot', shell=True, capture_output=True, text=True, timeout=30)
                    if reboot_result.returncode == 0:
                        logger.info(f"Device {instance_name} rebooted successfully")
                        # Wait a moment for reboot to start
                        import time
                        time.sleep(3)
                    else:
                        logger.warning(f"Device reboot may have failed: {reboot_result.stderr}")
                except Exception as e:
                    logger.warning(f"Failed to reboot device: {e}")

            return success

        except Exception as e:
            logger.error(f"Error customizing Android device properties: {e}")
            return False

    def _set_android_device_name(self, device_id: str, device_name: str) -> bool:
        """Set Android device name using ADB"""
        try:
            # Set device name in Android settings and persistent properties
            commands = [
                f'adb -s {device_id} shell settings put global device_name "{device_name}"',
                f'adb -s {device_id} shell settings put secure bluetooth_name "{device_name}"',
                f'adb -s {device_id} shell setprop net.hostname "{device_name}"',
                # Set persistent properties that survive reboot (Genymotion method)
                f'adb -s {device_id} shell setprop persist.ro.product.model "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.device "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.name "{device_name}"'
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode != 0:
                        logger.warning(f"Command failed: {cmd}, Error: {result.stderr}")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Command timed out: {cmd}")
                except Exception as e:
                    logger.warning(f"Command error: {cmd}, Error: {e}")

            logger.info(f"Android device name and persistent properties set to: {device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Android device name: {e}")
            return False

    def _set_genymotion_model_properties(self, instance_name: str, device_name: str) -> bool:
        """Set Genymotion system properties using gmtool admin edit"""
        try:
            # Use gmtool to set system properties that show up in device info
            edit_commands = [
                ['admin', 'edit', instance_name, '--sysprop', f'MODEL:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'PRODUCT:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'DEVICE:{device_name}']
            ]

            for cmd in edit_commands:
                try:
                    result = self._execute_gmtool(cmd)
                    if not result or result.returncode != 0:
                        logger.warning(f"Gmtool command failed: {' '.join(cmd)}")
                        if result and result.stderr:
                            logger.warning(f"Error: {result.stderr}")
                except Exception as e:
                    logger.warning(f"Gmtool command error: {' '.join(cmd)}, Error: {e}")

            logger.info(f"Genymotion system properties set for {instance_name}: MODEL={device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Genymotion system properties: {e}")
            return False



    def set_baseband_sim_operator(self, instance_name: str, operator_name: str = "Android",
                                  mcc_mnc: str = "310260", phone_number: str = None) -> bool:
        """Set SIM operator information using Genymotion Shell only"""
        try:
            logger.info(f"Setting SIM operator for {instance_name} using Genymotion Shell")
            logger.info(f"Operator: {operator_name}, MCC/MNC: {mcc_mnc}")

            # Genymotion Shell baseband commands
            baseband_commands = [
                "phone baseband gsm status",
                "phone baseband gsm voice home",
                "phone baseband gsm data home"
            ]

            success = False
            for command in baseband_commands:
                try:
                    logger.debug(f"Executing baseband command: {command}")
                    result = self._execute_genyshell_interactive(command, timeout=30)
                    if result and result.returncode == 0:
                        success = True
                        logger.debug(f"✅ Baseband command succeeded: {command}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Baseband command failed: {command} - {error_msg}")
                except Exception as e:
                    logger.debug(f"Baseband command error: {command}, Error: {e}")

            # Phone number setting via Genymotion Shell
            phone_success = False
            if phone_number:
                try:
                    logger.debug(f"Setting phone number via SMS simulation: {phone_number}")
                    sms_command = f"phone baseband sms send {phone_number} SIM_CONFIG"
                    result = self._execute_genyshell_interactive(sms_command, timeout=30)
                    if result and result.returncode == 0:
                        phone_success = True
                        logger.debug(f"✅ Phone number SMS simulation succeeded: {phone_number}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Phone number SMS simulation failed: {error_msg}")
                except Exception as e:
                    logger.debug(f"Phone number setting error: {e}")

            if success:
                logger.info(f"✅ SIM operator configured via Genymotion Shell: {operator_name} ({mcc_mnc})")
                if phone_number and phone_success:
                    logger.info(f"✅ Phone number configured: {phone_number}")
                elif phone_number:
                    logger.warning(f"⚠️ Phone number setting failed: {phone_number}")
            else:
                logger.warning(f"❌ SIM operator configuration failed")

            return success

        except Exception as e:
            logger.error(f"Failed to set SIM operator: {e}")
            return False

    def _set_genymotion_phone_number(self, instance_name: str, phone_number: str) -> bool:
        """Set phone number using Genymotion Shell baseband commands"""
        try:
            # Use the comprehensive baseband configuration
            return self.set_baseband_sim_operator(instance_name, phone_number=phone_number)

        except Exception as e:
            logger.error(f"Failed to set Genymotion phone number: {e}")
            return False

    def _set_android_phone_number(self, device_id: str, phone_number: str) -> bool:
        """Set Android phone number using multiple approaches for better compatibility"""
        try:
            success = False

            # Method 1: Set phone number in Android settings database
            settings_commands = [
                f'adb -s {device_id} shell settings put global device_provisioned 1',
                f'adb -s {device_id} shell settings put system phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put secure phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put global phone_number "{phone_number}"'
            ]

            for cmd in settings_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Settings command succeeded: {cmd}")
                    else:
                        logger.debug(f"Settings command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Settings command error: {cmd}, Error: {e}")

            # Method 2: Set telephony system properties
            telephony_commands = [
                f'adb -s {device_id} shell setprop gsm.sim.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop gsm.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop ro.telephony.default_network "9"',
                f'adb -s {device_id} shell setprop telephony.lteOnCdmaDevice "1"'
            ]

            for cmd in telephony_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Telephony command succeeded: {cmd}")
                    else:
                        logger.debug(f"Telephony command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Telephony command error: {cmd}, Error: {e}")

            # Method 3: Try to write to telephony database (with root)
            db_commands = [
                f'adb -s {device_id} shell su -c "mkdir -p /data/misc/radio"',
                f'adb -s {device_id} shell su -c "echo \'{phone_number}\' > /data/misc/radio/phone_number.txt"',
                f'adb -s {device_id} shell su -c "chmod 644 /data/misc/radio/phone_number.txt"'
            ]

            for cmd in db_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Database command succeeded: {cmd}")
                    else:
                        logger.debug(f"Database command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Database command error: {cmd}, Error: {e}")

            # Method 4: Verify phone number was set by checking device settings
            if success:
                # Try to verify the phone number was actually set
                verify_commands = [
                    f'adb -s {device_id} shell settings get system phone_number',
                    f'adb -s {device_id} shell settings get secure phone_number',
                    f'adb -s {device_id} shell settings get global phone_number'
                ]

                for cmd in verify_commands:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                        if result.returncode == 0 and result.stdout.strip() and result.stdout.strip() != 'null':
                            logger.info(f"Phone number verification successful: {result.stdout.strip()}")
                            break
                    except Exception as e:
                        logger.debug(f"Verification command error: {cmd}, Error: {e}")

                logger.info(f"Android phone number set to: {phone_number}")
            else:
                logger.warning(f"All phone number setting methods failed for: {phone_number}")

            return success

        except Exception as e:
            logger.error(f"Failed to set Android phone number: {e}")
            return False



    def set_android_id(self, instance_name: str, android_id: str = None) -> bool:
        """Set Android ID using Genymotion Shell only"""
        try:
            logger.info(f"Setting Android ID for {instance_name} using Genymotion Shell")

            if android_id is None:
                # Generate random Android ID
                command = "android setandroidid random"
                logger.debug("Using random Android ID generation")
            else:
                # Set custom Android ID (must be 16 hex digits)
                if len(android_id) != 16 or not all(c in '0123456789abcdefABCDEF' for c in android_id):
                    logger.error("Android ID must be exactly 16 hexadecimal digits")
                    return False
                command = f"android setandroidid custom {android_id}"
                logger.debug(f"Using custom Android ID: {android_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Android ID set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Android ID via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Android ID: {e}")
            return False



    def set_device_id_imei(self, instance_name: str, device_id: str = None) -> bool:
        """Set Device ID/IMEI using Genymotion Shell only"""
        try:
            logger.info(f"Setting Device ID/IMEI for {instance_name} using Genymotion Shell")

            if device_id is None:
                # Generate random Device ID/IMEI
                command = "android setdeviceid random"
                logger.debug("Using random Device ID/IMEI generation")
            elif device_id.lower() == "none":
                # Remove Device ID
                command = "android setdeviceid none"
                logger.debug("Removing Device ID")
            else:
                # Set custom Device ID (alphanumeric, dots, dashes, underscores)
                command = f"android setdeviceid custom {device_id}"
                logger.debug(f"Using custom Device ID: {device_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Device ID/IMEI set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Device ID/IMEI via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Device ID/IMEI: {e}")
            return False

    def get_device_identifiers(self, instance_name: str) -> dict:
        """Get current Android ID and Device ID/IMEI using Genymotion Shell"""
        try:
            logger.debug(f"Getting device identifiers for {instance_name}")
            identifiers = {}

            # Get Android ID
            try:
                result = self._execute_genyshell_interactive("android getandroidid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Android ID from output
                    for line in result.stdout.split('\n'):
                        if 'Android ID:' in line:
                            identifiers['android_id'] = line.split('Android ID:')[1].strip()
                            logger.debug(f"Found Android ID: {identifiers['android_id']}")
                            break
                else:
                    logger.debug("Failed to get Android ID")
            except Exception as e:
                logger.debug(f"Error getting Android ID: {e}")

            # Get Device ID/IMEI
            try:
                result = self._execute_genyshell_interactive("android getdeviceid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Device ID from output
                    for line in result.stdout.split('\n'):
                        if 'Device ID:' in line:
                            identifiers['device_id'] = line.split('Device ID:')[1].strip()
                            logger.debug(f"Found Device ID: {identifiers['device_id']}")
                            break
                else:
                    logger.debug("Failed to get Device ID")
            except Exception as e:
                logger.debug(f"Error getting Device ID: {e}")

            return identifiers

        except Exception as e:
            logger.error(f"Error getting device identifiers: {e}")
            return {}

    def randomize_device_identifiers(self, instance_name: str) -> bool:
        """Randomize both Android ID and Device ID/IMEI (like clicking shuffle buttons)"""
        try:
            logger.info(f"Randomizing device identifiers for {instance_name}")

            # Randomize Android ID
            android_success = self.set_android_id(instance_name)

            # Randomize Device ID/IMEI
            imei_success = self.set_device_id_imei(instance_name)

            if android_success and imei_success:
                logger.info(f"All device identifiers randomized successfully for {instance_name}")
                return True
            else:
                logger.warning(f"Some device identifiers failed to randomize for {instance_name}")
                return False

        except Exception as e:
            logger.error(f"Error randomizing device identifiers: {e}")
            return False

    def set_amazon_fire7_properties(self, instance_name: str) -> bool:
        """Spoof device as Amazon Fire 7 (9th Gen) using comprehensive build properties"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Setting Amazon Fire 7 properties for {instance_name}")

            # Amazon Fire 7 (9th Gen) comprehensive properties
            fire7_props = {
                # Manufacturer spoofing (all variants)
                'ro.product.manufacturer': 'Amazon',
                'ro.product.bootimage.manufacturer': 'Amazon',
                'ro.product.odm.manufacturer': 'Amazon',
                'ro.product.product.manufacturer': 'Amazon',
                'ro.product.system_ext.manufacturer': 'Amazon',
                'ro.product.vendor.manufacturer': 'Amazon',
                'ro.product.vendor_dlkm.manufacturer': 'Amazon',

                # Model spoofing (all variants)
                'ro.product.model': 'Fire 7',
                'ro.product.bootimage.model': 'Fire 7',
                'ro.product.odm.model': 'Fire 7',
                'ro.product.product.model': 'Fire 7',
                'ro.product.system_ext.model': 'Fire 7',
                'ro.product.vendor.model': 'Fire 7',
                'ro.product.vendor_dlkm.model': 'Fire 7',

                # Brand spoofing (all variants)
                'ro.product.brand': 'Amazon',
                'ro.product.bootimage.brand': 'Amazon',
                'ro.product.odm.brand': 'Amazon',
                'ro.product.product.brand': 'Amazon',
                'ro.product.system_ext.brand': 'Amazon',
                'ro.product.vendor.brand': 'Amazon',
                'ro.product.vendor_dlkm.brand': 'Amazon',

                # Device name spoofing (all variants)
                'ro.product.device': 'mantis',
                'ro.product.bootimage.device': 'mantis',
                'ro.product.odm.device': 'mantis',
                'ro.product.product.device': 'mantis',
                'ro.product.system_ext.device': 'mantis',
                'ro.product.vendor.device': 'mantis',
                'ro.product.vendor_dlkm.device': 'mantis',

                # Product name spoofing (all variants)
                'ro.product.name': 'mantis',
                'ro.product.bootimage.name': 'mantis',
                'ro.product.odm.name': 'mantis',
                'ro.product.product.name': 'mantis',
                'ro.product.system_ext.name': 'mantis',
                'ro.product.vendor.name': 'mantis',
                'ro.product.vendor_dlkm.name': 'mantis',

                # Build properties
                'ro.build.product': 'mantis',
                'ro.build.user': 'amazon',
                'ro.build.host': 'ip-10-0-0-123',

                # Build fingerprints (real Amazon Fire 7)
                'ro.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.bootimage.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.odm.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.product.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.system_ext.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.vendor.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.vendor_dlkm.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',

                # Build description
                'ro.build.description': 'mantis-user 9 PS7327 0065800820220419 release-keys',

                # Hardware fingerprint (remove Genymotion traces)
                'ro.hardware.fingerprint': 'qcom',

                # Additional realistic properties
                'ro.build.characteristics': 'tablet',
                'ro.carrier': 'unknown',
                'ro.product.cpu.abi': 'arm64-v8a',
                'ro.product.cpu.abilist': 'arm64-v8a,armeabi-v7a,armeabi',
                'ro.product.cpu.abilist32': 'armeabi-v7a,armeabi',
                'ro.product.cpu.abilist64': 'arm64-v8a',

                # GSM/Telephony properties
                'gsm.version.baseband': '8976.gen.prodQ-00253-M8976FAAAANAZM-1',
                'gsm.version.ril-impl': 'Qualcomm RIL 1.0',
                'ro.baseband': 'msm',

                # IMEI (realistic example)
                'persist.gsm.imei': '352187085963841',

                # Network operator
                'persist.gsm.operator': '310410',
                'persist.gsm.operator_name': 'AT&T',
                'persist.gsm.sim.operator': '310410',
                'persist.gsm.sim.operator_name': 'AT&T',

                # Remove Genymotion-specific properties
                'ro.genymotion.device.version': '',
                'ro.genyd.caps.baseband': ''
            }

            success_count = 0
            total_props = len(fire7_props)

            for prop, value in fire7_props.items():
                try:
                    cmd = f'adb -s {device_id} shell su -c "setprop {prop} \'{value}\'"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                        logger.debug(f"✅ Set {prop} = {value}")
                    else:
                        logger.debug(f"❌ Failed to set {prop}: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Error setting {prop}: {e}")

            success = success_count > 0
            if success:
                logger.info(f"✅ Amazon Fire 7 properties set: {success_count}/{total_props} successful")
                logger.info("📱 Device now appears as: Amazon Fire 7 (9th Gen)")
                logger.info("🔧 Genymotion-specific properties removed")
            else:
                logger.warning(f"❌ Failed to set Amazon Fire 7 properties")

            return success

        except Exception as e:
            logger.error(f"Error setting Amazon Fire 7 properties: {e}")
            return False

    def install_advanced_magisk_spoofing(self, instance_name: str) -> bool:
        """Install comprehensive Magisk anti-detection setup"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Installing advanced Magisk anti-detection setup for {instance_name}")

            # Step 1: Check if Magisk is already installed
            magisk_check = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(magisk_check, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Magisk is already installed")
            else:
                logger.info("📦 Magisk not found - attempting automatic installation")

                # Attempt automatic Magisk installation with multiple methods
                install_success = False

                # Method 1: Direct installation script
                logger.info("🔧 Trying Method 1: Direct installation script")
                install_success = self._auto_install_magisk(device_id, instance_name)

                if not install_success:
                    # Method 2: Magisk Manager app installation
                    logger.info("🔧 Trying Method 2: Magisk Manager app installation")
                    install_success = self._install_magisk_via_manager(device_id)

                if install_success:
                    logger.info("✅ Magisk installed successfully")

                    # Verify installation
                    verify_check = f'adb -s {device_id} shell su -c "which magisk"'
                    verify_result = subprocess.run(verify_check, shell=True, capture_output=True, text=True, timeout=10)
                    if verify_result.returncode != 0 or "magisk" not in verify_result.stdout:
                        logger.warning("⚠️ Magisk installation verification failed")

                        # Show installation guide
                        guide = self._create_magisk_installation_guide(device_id)
                        logger.info(guide)
                        return False
                else:
                    logger.warning("⚠️ All automatic Magisk installation methods failed")
                    logger.info("🔧 Attempting basic property spoofing without Magisk...")

                    # Try basic property spoofing for non-ro properties
                    basic_success = self._apply_basic_property_spoofing(device_id, instance_name)
                    if basic_success:
                        logger.info("✅ Basic property spoofing applied (limited effectiveness)")
                        logger.warning("⚠️ For full anti-detection, Magisk installation is recommended")
                    else:
                        logger.warning("❌ Basic property spoofing also failed")

                    # Show comprehensive installation guide
                    guide = self._create_magisk_installation_guide(device_id)
                    logger.info(guide)
                    return basic_success

            # Step 2: Create comprehensive anti-detection module
            module_path = "/data/adb/modules/fire7_anti_detection"

            # Create module directory structure
            commands = [
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/META-INF/com/google/android"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/system/bin"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/system/lib"'
            ]

            for cmd in commands:
                subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Step 3: Create advanced module.prop
            module_prop_content = '''id=fire7_anti_detection
name=Amazon Fire 7 Anti-Detection Suite
version=v2.0
versionCode=2
author=GenymotionAutomation
description=Comprehensive anti-detection for Amazon Fire 7 spoofing with automation hiding
updateJson=https://raw.githubusercontent.com/example/fire7-spoof/main/update.json
'''

            # Write module.prop
            prop_cmd = f'adb -s {device_id} shell su -c "echo \'{module_prop_content}\' > {module_path}/module.prop"'
            subprocess.run(prop_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Step 4: Create comprehensive system.prop file (based on advanced guide)
            system_prop_content = '''# Amazon Fire 7 Anti-Detection System Properties
# Device Information
ro.product.manufacturer=Amazon
ro.product.brand=Amazon
ro.product.name=mantis
ro.product.device=mantis
ro.product.model=Fire 7

# Build Information
ro.build.product=mantis
ro.build.device=mantis
ro.build.brand=Amazon
ro.build.manufacturer=Amazon
ro.build.model=Fire 7
ro.build.fingerprint=Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys
ro.build.description=mantis-user 9 PS7327 0065800820220419 release-keys
ro.build.display.id=PS7327
ro.build.id=PS7327
ro.build.version.release=9
ro.build.version.sdk=28
ro.build.version.incremental=0065800820220419
ro.build.tags=release-keys
ro.build.type=user
ro.build.user=amazon
ro.build.host=ip-10-0-1-234

# Hardware Information
ro.hardware=qcom
ro.hardware.fingerprint=qcom
ro.board.platform=msm8937
ro.chipname=msm8937

# Remove Genymotion traces
ro.genymotion.device.version=
ro.genyd.caps.baseband=

# Bootimage properties
ro.bootimage.build.fingerprint=Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys
ro.product.bootimage.brand=Amazon
ro.product.bootimage.device=mantis
ro.product.bootimage.manufacturer=Amazon
ro.product.bootimage.model=Fire 7
ro.product.bootimage.name=mantis

# System properties
ro.product.system.brand=Amazon
ro.product.system.device=mantis
ro.product.system.manufacturer=Amazon
ro.product.system.model=Fire 7
ro.product.system.name=mantis

# Vendor properties
ro.product.vendor.brand=Amazon
ro.product.vendor.device=mantis
ro.product.vendor.manufacturer=Amazon
ro.product.vendor.model=Fire 7
ro.product.vendor.name=mantis

# ODM properties
ro.product.odm.brand=Amazon
ro.product.odm.device=mantis
ro.product.odm.manufacturer=Amazon
ro.product.odm.model=Fire 7
ro.product.odm.name=mantis

# CPU Information
ro.product.cpu.abi=arm64-v8a
ro.product.cpu.abilist=arm64-v8a,armeabi-v7a,armeabi
ro.product.cpu.abilist32=armeabi-v7a,armeabi
ro.product.cpu.abilist64=arm64-v8a

# Network/Telephony
ro.telephony.default_network=9
ro.build.characteristics=tablet
gsm.version.baseband=8937.gen.prodQ-00031-M8937AAAAANLYD-1
gsm.version.ril-impl=Qualcomm RIL 1.0
ro.baseband=msm

# Security
ro.build.selinux=1
ro.secure=1
ro.debuggable=0
ro.adb.secure=1
'''

            # Write system.prop
            system_prop_cmd = f'adb -s {device_id} shell su -c "echo \'{system_prop_content}\' > {module_path}/system.prop"'
            subprocess.run(system_prop_cmd, shell=True, capture_output=True, text=True, timeout=15)

            # Step 5: Create advanced service.sh with anti-detection and automation hiding
            service_sh_content = '''#!/system/bin/sh
# Amazon Fire 7 Advanced Anti-Detection Service

# Wait for system to be ready
sleep 30

# Runtime property modifications
resetprop ro.boot.hardware qcom
resetprop ro.boot.bootdevice 7824900.sdhci
resetprop ro.boot.serialno HT7A$(cat /dev/urandom | tr -dc '0-9' | fold -w 7 | head -n 1)

# Hide emulator traces
resetprop ro.kernel.qemu ""
resetprop ro.kernel.qemu.gles ""
resetprop ro.product.board ""

# Reset Genymotion-specific properties
resetprop ro.genymotion.device.version ""
resetprop ro.genyd.caps.baseband ""

# Set realistic hardware values
resetprop persist.vendor.radio.enable_voicecall 1
resetprop persist.vendor.radio.calls.on.ims 1

# Hide automation frameworks
mount -o bind /dev/null /system/bin/uiautomator 2>/dev/null
mount -o bind /dev/null /system/framework/uiautomator.jar 2>/dev/null
mount -o bind /dev/null /system/app/RemoteTestRunner 2>/dev/null

# Hide Appium processes
am force-stop io.appium.uiautomator2.server 2>/dev/null
am force-stop io.appium.uiautomator2.server.test 2>/dev/null

# Network detection bypass
resetprop net.hostname android-device

# Create fake CPU info
cat > /data/local/tmp/fake_cpuinfo << 'EOF'
processor	: 0
model name	: ARMv8 Processor rev 4 (v8l)
BogoMIPS	: 38.40
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer	: 0x51
CPU architecture: 8
CPU variant	: 0xa
CPU part	: 0x801
CPU revision	: 4

processor	: 1
model name	: ARMv8 Processor rev 4 (v8l)
BogoMIPS	: 38.40
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer	: 0x51
CPU architecture: 8
CPU variant	: 0xa
CPU part	: 0x801
CPU revision	: 4
EOF

# Mount fake CPU info
mount -o bind /data/local/tmp/fake_cpuinfo /proc/cpuinfo 2>/dev/null

# Log completion
log -t "Fire7AntiDetect" "Amazon Fire 7 anti-detection setup completed"
'''

            # Write service.sh
            service_cmd = f'adb -s {device_id} shell su -c "echo \'{service_sh_content}\' > {module_path}/service.sh"'
            subprocess.run(service_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Make service.sh executable
            chmod_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/service.sh"'
            subprocess.run(chmod_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 6: Create enhanced post-fs-data.sh (based on EmuPropsSpoof guide)
            post_fs_content = '''#!/system/bin/sh
# Amazon Fire 7 Early Boot Anti-Detection (EmuPropsSpoof Enhanced)

# Core emulator property spoofing using resetprop
resetprop ro.kernel.qemu 0
resetprop ro.hardware qcom
resetprop ro.product.device mantis
resetprop ro.product.model "Fire 7"
resetprop ro.product.brand Amazon
resetprop ro.product.manufacturer Amazon
resetprop ro.build.fingerprint "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys"

# Additional Amazon Fire 7 specific properties
resetprop ro.build.product mantis
resetprop ro.build.device mantis
resetprop ro.build.description "mantis-user 9 PS7327 0065800820220419 release-keys"
resetprop ro.build.display.id PS7327
resetprop ro.build.id PS7327
resetprop ro.build.version.release 9
resetprop ro.build.version.sdk 28
resetprop ro.build.version.incremental 0065800820220419
resetprop ro.build.tags release-keys
resetprop ro.build.type user
resetprop ro.build.user amazon
resetprop ro.build.host ip-10-0-1-234

# Hardware spoofing
resetprop ro.board.platform msm8937
resetprop ro.chipname msm8937
resetprop ro.hardware.fingerprint qcom

# Remove Genymotion traces
resetprop ro.genymotion.device.version ""
resetprop ro.genyd.caps.baseband ""

# Remove emulator-specific files
rm -f /system/bin/qemu-props 2>/dev/null
rm -f /system/lib/libc_malloc_debug_qemu.so 2>/dev/null
rm -f /system/lib64/libc_malloc_debug_qemu.so 2>/dev/null

# Create fake version file
cat > /data/local/tmp/fake_version << 'EOF'
Linux version 4.14.117-perf+ (amazon@ip-10-0-1-234) (gcc version 4.9.x 20150123 (prerelease) (GCC)) #1 SMP PREEMPT Mon Apr 15 10:39:17 PDT 2019
EOF

# Hide automation testing packages
pm hide com.android.development 2>/dev/null
pm hide com.android.development_settings 2>/dev/null
pm hide com.example.android.apis 2>/dev/null
pm hide io.appium.uiautomator2.server 2>/dev/null
pm hide io.appium.uiautomator2.server.test 2>/dev/null

# Set realistic sensor properties
resetprop ro.hardware.sensors qcom
resetprop ro.qti.sensors.dev_ori 1
resetprop ro.qti.sensors.pmd 1
resetprop ro.qti.sensors.sta_detect 1

# Network properties
resetprop net.hostname android-device

# Security properties
resetprop ro.build.selinux 1
resetprop ro.secure 1
resetprop ro.debuggable 0
resetprop ro.adb.secure 1
'''

            # Write post-fs-data.sh
            post_fs_cmd = f'adb -s {device_id} shell su -c "echo \'{post_fs_content}\' > {module_path}/post-fs-data.sh"'
            subprocess.run(post_fs_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Make post-fs-data.sh executable
            chmod_post_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/post-fs-data.sh"'
            subprocess.run(chmod_post_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 7: Create config.sh for Magisk v25+ compatibility
            config_content = '''SKIPMOUNT=true
PROPFILE=false
POSTFSDATA=true
LATESTARTSERVICE=false
'''

            # Write config.sh
            config_cmd = f'adb -s {device_id} shell su -c "echo \'{config_content}\' > {module_path}/config.sh"'
            subprocess.run(config_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 8: Create customize.sh (empty but required)
            customize_content = '''#!/system/bin/sh
# Nothing to customize - all handled by post-fs-data.sh
'''

            # Write customize.sh
            customize_cmd = f'adb -s {device_id} shell su -c "echo \'{customize_content}\' > {module_path}/customize.sh"'
            subprocess.run(customize_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Make customize.sh executable
            chmod_customize_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/customize.sh"'
            subprocess.run(chmod_customize_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 9: Enable the module
            touch_cmd = f'adb -s {device_id} shell su -c "touch {module_path}/auto_mount"'
            subprocess.run(touch_cmd, shell=True, capture_output=True, text=True, timeout=5)

            logger.info("✅ Advanced Magisk anti-detection module created successfully")
            logger.info("📱 Amazon Fire 7 comprehensive spoofing installed")
            logger.info("🛡️ Anti-detection measures: Property spoofing, automation hiding, emulator traces removal")
            logger.info("🔄 Reboot required to activate all anti-detection measures")

            return True

        except Exception as e:
            logger.error(f"Error setting up Magisk spoofing: {e}")
            return False

    def reboot_device_for_magisk(self, instance_name: str) -> bool:
        """Reboot device to activate Magisk modules"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔄 Rebooting {instance_name} to activate Magisk modules...")

            # Reboot the device
            reboot_cmd = f'adb -s {device_id} reboot'
            result = subprocess.run(reboot_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                logger.info("✅ Device reboot initiated")

                # Wait for device to come back online
                logger.info("⏳ Waiting for device to come back online...")
                time.sleep(30)  # Wait for reboot

                # Check if device is back online
                for attempt in range(12):  # 2 minutes total
                    try:
                        check_cmd = f'adb -s {device_id} shell echo "online"'
                        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        if result.returncode == 0 and "online" in result.stdout:
                            logger.info("✅ Device is back online")
                            return True
                    except:
                        pass

                    logger.debug(f"Device not ready yet, attempt {attempt + 1}/12")
                    time.sleep(10)

                logger.warning("⚠️ Device took longer than expected to come back online")
                return False
            else:
                logger.error(f"❌ Failed to reboot device: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error rebooting device: {e}")
            return False

    def check_magisk_module_status(self, instance_name: str) -> dict:
        """Check if Magisk module is active and properties are spoofed"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            logger.info(f"🔍 Checking Magisk module status for {instance_name}")

            # Check if module directory exists
            module_check = f'adb -s {device_id} shell su -c "ls /data/adb/modules/amazon_fire7_spoof/"'
            result = subprocess.run(module_check, shell=True, capture_output=True, text=True, timeout=10)

            module_installed = result.returncode == 0

            # Check key spoofed properties
            key_props = {
                'ro.product.manufacturer': 'Amazon',
                'ro.product.model': 'Fire 7',
                'ro.build.fingerprint': 'Amazon/mantis/mantis',
                'ro.hardware.fingerprint': 'qcom',
                'ro.genymotion.device.version': ''
            }

            spoofed_props = {}
            spoofing_success = True

            for prop, expected in key_props.items():
                try:
                    prop_cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(prop_cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        spoofed_props[prop] = value

                        if expected and expected not in value:
                            spoofing_success = False
                        elif not expected and value:  # Should be empty
                            spoofing_success = False
                except Exception as e:
                    logger.debug(f"Failed to check property {prop}: {e}")
                    spoofing_success = False

            status = {
                'module_installed': module_installed,
                'spoofing_success': spoofing_success,
                'properties': spoofed_props
            }

            if module_installed and spoofing_success:
                logger.info("✅ Magisk module is active and spoofing is working")
            elif module_installed:
                logger.warning("⚠️ Magisk module installed but spoofing may not be complete")
            else:
                logger.warning("❌ Magisk module not found or not installed")

            return status

        except Exception as e:
            logger.error(f"Error checking Magisk module status: {e}")
            return {}

    def verify_anti_detection_setup(self, instance_name: str) -> dict:
        """Comprehensive anti-detection verification based on advanced guide"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            logger.info(f"🔍 Running comprehensive anti-detection verification for {instance_name}")

            verification_results = {
                'properties_spoofed': False,
                'genymotion_traces_removed': False,
                'emulator_files_hidden': False,
                'automation_hidden': False,
                'cpu_info_spoofed': False,
                'overall_success': False
            }

            # 1. Verify key properties are spoofed
            key_props = {
                'ro.product.manufacturer': 'Amazon',
                'ro.product.model': 'Fire 7',
                'ro.build.fingerprint': 'Amazon/mantis/mantis',
                'ro.hardware': 'qcom',
                'ro.hardware.fingerprint': 'qcom'
            }

            props_success = True
            for prop, expected in key_props.items():
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if expected not in value:
                            props_success = False
                            logger.debug(f"❌ Property {prop}: {value} (expected: {expected})")
                        else:
                            logger.debug(f"✅ Property {prop}: {value}")
                except Exception as e:
                    logger.debug(f"Failed to check property {prop}: {e}")
                    props_success = False

            verification_results['properties_spoofed'] = props_success

            # 2. Check Genymotion traces are removed
            geny_traces = ['ro.genymotion.device.version', 'ro.genyd.caps.baseband']
            geny_success = True
            for prop in geny_traces:
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value:  # Should be empty
                            geny_success = False
                            logger.debug(f"❌ Genymotion trace found: {prop}={value}")
                        else:
                            logger.debug(f"✅ Genymotion trace removed: {prop}")
                except Exception as e:
                    logger.debug(f"Failed to check Genymotion trace {prop}: {e}")

            verification_results['genymotion_traces_removed'] = geny_success

            # 3. Check emulator files are hidden/removed
            emulator_files = ['/system/bin/qemu-props', '/system/lib/libc_malloc_debug_qemu.so']
            files_success = True
            for file_path in emulator_files:
                try:
                    cmd = f'adb -s {device_id} shell ls {file_path}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:  # File exists
                        files_success = False
                        logger.debug(f"❌ Emulator file still exists: {file_path}")
                    else:
                        logger.debug(f"✅ Emulator file hidden/removed: {file_path}")
                except Exception as e:
                    logger.debug(f"Error checking emulator file {file_path}: {e}")

            verification_results['emulator_files_hidden'] = files_success

            # 4. Check CPU info is spoofed
            try:
                cmd = f'adb -s {device_id} shell cat /proc/cpuinfo | head -5'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    cpu_info = result.stdout.lower()
                    if 'armv8' in cpu_info and 'x86' not in cpu_info and 'intel' not in cpu_info:
                        verification_results['cpu_info_spoofed'] = True
                        logger.debug("✅ CPU info shows ARM processor")
                    else:
                        logger.debug(f"❌ CPU info suspicious: {cpu_info[:100]}")
            except Exception as e:
                logger.debug(f"Error checking CPU info: {e}")

            # 5. Check automation frameworks are hidden
            automation_files = ['/system/bin/uiautomator', '/system/framework/uiautomator.jar']
            automation_success = True
            for file_path in automation_files:
                try:
                    cmd = f'adb -s {device_id} shell ls -la {file_path}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and '/dev/null' not in result.stdout:
                        automation_success = False
                        logger.debug(f"❌ Automation file not hidden: {file_path}")
                    else:
                        logger.debug(f"✅ Automation file hidden: {file_path}")
                except Exception as e:
                    logger.debug(f"Error checking automation file {file_path}: {e}")

            verification_results['automation_hidden'] = automation_success

            # Overall success calculation
            success_count = sum([
                verification_results['properties_spoofed'],
                verification_results['genymotion_traces_removed'],
                verification_results['emulator_files_hidden'],
                verification_results['cpu_info_spoofed'],
                verification_results['automation_hidden']
            ])

            verification_results['overall_success'] = success_count >= 4  # At least 4/5 checks pass

            # Log results
            logger.info("🔍 Anti-Detection Verification Results:")
            logger.info("=" * 60)
            logger.info(f"  📱 Properties Spoofed: {'✅' if verification_results['properties_spoofed'] else '❌'}")
            logger.info(f"  🚫 Genymotion Traces Removed: {'✅' if verification_results['genymotion_traces_removed'] else '❌'}")
            logger.info(f"  📁 Emulator Files Hidden: {'✅' if verification_results['emulator_files_hidden'] else '❌'}")
            logger.info(f"  🤖 Automation Hidden: {'✅' if verification_results['automation_hidden'] else '❌'}")
            logger.info(f"  💻 CPU Info Spoofed: {'✅' if verification_results['cpu_info_spoofed'] else '❌'}")
            logger.info("=" * 60)

            if verification_results['overall_success']:
                logger.info("✅ Anti-detection verification PASSED - Device appears as real Amazon Fire 7")
            else:
                logger.warning(f"⚠️ Anti-detection verification PARTIAL - {success_count}/5 checks passed")

            return verification_results

        except Exception as e:
            logger.error(f"Error running anti-detection verification: {e}")
            return {}

    def install_frida_server(self, instance_name: str) -> bool:
        """Install and configure Frida server for runtime hooking"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Installing Frida server for runtime hooking on {instance_name}")

            # Check if Frida server is already running
            check_cmd = f'adb -s {device_id} shell ps | grep frida-server'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "frida-server" in result.stdout:
                logger.info("✅ Frida server is already running")
                return True

            # Download and install Frida server (assuming it's available locally)
            frida_server_path = "/data/local/tmp/frida-server"

            # Check if frida-server binary exists
            check_binary = f'adb -s {device_id} shell ls {frida_server_path}'
            result = subprocess.run(check_binary, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode != 0:
                logger.warning("⚠️ Frida server binary not found on device")
                logger.info("📦 Please install frida-server manually:")
                logger.info(f"   1. Download frida-server for Android x86/ARM")
                logger.info(f"   2. adb -s {device_id} push frida-server {frida_server_path}")
                logger.info(f"   3. adb -s {device_id} shell chmod 755 {frida_server_path}")
                return False

            # Make frida-server executable and start it
            commands = [
                f'adb -s {device_id} shell su -c "chmod 755 {frida_server_path}"',
                f'adb -s {device_id} shell su -c "nohup {frida_server_path} > /dev/null 2>&1 &"'
            ]

            for cmd in commands:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    logger.error(f"Failed to execute: {cmd}")
                    return False

            # Wait a moment for server to start
            time.sleep(3)

            # Verify Frida server is running
            check_cmd = f'adb -s {device_id} shell ps | grep frida-server'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and "frida-server" in result.stdout:
                logger.info("✅ Frida server started successfully")
                return True
            else:
                logger.error("❌ Failed to start Frida server")
                return False

        except Exception as e:
            logger.error(f"Error installing Frida server: {e}")
            return False

    def create_frida_bypass_script(self, instance_name: str) -> bool:
        """Create comprehensive Frida bypass script for runtime hooking"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Creating comprehensive Frida bypass script for {instance_name}")

            # Comprehensive Frida bypass script (based on your guide)
            frida_script = '''Java.perform(() => {
    console.log("🛡️ Amazon Fire 7 Anti-Detection Frida Script Loaded");

    // === 1. System Property Check Bypass ===
    const SystemProperties = Java.use("android.os.SystemProperties");
    SystemProperties.get.overload("java.lang.String").implementation = function (key) {
        // Amazon Fire 7 property spoofing
        if (key === "ro.kernel.qemu") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> 0`);
            return "0";
        } else if (key === "ro.hardware") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> qcom`);
            return "qcom";
        } else if (key === "ro.product.device") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> mantis`);
            return "mantis";
        } else if (key === "ro.product.model") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Fire 7`);
            return "Fire 7";
        } else if (key === "ro.product.manufacturer") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon`);
            return "Amazon";
        } else if (key === "ro.product.brand") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon`);
            return "Amazon";
        } else if (key === "ro.build.fingerprint") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon fingerprint`);
            return "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys";
        } else if (key === "ro.genymotion.device.version") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> empty`);
            return "";
        } else if (key === "ro.genyd.caps.baseband") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> empty`);
            return "";
        }
        return this.get(key);
    };

    // === 2. File System Check Bypass ===
    const File = Java.use("java.io.File");
    File.exists.implementation = function () {
        const path = this.getAbsolutePath();
        if (
            path.includes("qemu_pipe") ||
            path.includes("genyd") ||
            path.includes("goldfish") ||
            path.includes("ranchu") ||
            path.includes("libc_malloc_debug_qemu") ||
            path.includes("genymotion") ||
            path.includes("qemu-props") ||
            path.includes("ueventd.android_x86.rc") ||
            path.includes("fstab.android_x86")
        ) {
            console.log(`✅ Bypassing file check for: ${path}`);
            return false;
        }
        return this.exists();
    };

    // === 3. Network Properties Bypass ===
    const WifiInfo = Java.use("android.net.wifi.WifiInfo");
    const NetworkInfo = Java.use("android.net.NetworkInfo");

    WifiInfo.getMacAddress.implementation = function () {
        console.log("✅ Spoofing MAC address");
        return "02:00:00:00:00:00"; // Realistic Fire 7 MAC
    };

    WifiInfo.getIpAddress.implementation = function () {
        console.log("✅ Spoofing IP address");
        return 3232235777; // ***********
    };

    NetworkInfo.isConnected.implementation = function () {
        console.log("✅ Spoofing network connection status");
        return true;
    };

    // === 4. Sensor Check Bypass ===
    const SensorManager = Java.use("android.hardware.SensorManager");
    const Sensor = Java.use("android.hardware.Sensor");

    SensorManager.getDefaultSensor.implementation = function (type) {
        console.log("✅ Intercepted getDefaultSensor for type: " + type);
        const sensor = this.getDefaultSensor(type);
        if (sensor === null) {
            console.log("✅ Returning dummy sensor for type: " + type);
            // Return the original result but log the interception
        }
        return sensor;
    };

    SensorManager.registerListener.overload(
        "android.hardware.SensorEventListener",
        "android.hardware.Sensor",
        "int"
    ).implementation = function (listener, sensor, delay) {
        console.log("✅ Spoofing sensor listener for type: " + sensor.getType());
        const result = this.registerListener(listener, sensor, delay);

        // Send realistic Fire 7 sensor data
        setTimeout(() => {
            sendRealisticSensorData(listener, sensor.getType());
        }, 1000);

        return result;
    };

    function sendRealisticSensorData(listener, type) {
        Java.scheduleOnMainThread(() => {
            try {
                const SensorEvent = Java.use("android.hardware.SensorEvent");
                const sensorEvent = SensorEvent.$new(3);

                if (type === 1) { // Accelerometer
                    sensorEvent.values = [0.1, 9.8, 0.2]; // Realistic tablet orientation
                } else if (type === 4) { // Gyroscope
                    sensorEvent.values = [0.01, 0.02, 0.01]; // Minimal movement
                } else if (type === 2) { // Magnetometer
                    sensorEvent.values = [25.0, -15.0, 45.0]; // Realistic magnetic field
                } else {
                    sensorEvent.values = [1.0, 1.0, 1.0];
                }

                listener.onSensorChanged(sensorEvent);
                console.log(`✅ Sent realistic Fire 7 sensor data for type ${type}`);
            } catch (err) {
                console.error("❌ Sensor spoof error:", err);
            }
        });
    }

    // === 5. Build Information Bypass ===
    const Build = Java.use("android.os.Build");
    Build.MANUFACTURER.value = "Amazon";
    Build.MODEL.value = "Fire 7";
    Build.BRAND.value = "Amazon";
    Build.DEVICE.value = "mantis";
    Build.PRODUCT.value = "mantis";
    Build.HARDWARE.value = "qcom";
    Build.FINGERPRINT.value = "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys";

    // === 6. Package Manager Bypass ===
    const PackageManager = Java.use("android.content.pm.PackageManager");
    PackageManager.getInstalledPackages.overload("int").implementation = function(flags) {
        const packages = this.getInstalledPackages(flags);
        const filteredPackages = [];

        for (let i = 0; i < packages.size(); i++) {
            const pkg = packages.get(i);
            const packageName = pkg.packageName.value;

            // Hide automation/testing packages
            if (!packageName.includes("uiautomator") &&
                !packageName.includes("appium") &&
                !packageName.includes("frida") &&
                !packageName.includes("xposed")) {
                filteredPackages.push(pkg);
            } else {
                console.log(`✅ Hiding package: ${packageName}`);
            }
        }

        return Java.use("java.util.ArrayList").$new(filteredPackages);
    };

    console.log("✅ Amazon Fire 7 Anti-Detection Frida Script Active - All hooks loaded");
});'''

            # Write Frida script to device
            script_path = "/data/local/tmp/fire7_bypass.js"

            # Create script file
            script_cmd = f'adb -s {device_id} shell su -c "echo \'{frida_script}\' > {script_path}"'
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                logger.info("✅ Frida bypass script created successfully")
                logger.info(f"📁 Script location: {script_path}")
                logger.info("🔧 Usage: frida -U -n <app_package> -l /data/local/tmp/fire7_bypass.js")
                return True
            else:
                logger.error("❌ Failed to create Frida bypass script")
                return False

        except Exception as e:
            logger.error(f"Error creating Frida bypass script: {e}")
            return False

    def _auto_install_magisk(self, device_id: str, instance_name: str) -> bool:
        """Automatically install Magisk using multiple robust download methods"""
        try:
            logger.info(f"🔧 Starting comprehensive Magisk installation for {instance_name}")

            # Step 1: Try multiple download approaches
            if self._download_magisk_comprehensive(device_id):
                logger.info("✅ Magisk files successfully obtained")
            else:
                logger.error("❌ All Magisk download methods failed")
                return False

            # Step 2: Install Magisk Manager APK
            logger.info("📱 Installing Magisk Manager APK...")
            install_cmd = f'adb -s {device_id} shell su -c "pm install /sdcard/Magisk.apk"'
            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True, timeout=60)

            if result.returncode != 0:
                logger.error(f"❌ Failed to install Magisk APK: {result.stderr}")
                return False

            logger.info("✅ Magisk Manager APK installed successfully")

            # Step 3: Install Magisk using direct install method
            logger.info("🔧 Installing Magisk via direct install method...")

            # Create installation script that works with downloaded files
            install_script = '''#!/system/bin/sh
# Magisk Direct Install Script

MAGISK_ZIP="/sdcard/Magisk.zip"
INSTALL_DIR="/data/local/tmp/magisk_install"

# Create installation directory
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# Extract Magisk ZIP
unzip -o "$MAGISK_ZIP"

# Make installer executable
chmod 755 META-INF/com/google/android/update-binary

# Remount system as read-write
mount -o remount,rw /system

# Run Magisk installer
sh META-INF/com/google/android/update-binary dummy 1 "$MAGISK_ZIP"

# Install Magisk binary if available
if [ -f "magisk" ]; then
    cp magisk /system/bin/magisk
    chmod 755 /system/bin/magisk

    # Create Magisk directories
    mkdir -p /data/adb/magisk
    mkdir -p /data/adb/modules

    # Set up Magisk daemon
    /system/bin/magisk --daemon &

    echo "Magisk installation completed successfully"
else
    echo "Magisk binary not found, but installation may have succeeded"
fi

# Install Magisk app as system app
if [ -f "/sdcard/Magisk.apk" ]; then
    cp /sdcard/Magisk.apk /system/app/Magisk.apk
    chmod 644 /system/app/Magisk.apk
fi

echo "Installation process finished"
'''

            # Write installation script to device
            script_path = "/data/local/tmp/install_magisk.sh"
            script_cmd = f'adb -s {device_id} shell su -c "echo \'{install_script}\' > {script_path}"'
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                logger.error(f"❌ Failed to create installation script: {result.stderr}")
                return False

            # Make script executable and run it
            chmod_cmd = f'adb -s {device_id} shell su -c "chmod 755 {script_path}"'
            subprocess.run(chmod_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Remount system as read-write
            remount_cmd = f'adb -s {device_id} shell su -c "mount -o remount,rw /system"'
            subprocess.run(remount_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Execute installation script
            logger.info("⚙️ Executing Magisk installation...")
            exec_cmd = f'adb -s {device_id} shell su -c "sh {script_path}"'
            result = subprocess.run(exec_cmd, shell=True, capture_output=True, text=True, timeout=180)

            if result.returncode == 0 and ("installation completed" in result.stdout or "Installation process finished" in result.stdout):
                logger.info("✅ Magisk installation script completed successfully")

                # Step 4: Try alternative installation via Magisk Manager
                logger.info("🔧 Attempting Magisk Manager installation as backup...")
                self._install_magisk_via_manager_automated(device_id)

                # Step 5: Reboot device to activate Magisk
                logger.info("🔄 Rebooting device to activate Magisk...")
                reboot_cmd = f'adb -s {device_id} reboot'
                subprocess.run(reboot_cmd, shell=True, capture_output=True, text=True, timeout=10)

                # Wait for device to come back online
                logger.info("⏳ Waiting for device to restart...")
                time.sleep(45)  # Wait for reboot

                # Wait for ADB to be ready
                for attempt in range(20):  # 2 minutes total
                    try:
                        check_cmd = f'adb -s {device_id} shell echo "ready"'
                        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        if result.returncode == 0 and "ready" in result.stdout:
                            logger.info("✅ Device is back online after Magisk installation")

                            # Final verification
                            verify_cmd = f'adb -s {device_id} shell su -c "which magisk"'
                            verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)
                            if verify_result.returncode == 0 and "magisk" in verify_result.stdout:
                                logger.info("✅ Magisk installation verified successfully")
                                return True
                            else:
                                logger.warning("⚠️ Magisk installation may need additional setup")
                                return True  # Return true as installation likely succeeded
                    except:
                        pass

                    logger.debug(f"Waiting for device to be ready, attempt {attempt + 1}/20")
                    time.sleep(6)

                logger.warning("⚠️ Device took longer than expected to restart")
                return True  # Assume success even if we can't verify immediately

            else:
                logger.error(f"❌ Magisk installation script failed: {result.stderr}")
                logger.info("🔧 Trying alternative Magisk Manager installation...")
                return self._install_magisk_via_manager_automated(device_id)

        except Exception as e:
            logger.error(f"Error during automatic Magisk installation: {e}")
            return False



    def _apply_basic_property_spoofing(self, device_id: str, instance_name: str) -> bool:
        """Apply basic property spoofing for properties that can be modified without Magisk"""
        try:
            logger.info("🔧 Applying basic property spoofing (non-ro properties)")

            # Properties that can potentially be modified without Magisk
            basic_properties = {
                # GSM/Network properties (some may be writable)
                'gsm.operator.alpha': 'Amazon Wireless',
                'gsm.sim.operator.alpha': 'Amazon Wireless',
                'net.hostname': 'amazon-fire7',

                # Debug properties (usually writable)
                'debug.tracing.mcc': '310',
                'debug.tracing.mnc': '410',

                # Persist properties (may be writable)
                'persist.vendor.radio.enable_voicecall': '1',
                'persist.vendor.radio.calls.on.ims': '1',

                # System properties that might be writable
                'sys.usb.config': 'mtp,adb',
                'sys.boot_completed': '1'
            }

            success_count = 0
            total_count = len(basic_properties)

            for prop, value in basic_properties.items():
                try:
                    # Try setting property without su first
                    cmd = f'adb -s {device_id} shell setprop {prop} "{value}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                    if result.returncode == 0:
                        # Verify the property was set
                        verify_cmd = f'adb -s {device_id} shell getprop {prop}'
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=5)

                        if verify_result.returncode == 0 and value in verify_result.stdout.strip():
                            success_count += 1
                            logger.debug(f"✅ Set {prop} = {value}")
                        else:
                            logger.debug(f"❌ Failed to verify {prop}")
                    else:
                        logger.debug(f"❌ Failed to set {prop}: {result.stderr}")

                except Exception as e:
                    logger.debug(f"Error setting property {prop}: {e}")

            # Try to hide some Genymotion traces using available methods
            try:
                # Rename Genymotion-specific files (if possible)
                hide_commands = [
                    f'adb -s {device_id} shell su -c "mv /dev/socket/genyd /dev/socket/_genyd_hidden" 2>/dev/null',
                    f'adb -s {device_id} shell su -c "chmod 000 /system/bin/qemu-props" 2>/dev/null'
                ]

                for cmd in hide_commands:
                    try:
                        subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    except:
                        pass

            except Exception as e:
                logger.debug(f"Error hiding Genymotion traces: {e}")

            success_rate = success_count / total_count if total_count > 0 else 0
            logger.info(f"📊 Basic property spoofing: {success_count}/{total_count} ({success_rate:.1%})")

            if success_count > 0:
                logger.info("✅ Basic property spoofing partially successful")
                logger.warning("⚠️ Limited effectiveness - Magisk required for full spoofing")
                return True
            else:
                logger.warning("❌ Basic property spoofing failed")
                return False

        except Exception as e:
            logger.error(f"Error applying basic property spoofing: {e}")
            return False

    def _download_magisk_comprehensive(self, device_id: str) -> bool:
        """Comprehensive Magisk download using multiple methods and sources"""
        try:
            logger.info("🔧 Starting comprehensive Magisk download with multiple fallback methods")

            # Multiple Magisk versions and sources
            magisk_sources = [
                # Official GitHub releases
                {
                    "version": "v27.0",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.apk",
                    "zip_url": "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.zip"
                },
                {
                    "version": "v26.4",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk",
                    "zip_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.zip"
                },
                {
                    "version": "v26.3",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.3/Magisk-v26.3.apk",
                    "zip_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.3/Magisk-v26.3.zip"
                },
                {
                    "version": "v26.1",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.1/Magisk-v26.1.apk",
                    "zip_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.1/Magisk-v26.1.zip"
                }
            ]

            for source in magisk_sources:
                logger.info(f"📥 Trying Magisk {source['version']}...")

                # Method 1: Host download + ADB push (most reliable)
                if self._download_method_host_push(device_id, source):
                    return True

                # Method 2: Device download using multiple tools
                if self._download_method_device_direct(device_id, source):
                    return True

                # Method 3: Alternative download methods
                if self._download_method_alternative(device_id, source):
                    return True

            logger.error("❌ All comprehensive download methods failed")
            return False

        except Exception as e:
            logger.error(f"Error in comprehensive Magisk download: {e}")
            return False

    def _download_method_host_push(self, device_id: str, source: dict) -> bool:
        """Method 1: Download on host machine and push via ADB"""
        try:
            logger.debug(f"📥 Method 1: Host download + ADB push for {source['version']}")

            import urllib.request
            import tempfile
            import os

            # Create temporary files
            with tempfile.NamedTemporaryFile(suffix='.apk', delete=False) as apk_file:
                apk_path = apk_file.name
            with tempfile.NamedTemporaryFile(suffix='.zip', delete=False) as zip_file:
                zip_path = zip_file.name

            try:
                # Download APK with multiple methods
                logger.debug("📥 Downloading APK on host...")
                success = False

                # Try urllib first
                try:
                    urllib.request.urlretrieve(source['apk_url'], apk_path)
                    success = True
                    logger.debug("✅ urllib APK download successful")
                except Exception as e:
                    logger.debug(f"urllib failed for APK: {e}")

                # Try requests if urllib fails
                if not success:
                    try:
                        import requests
                        response = requests.get(source['apk_url'], timeout=60)
                        response.raise_for_status()
                        with open(apk_path, 'wb') as f:
                            f.write(response.content)
                        success = True
                        logger.debug("✅ requests APK download successful")
                    except Exception as e:
                        logger.debug(f"requests failed for APK: {e}")

                # Try curl as last resort
                if not success:
                    try:
                        curl_cmd = f'curl -L -o "{apk_path}" "{source["apk_url"]}"'
                        result = subprocess.run(curl_cmd, shell=True, capture_output=True, text=True, timeout=120)
                        if result.returncode == 0:
                            success = True
                            logger.debug("✅ curl APK download successful")
                    except Exception as e:
                        logger.debug(f"curl failed for APK: {e}")

                if not success:
                    raise Exception("All APK download methods failed")

                # Download ZIP with same methods
                logger.debug("📥 Downloading ZIP on host...")
                success = False

                # Try urllib first
                try:
                    urllib.request.urlretrieve(source['zip_url'], zip_path)
                    success = True
                    logger.debug("✅ urllib ZIP download successful")
                except Exception as e:
                    logger.debug(f"urllib failed for ZIP: {e}")

                # Try requests if urllib fails
                if not success:
                    try:
                        import requests
                        response = requests.get(source['zip_url'], timeout=60)
                        response.raise_for_status()
                        with open(zip_path, 'wb') as f:
                            f.write(response.content)
                        success = True
                        logger.debug("✅ requests ZIP download successful")
                    except Exception as e:
                        logger.debug(f"requests failed for ZIP: {e}")

                # Try curl as last resort
                if not success:
                    try:
                        curl_cmd = f'curl -L -o "{zip_path}" "{source["zip_url"]}"'
                        result = subprocess.run(curl_cmd, shell=True, capture_output=True, text=True, timeout=120)
                        if result.returncode == 0:
                            success = True
                            logger.debug("✅ curl ZIP download successful")
                    except Exception as e:
                        logger.debug(f"curl failed for ZIP: {e}")

                if not success:
                    raise Exception("All ZIP download methods failed")

                # Verify files exist and have content
                if not os.path.exists(apk_path) or os.path.getsize(apk_path) < 1000:
                    raise Exception("APK file invalid or too small")
                if not os.path.exists(zip_path) or os.path.getsize(zip_path) < 1000:
                    raise Exception("ZIP file invalid or too small")

                logger.debug(f"✅ Files verified - APK: {os.path.getsize(apk_path)} bytes, ZIP: {os.path.getsize(zip_path)} bytes")

                # Push files to device
                logger.debug("📁 Pushing files to device...")

                # Push APK
                push_apk_cmd = f'adb -s {device_id} push "{apk_path}" /sdcard/Magisk.apk'
                result = subprocess.run(push_apk_cmd, shell=True, capture_output=True, text=True, timeout=60)
                if result.returncode != 0:
                    raise Exception(f"Failed to push APK: {result.stderr}")

                # Push ZIP
                push_zip_cmd = f'adb -s {device_id} push "{zip_path}" /sdcard/Magisk.zip'
                result = subprocess.run(push_zip_cmd, shell=True, capture_output=True, text=True, timeout=60)
                if result.returncode != 0:
                    raise Exception(f"Failed to push ZIP: {result.stderr}")

                logger.info(f"✅ Method 1 successful: {source['version']} downloaded and pushed")
                return True

            finally:
                # Clean up temporary files
                try:
                    os.unlink(apk_path)
                    os.unlink(zip_path)
                except:
                    pass

        except Exception as e:
            logger.debug(f"Method 1 failed for {source['version']}: {e}")
            return False

    def _download_method_device_direct(self, device_id: str, source: dict) -> bool:
        """Method 2: Download directly on device using available tools"""
        try:
            logger.debug(f"📥 Method 2: Device direct download for {source['version']}")

            # Try multiple download tools on device
            download_tools = [
                # Try curl with various options
                f'curl -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'curl --insecure -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'curl -k -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try wget with various options
                f'wget -O /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'wget --no-check-certificate -O /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try busybox wget
                f'busybox wget -O /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try toybox curl/wget
                f'toybox curl -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'toybox wget -O /sdcard/Magisk.apk "{source["apk_url"]}"'
            ]

            # Try downloading APK
            apk_success = False
            for tool_cmd in download_tools:
                try:
                    cmd = f'adb -s {device_id} shell su -c "cd /sdcard && {tool_cmd}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)

                    if result.returncode == 0:
                        # Verify file exists and has content
                        verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/Magisk.apk"'
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)

                        if verify_result.returncode == 0 and "Magisk.apk" in verify_result.stdout:
                            # Check file size
                            size_info = verify_result.stdout.strip()
                            if "1000" in size_info or len(size_info.split()) > 4:  # Basic size check
                                apk_success = True
                                logger.debug(f"✅ APK downloaded successfully with: {tool_cmd.split()[0]}")
                                break
                except Exception as e:
                    logger.debug(f"Tool failed: {tool_cmd.split()[0]} - {e}")
                    continue

            if not apk_success:
                raise Exception("All APK download tools failed on device")

            # Try downloading ZIP with same tools
            zip_tools = [cmd.replace('Magisk.apk', 'Magisk.zip').replace(source["apk_url"], source["zip_url"]) for cmd in download_tools]

            zip_success = False
            for tool_cmd in zip_tools:
                try:
                    cmd = f'adb -s {device_id} shell su -c "cd /sdcard && {tool_cmd}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)

                    if result.returncode == 0:
                        # Verify file exists and has content
                        verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/Magisk.zip"'
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)

                        if verify_result.returncode == 0 and "Magisk.zip" in verify_result.stdout:
                            # Check file size
                            size_info = verify_result.stdout.strip()
                            if "1000" in size_info or len(size_info.split()) > 4:  # Basic size check
                                zip_success = True
                                logger.debug(f"✅ ZIP downloaded successfully with: {tool_cmd.split()[0]}")
                                break
                except Exception as e:
                    logger.debug(f"Tool failed: {tool_cmd.split()[0]} - {e}")
                    continue

            if not zip_success:
                raise Exception("All ZIP download tools failed on device")

            logger.info(f"✅ Method 2 successful: {source['version']} downloaded on device")
            return True

        except Exception as e:
            logger.debug(f"Method 2 failed for {source['version']}: {e}")
            return False

    def _download_method_alternative(self, device_id: str, source: dict) -> bool:
        """Method 3: Alternative download methods"""
        try:
            logger.debug(f"📥 Method 3: Alternative download for {source['version']}")

            # Method 3a: Try using Android's built-in download manager
            try:
                logger.debug("Trying Android DownloadManager...")

                # Create a simple download script using Android APIs
                download_script = f'''
am start -a android.intent.action.VIEW -d "{source["apk_url"]}"
sleep 5
am start -a android.intent.action.VIEW -d "{source["zip_url"]}"
'''

                script_cmd = f'adb -s {device_id} shell su -c \'{download_script}\''
                result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=30)

                # Wait a bit for downloads
                time.sleep(10)

                # Check if files appeared in Downloads
                check_cmd = f'adb -s {device_id} shell "find /sdcard -name \'*agisk*\' -type f"'
                check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if check_result.returncode == 0 and check_result.stdout.strip():
                    logger.debug("✅ Files found via DownloadManager")
                    # Move files to expected location
                    files = check_result.stdout.strip().split('\n')
                    for file_path in files:
                        if '.apk' in file_path:
                            move_cmd = f'adb -s {device_id} shell su -c "cp \'{file_path}\' /sdcard/Magisk.apk"'
                            subprocess.run(move_cmd, shell=True, capture_output=True, text=True, timeout=10)
                        elif '.zip' in file_path:
                            move_cmd = f'adb -s {device_id} shell su -c "cp \'{file_path}\' /sdcard/Magisk.zip"'
                            subprocess.run(move_cmd, shell=True, capture_output=True, text=True, timeout=10)

                    logger.info(f"✅ Method 3 successful: {source['version']} via DownloadManager")
                    return True

            except Exception as e:
                logger.debug(f"DownloadManager method failed: {e}")

            # Method 3b: Try using netcat/nc if available
            try:
                logger.debug("Trying netcat method...")

                # This is a more advanced method that would require setting up a local server
                # For now, we'll skip this but it's a placeholder for future enhancement

            except Exception as e:
                logger.debug(f"Netcat method failed: {e}")

            return False

        except Exception as e:
            logger.debug(f"Method 3 failed for {source['version']}: {e}")
            return False

    def _install_magisk_via_manager(self, device_id: str) -> bool:
        """Alternative Magisk installation via Magisk Manager app (manual)"""
        try:
            logger.info("🔧 Attempting Magisk installation via Manager app...")

            # Launch Magisk Manager
            launch_cmd = f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity'
            result = subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                logger.error("❌ Failed to launch Magisk Manager")
                return False

            logger.info("📱 Magisk Manager launched")
            logger.info("💡 Please manually tap 'Install' -> 'Direct Install (Recommended)' in Magisk Manager")
            logger.info("⏳ Waiting 60 seconds for manual installation...")

            # Wait for user to complete installation
            time.sleep(60)

            # Check if Magisk is now installed
            check_cmd = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Magisk installation detected")
                return True
            else:
                logger.warning("⚠️ Magisk installation not detected")
                return False

        except Exception as e:
            logger.error(f"Error in Magisk Manager installation: {e}")
            return False

    def _install_magisk_via_manager_automated(self, device_id: str) -> bool:
        """Automated Magisk installation via Magisk Manager using UI automation"""
        try:
            logger.info("🤖 Attempting automated Magisk Manager installation...")

            # Launch Magisk Manager
            launch_cmd = f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity'
            result = subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                logger.warning("⚠️ Failed to launch Magisk Manager for automated installation")
                return False

            logger.info("📱 Magisk Manager launched for automated installation")
            time.sleep(5)  # Wait for app to load

            # Try to automate the installation using UI commands
            automation_commands = [
                # Tap on Install button (approximate coordinates)
                f'adb -s {device_id} shell input tap 540 400',  # Install button
                f'adb -s {device_id} shell input tap 540 500',  # Direct Install option
                f'adb -s {device_id} shell input tap 540 600',  # Confirm button
            ]

            for cmd in automation_commands:
                try:
                    subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    time.sleep(2)  # Wait between taps
                except:
                    pass

            logger.info("⏳ Waiting for automated installation to complete...")
            time.sleep(30)  # Wait for installation

            # Check if installation succeeded
            check_cmd = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Automated Magisk installation successful")
                return True
            else:
                logger.warning("⚠️ Automated installation may not have completed")
                return False

        except Exception as e:
            logger.error(f"Error in automated Magisk Manager installation: {e}")
            return False

    def _create_magisk_installation_guide(self, device_id: str) -> str:
        """Create step-by-step Magisk installation guide"""
        guide = f"""
🔧 AUTOMATIC MAGISK INSTALLATION GUIDE
=====================================

Device ADB ID: {device_id}

OPTION 1: Automatic Installation (Recommended)
---------------------------------------------
The system will attempt automatic installation:
1. Download Magisk APK and ZIP files
2. Install Magisk Manager APK
3. Push Magisk ZIP to device
4. Execute direct installation script
5. Reboot device to activate

OPTION 2: Manual Installation (Fallback)
----------------------------------------
If automatic installation fails:

1. Download Magisk files manually:
   - Magisk APK: https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk
   - Magisk ZIP: https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.zip

2. Install Magisk Manager:
   adb -s {device_id} install Magisk-v26.4.apk

3. Push ZIP to device:
   adb -s {device_id} push Magisk-v26.4.zip /sdcard/

4. Launch Magisk Manager:
   adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity

5. In Magisk Manager app:
   - Tap "Install" next to Magisk
   - Select "Direct Install (Recommended)"
   - Wait for installation to complete
   - Reboot when prompted

6. Verify installation:
   adb -s {device_id} shell su -c "which magisk"

OPTION 3: Pre-Rooted Image (Best)
---------------------------------
Use Genymotion images with Magisk pre-installed:
- Download pre-rooted Android images
- Import into Genymotion
- Skip manual installation entirely

=====================================
"""
        return guide

    def prepare_comprehensive_magisk_guide(self, instance_name: str) -> str:
        """Comprehensive Magisk anti-detection setup guide"""
        device_id = self.get_device_adb_id(instance_name) or "DEVICE_ID"
        guide = f"""
🛡️ COMPREHENSIVE MAGISK ANTI-DETECTION SETUP GUIDE
==================================================

Device: {instance_name}
ADB ID: {device_id}

📋 REQUIRED COMPONENTS:
1. ✅ MagiskHide Props Config - Modify build.prop properties systemlessly
2. ✅ Universal SafetyNet Fix - Hide root detection and pass SafetyNet
3. ✅ Shamiko (Zygisk-based) - Advanced hiding for newer detection methods
4. ✅ Custom Fire 7 Anti-Detection Module - Our comprehensive spoofing module
5. ✅ Frida Server - Runtime hooking and dynamic instrumentation
6. ✅ Frida Bypass Scripts - Runtime property/file/sensor spoofing

🔧 INSTALLATION STEPS:

STEP 1: Install Magisk
----------------------
1. Download latest Magisk APK: https://github.com/topjohnwu/Magisk/releases
2. Install Magisk Manager:
   adb -s {device_id} install Magisk-v26.4.apk

3. Push Magisk ZIP to device:
   adb -s {device_id} push Magisk-v26.4.zip /sdcard/

4. Install via Magisk Manager -> Install -> Direct Install
5. Reboot device:
   adb -s {device_id} reboot

STEP 2: Install Required Modules
--------------------------------
1. MagiskHide Props Config:
   - Download: https://github.com/Magisk-Modules-Repo/MagiskHidePropsConf
   - Install via Magisk Manager -> Modules -> Install from storage

2. Universal SafetyNet Fix:
   - Download from Magisk repo
   - Install via Magisk Manager

3. Shamiko (for advanced hiding):
   - Enable Zygisk in Magisk settings first
   - Download Shamiko module
   - Install via Magisk Manager

STEP 3: Configure MagiskHide Props
---------------------------------
1. Open terminal and run: props
2. Choose option 1 (Edit device fingerprint)
3. Select option f (Pick from list) or add custom Amazon Fire 7 fingerprint
4. Enable option 2 (Force BASIC attestation)
5. Set option 3 (Device simulation) to Amazon Fire 7

STEP 4: Install Frida Server
---------------------------
1. Download Frida server for Android:
   - For x86: frida-server-16.1.5-android-x86
   - For ARM: frida-server-16.1.5-android-arm64

2. Install Frida server:
   adb -s {device_id} push frida-server-16.1.5-android-x86 /data/local/tmp/frida-server
   adb -s {device_id} shell su -c "chmod 755 /data/local/tmp/frida-server"
   adb -s {device_id} shell su -c "nohup /data/local/tmp/frida-server > /dev/null 2>&1 &"

3. Install Frida CLI on host:
   pip install frida-tools

STEP 5: Run Our Automation
--------------------------
- Run the automation system again
- It will automatically:
  * Create and install comprehensive anti-detection Magisk module
  * Install and start Frida server
  * Create comprehensive Frida bypass script
  * Verify all anti-detection measures

- The system includes:
  * Complete property spoofing (80+ properties via Magisk)
  * Runtime hooking (Frida-based dynamic spoofing)
  * Genymotion traces removal
  * Emulator file hiding
  * Automation framework hiding
  * CPU info spoofing
  * Network detection bypass
  * Sensor data spoofing

🔍 VERIFICATION COMMANDS:
------------------------
# Check properties are spoofed:
adb -s {device_id} shell getprop ro.product.manufacturer  # Should be "Amazon"
adb -s {device_id} shell getprop ro.product.model         # Should be "Fire 7"
adb -s {device_id} shell getprop ro.build.fingerprint     # Should be Fire 7 fingerprint

# Check Genymotion traces removed:
adb -s {device_id} shell getprop | grep -i geny          # Should return nothing
adb -s {device_id} shell getprop | grep -i motion        # Should return nothing

# Check emulator files hidden:
adb -s {device_id} shell ls /system/bin/qemu*            # Should not exist
adb -s {device_id} shell cat /proc/cpuinfo | head -5     # Should show ARM

# Check Frida server is running:
adb -s {device_id} shell ps | grep frida-server         # Should show running process

🔧 FRIDA USAGE:
--------------
# Use our comprehensive bypass script:
frida -U -n com.target.app -l /data/local/tmp/fire7_bypass.js

# Or attach to running app:
frida -U com.target.app -l /data/local/tmp/fire7_bypass.js

# List running processes:
frida-ps -U

# Interactive mode:
frida -U com.target.app

🚀 RECOMMENDED APPROACH:
-----------------------
Option 1: Pre-Rooted Images (BEST)
- Use Genymotion images with Magisk pre-installed
- All modules pre-configured
- Automation works immediately

Option 2: Custom ROM
- Use custom Android ROMs with built-in anti-detection
- Maximum control over device properties

Option 3: Manual Setup (This Guide)
- Install Magisk on standard Genymotion devices
- Configure all required modules
- Run our automation for final setup

📱 EXPECTED FINAL STATE:
-----------------------
✅ Device appears as: Amazon Fire 7 (9th Gen)
✅ All Genymotion traces removed (Magisk + Frida)
✅ Emulator files hidden/removed
✅ Automation frameworks hidden
✅ CPU info shows ARM processor
✅ Network properties realistic
✅ SafetyNet bypass active
✅ Advanced detection bypass active
✅ Frida server running for runtime hooking
✅ Dynamic property spoofing active
✅ Runtime file/sensor/network spoofing
✅ Multi-layer protection: Static + Dynamic

🔧 TROUBLESHOOTING:
------------------
- If properties don't stick: Check MagiskHide Props Config
- If detection still occurs: Verify Shamiko is active with Zygisk
- If automation detected: Check our module's automation hiding
- If SafetyNet fails: Verify Universal SafetyNet Fix is active

============================================
"""
        return guide

    def verify_amazon_fire7_spoofing(self, instance_name: str) -> dict:
        """Verify Amazon Fire 7 spoofing by checking key properties"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            # Key properties to verify
            key_props = [
                'ro.product.manufacturer',
                'ro.product.model',
                'ro.product.brand',
                'ro.product.device',
                'ro.build.fingerprint',
                'ro.hardware.fingerprint',
                'ro.genymotion.device.version',
                'ro.genyd.caps.baseband'
            ]

            verification_results = {}
            for prop in key_props:
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        verification_results[prop] = value
                except Exception as e:
                    logger.debug(f"Failed to get property {prop}: {e}")

            # Check spoofing success
            spoofing_success = True
            if verification_results.get('ro.product.manufacturer') != 'Amazon':
                spoofing_success = False
            if verification_results.get('ro.product.model') != 'Fire 7':
                spoofing_success = False
            if 'Amazon/mantis/mantis' not in verification_results.get('ro.build.fingerprint', ''):
                spoofing_success = False
            if verification_results.get('ro.hardware.fingerprint') == 'genymotion':
                spoofing_success = False

            logger.info("🔍 Amazon Fire 7 Spoofing Verification:")
            logger.info("=" * 50)
            for prop, value in verification_results.items():
                status = "✅" if value else "❌"
                logger.info(f"  {status} {prop}: {value}")
            logger.info("=" * 50)

            if spoofing_success:
                logger.info("✅ Amazon Fire 7 spoofing verification PASSED")
            else:
                logger.warning("❌ Amazon Fire 7 spoofing verification FAILED")

            verification_results['spoofing_success'] = spoofing_success
            return verification_results

        except Exception as e:
            logger.error(f"Error verifying Amazon Fire 7 spoofing: {e}")
            return {}

    def print_device_properties_diagnostic(self, instance_name: str) -> None:
        """Print device build properties to verify Samsung S24 Ultra spoofing"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return

            logger.info(f"🔍 Device Properties Diagnostic for {instance_name}")
            logger.info("=" * 60)

            # Get all properties and filter device/build related ones
            cmd = f'adb -s {device_id} shell getprop'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                device_props = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if any(keyword in line.lower() for keyword in [
                        'ro.product', 'ro.build', 'ro.system', 'brand', 'manufacturer',
                        'model', 'device', 'fingerprint'
                    ]):
                        device_props.append(line)

                if device_props:
                    logger.info("📱 Device/Build Properties:")
                    for prop in sorted(device_props):
                        logger.info(f"  {prop}")
                else:
                    logger.info("No device/build properties found")
            else:
                logger.error(f"Failed to get properties: {result.stderr}")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error getting device properties diagnostic: {e}")

    def print_gsm_properties_diagnostic(self, instance_name: str) -> None:
        """Print GSM baseband related properties for diagnostic purposes only"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return

            logger.info(f"🔍 GSM Baseband Properties Diagnostic for {instance_name}")
            logger.info("=" * 60)

            # Get all properties and filter GSM/telephony related ones
            cmd = f'adb -s {device_id} shell getprop'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                gsm_props = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if any(keyword in line.lower() for keyword in [
                        'gsm', 'sim', 'operator', 'telephony', 'radio', 'baseband',
                        'mcc', 'mnc', 'imei', 'phone', 'network', 'carrier'
                    ]):
                        gsm_props.append(line)

                if gsm_props:
                    logger.info("📱 GSM/Telephony Related Properties:")
                    for prop in sorted(gsm_props):
                        logger.info(f"  {prop}")
                else:
                    logger.info("No GSM/telephony properties found")
            else:
                logger.error(f"Failed to get properties: {result.stderr}")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error getting GSM properties diagnostic: {e}")

    def cleanup_resources(self):
        """Cleanup all resources including persistent genyshell session"""
        logger.info("🔧 Cleaning up Genymotion Manager resources...")
        self._cleanup_genyshell_session()
        logger.info("✅ Genymotion Manager resources cleaned up")

    def get_session_stats(self) -> dict:
        """Get statistics about the persistent genyshell session"""
        with self._genyshell_lock:
            if self._genyshell_process and self._genyshell_process.poll() is None:
                uptime = time.time() - self._genyshell_last_used
                return {
                    "session_active": True,
                    "session_uptime": uptime,
                    "last_used": self._genyshell_last_used,
                    "process_id": self._genyshell_process.pid
                }
            else:
                return {
                    "session_active": False,
                    "session_uptime": 0,
                    "last_used": 0,
                    "process_id": None
                }

    def generate_random_phone_number(self) -> str:
        """Generate a random US phone number"""
        # Generate random US phone number (format: ******-XXX-XXXX)
        area_code = "555"  # Use 555 for testing (reserved for fictional use)
        exchange = random.randint(100, 999)
        number = random.randint(1000, 9999)
        return f"1 {area_code}-{exchange}-{number}"


# Backward compatibility alias
BlueStacksManager = GenymotionManager
