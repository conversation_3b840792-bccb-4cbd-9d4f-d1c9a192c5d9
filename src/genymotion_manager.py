import subprocess
import time
import os
import json
import random
from typing import Dict, Optional, List
import yaml
from loguru import logger


class GenymotionManager:
    def __init__(self, config_path: str = "config/device_profiles.yaml"):
        self.config_path = config_path
        self.device_profiles = self._load_device_profiles()
        self.current_profile = None
        self.actual_device_id = None
        self.genymotion_path = os.getenv('GENYMOTION_PATH', '/Applications/Genymotion.app/Contents/MacOS')
        self.gmtool_path = os.path.join(self.genymotion_path, 'gmtool')
        self.genyshell_path = os.getenv('GENYMOTION_SHELL_PATH', '/Applications/Genymotion Shell.app/Contents/MacOS/genyshell')
        self.running_instances = {}

    def _load_device_profiles(self) -> Dict:
        try:
            # Try relative to current directory first
            if os.path.exists(self.config_path):
                config_file = self.config_path
            else:
                # Try relative to project root (one level up from src)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = os.path.join(project_root, self.config_path)

            with open(config_file, 'r') as f:
                logger.info(f"Loaded device profiles from: {config_file}")
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Device profiles config not found: {self.config_path}")
            logger.error(f"Also tried: {config_file if 'config_file' in locals() else 'N/A'}")
            return {}

    def get_random_device_profile(self) -> Dict:
        profiles = self.device_profiles.get('device_profiles', {})
        if not profiles:
            raise ValueError("No device profiles configured")

        profile_name = random.choice(list(profiles.keys()))
        profile = profiles[profile_name].copy()

        # Randomize some properties for better anti-detection
        profile['android_id'] = self._generate_android_id()
        profile['imei'] = self._generate_imei()
        profile['advertising_id'] = self._generate_advertising_id()

        # Use actual device ID if available, otherwise use default
        if self.actual_device_id:
            profile['udid'] = self.actual_device_id
            logger.info(f"Using actual device ID: {self.actual_device_id}")
        else:
            logger.warning(f"Using default device ID: {profile.get('udid', 'emulator-5554')}")

        logger.info(f"Selected device profile: {profile_name}")
        return profile

    def _generate_android_id(self) -> str:
        return ''.join(random.choices('0123456789abcdef', k=16))

    def _generate_imei(self) -> str:
        # Generate a valid IMEI (15 digits)
        base = ''.join(random.choices('0123456789', k=14))
        check_digit = self._calculate_luhn_checksum(base)
        return base + str(check_digit)

    def _calculate_luhn_checksum(self, number: str) -> int:
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10
        return (10 - luhn_checksum(int(number))) % 10

    def _generate_advertising_id(self) -> str:
        return f"{random.randint(10000000, 99999999):08x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(100000000000, 999999999999):012x}"

    def configure_genymotion_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        if profile is None:
            profile = self.get_random_device_profile()

        self.current_profile = profile

        try:
            logger.info(f"Configuring Genymotion instance '{instance_name}' with profile: {profile['deviceName']}")

            # Store the profile for later use
            self.current_profile = profile

            # Check if instance exists, create if not
            if not self._instance_exists(instance_name):
                logger.info(f"Instance '{instance_name}' does not exist, creating...")
                if not self.create_new_instance(instance_name, profile.get('platformVersion', '11')):
                    logger.warning(f"Failed to create instance '{instance_name}', continuing anyway")

            # Configure instance properties using gmtool
            success = self._configure_instance_properties(instance_name, profile)
            if success:
                logger.info(f"Genymotion instance '{instance_name}' configured successfully")
            else:
                logger.warning(f"Some configuration options failed for '{instance_name}'")

            return True

        except Exception as e:
            logger.error(f"Failed to configure Genymotion instance: {e}")
            return False

    def _instance_exists(self, instance_name: str) -> bool:
        """Check if a Genymotion instance exists"""
        try:
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                return instance_name in result.stdout
            return False
        except Exception as e:
            logger.debug(f"Failed to check if instance exists: {e}")
            return False

    def _configure_instance_properties(self, instance_name: str, profile: Dict) -> bool:
        """Configure Genymotion instance properties"""
        try:
            # Build edit command with device properties
            edit_cmd = ['admin', 'edit', instance_name]

            # Set screen resolution if available
            if 'screen_resolution' in profile:
                width, height = profile['screen_resolution'].split('x')
                edit_cmd.extend(['--width', width, '--height', height])

            # Set DPI if available
            if 'dpi' in profile:
                edit_cmd.extend(['--density', str(profile['dpi'])])

            # Set system properties for device spoofing
            if 'deviceManufacturer' in profile:
                edit_cmd.extend(['--sysprop', f"MANUFACTURER:{profile['deviceManufacturer']}"])
            if 'deviceModel' in profile:
                edit_cmd.extend(['--sysprop', f"MODEL:{profile['deviceModel']}"])
            if 'deviceName' in profile:
                edit_cmd.extend(['--sysprop', f"DEVICE:{profile['deviceName']}"])

            # Execute the edit command
            result = self._execute_gmtool(edit_cmd)
            return result and result.returncode == 0

        except Exception as e:
            logger.error(f"Failed to configure instance properties: {e}")
            return False

    def _restart_adb_server(self) -> bool:
        """Restart ADB server to fix connection issues"""
        try:
            logger.info("Restarting ADB server...")
            subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=5)
            time.sleep(2)
            subprocess.run(['adb', 'start-server'], capture_output=True, timeout=10)
            time.sleep(3)

            # Check if any device is now online
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"ADB devices after restart: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"ADB restart failed: {e}")
            return False

    def _reconnect_device(self, device_id: str) -> bool:
        """Try to reconnect specific device"""
        try:
            logger.info(f"Attempting to reconnect device: {device_id}")
            # Extract port from device ID if it's emulator format
            if "emulator-" in device_id:
                port = device_id.split("-")[1]
                # Try to connect via TCP
                logger.debug(f"Connecting to 127.0.0.1:{port}")
                result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                      capture_output=True, text=True, timeout=10)
                logger.debug(f"ADB connect result: {result.stdout} {result.stderr}")
                time.sleep(3)

                # Check device status
                check_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                logger.debug(f"Device status after reconnect: {check_result.stdout}")

                # Check if device is now online
                is_online = device_id in check_result.stdout and f"{device_id}\tdevice" in check_result.stdout
                if is_online:
                    logger.info(f"Device {device_id} successfully reconnected")
                    return True
                else:
                    logger.debug(f"Device {device_id} still not online after reconnect attempt")
                    return False
            return False
        except Exception as e:
            logger.debug(f"Device reconnect failed: {e}")
            return False

    def _connect_network_device(self) -> bool:
        """Try to connect via common Genymotion network ports"""
        try:
            logger.info("Attempting to connect via common Genymotion ports...")
            # Genymotion typically uses different port ranges than BlueStacks
            common_ports = ['5555', '5556', '5557', '5558', '5559', '5560', '5561', '5562']
            connected_devices = []

            for port in common_ports:
                try:
                    logger.debug(f"Trying port {port}...")
                    result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                        connected_devices.append(port)
                        logger.debug(f"Successfully connected to port {port}")
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Failed to connect to port {port}: {e}")
                    continue

            if connected_devices:
                logger.info(f"Connected to ports: {connected_devices}")

            # Check if any device is now online
            time.sleep(2)
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"Devices after network connect: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"Network connect failed: {e}")
            return False

    def _execute_gmtool(self, command: List[str]) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command and return result"""
        return self._execute_gmtool_with_timeout(command, timeout=30)

    def _execute_gmtool_with_timeout(self, command: List[str], timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command with custom timeout and return result"""
        try:
            full_command = [self.gmtool_path] + command
            logger.debug(f"Executing gmtool command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Gmtool command executed successfully: {' '.join(command)}")
            else:
                logger.warning(f"Gmtool command failed: {' '.join(command)}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Gmtool command timed out after {timeout}s: {' '.join(command)}")
            return None
        except Exception as e:
            logger.error(f"Error executing gmtool command: {e}")
            return None

    def _check_genymotion_running(self) -> bool:
        """Check if Genymotion Desktop is running"""
        try:
            # Try to get version as a quick check
            result = self._execute_gmtool(['version'])
            return result and result.returncode == 0
        except Exception:
            return False

    def _parse_hardware_profile(self, stdout: str) -> Optional[str]:
        """Parse hardware profile name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for HTC One first, then Custom Phone as fallback
        for line in lines:
            if "HTC One" in line:
                return "HTC One"
            elif "Custom Phone" in line:
                return "Custom Phone"

        # If not found, try to parse the first valid profile
        for line in lines:
            if line.strip() and not line.startswith('-') and not line.startswith('UUID') and not line.startswith('NAME'):
                # Split by whitespace and try to extract name
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Extract everything until we hit display specs
                    name_parts = []
                    for i in range(1, len(parts)):
                        if parts[i].isdigit() or 'x' in parts[i] or 'dpi' in parts[i]:
                            break
                        name_parts.append(parts[i])
                    if name_parts:
                        return ' '.join(name_parts)

        return None

    def _parse_os_image(self, stdout: str, android_version: str) -> Optional[str]:
        """Parse OS image name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for the specified Android version first
        for line in lines:
            if f"Android {android_version}" in line or f"{android_version}.0" in line:
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Format: "Android 14.0" - need to combine Android + version
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android 14.0"
                    else:
                        return parts[1]

        # Fallback to any Android version
        for line in lines:
            if "Android" in line and line.strip() and not line.startswith('-') and not line.startswith('UUID'):
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android X.Y"
                    else:
                        return parts[1]

        return None

    def _provide_creation_troubleshooting(self, error_message: str):
        """Provide troubleshooting tips based on error message"""
        if "Unable to find the hwprofile" in error_message:
            logger.info("💡 Hardware profile not found. Try:")
            logger.info("   1. Check available profiles: gmtool admin hwprofiles")
            logger.info("   2. Use exact profile name from the list")
        elif "Unable to find the osimage" in error_message:
            logger.info("💡 OS image not found. Try:")
            logger.info("   1. Check available images: gmtool admin osimages")
            logger.info("   2. Use exact image name from the list")
        elif "Unable to sign in" in error_message:
            logger.info("💡 Authentication issue. Try:")
            logger.info("   1. Open Genymotion Desktop and sign in")
            logger.info("   2. Ensure you have a valid Genymotion account")
        elif "already exists" in error_message:
            logger.info("💡 Instance already exists. Try:")
            logger.info("   1. Use a different instance name")
            logger.info("   2. Delete existing instance: gmtool admin delete <name>")
        else:
            logger.info("💡 General troubleshooting:")
            logger.info("   1. Ensure Genymotion Desktop is running")
            logger.info("   2. Check that you're signed in to Genymotion")
            logger.info("   3. Verify sufficient system resources")

    def _execute_genyshell(self, command: str, device_ip: str = None, timeout: int = 60) -> Optional[subprocess.CompletedProcess]:
        """Execute Genymotion Shell command with improved error handling"""
        try:
            # Try interactive mode first (more reliable for device commands)
            if command.startswith(('android', 'phone')):
                logger.debug(f"Using interactive mode for command: {command}")
                return self._execute_genyshell_interactive(command, timeout=timeout)

            # Use direct command mode for other commands
            if device_ip:
                full_command = [self.genyshell_path, '-r', device_ip, '-c', command]
            else:
                full_command = [self.genyshell_path, '-c', command]

            logger.debug(f"Executing genyshell command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Genyshell command executed successfully: {command}")
            else:
                logger.warning(f"Genyshell command failed: {command}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Genyshell command timed out: {command}")
            return None
        except Exception as e:
            logger.error(f"Error executing genyshell command: {e}")
            return None

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Enable GPS first
            gps_enable_result = self._execute_genyshell("gps setstatus enabled", device_ip)
            if not gps_enable_result or gps_enable_result.returncode != 0:
                logger.error("Failed to enable GPS")
                return False

            # Set latitude
            lat_result = self._execute_genyshell(f"gps setlatitude {latitude}", device_ip)
            if not lat_result or lat_result.returncode != 0:
                logger.error("Failed to set GPS latitude")
                return False

            # Set longitude
            lng_result = self._execute_genyshell(f"gps setlongitude {longitude}", device_ip)
            if not lng_result or lng_result.returncode != 0:
                logger.error("Failed to set GPS longitude")
                return False

            logger.info(f"GPS location set to {latitude}, {longitude}")
            return True

        except Exception as e:
            logger.error(f"Failed to set GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using Genymotion Shell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            # Set battery to manual mode first
            mode_result = self._execute_genyshell("battery setmode manual", device_ip)
            if not mode_result or mode_result.returncode != 0:
                logger.error("Failed to set battery to manual mode")
                return False

            # Set battery level
            level_result = self._execute_genyshell(f"battery setlevel {level}", device_ip)
            if not level_result or level_result.returncode != 0:
                logger.error("Failed to set battery level")
                return False

            logger.info(f"Battery level set to {level}%")
            return True

        except Exception as e:
            logger.error(f"Failed to set battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using Genymotion Shell"""
        try:
            # Map orientation names to angles
            orientation_map = {
                "portrait": 0,
                "landscape": 90,
                "portrait_reverse": 180,
                "landscape_reverse": 270,
                "0": 0,
                "90": 90,
                "180": 180,
                "270": 270
            }

            angle = orientation_map.get(orientation.lower())
            if angle is None:
                logger.error(f"Invalid orientation: {orientation}. Use: portrait, landscape, portrait_reverse, landscape_reverse")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"rotation setangle {angle}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to rotate device")
                return False

            logger.info(f"Device rotated to {orientation} ({angle}°)")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"phone call {phone_number}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to simulate phone call")
                return False

            logger.info(f"Phone call simulated from {phone_number}")
            return True

        except Exception as e:
            logger.error(f"Failed to simulate phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Escape the message for shell command
            escaped_message = message.replace('"', '\\"').replace("'", "\\'")

            result = self._execute_genyshell(f'phone sms {phone_number} "{escaped_message}"', device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to send SMS")
                return False

            logger.info(f"SMS sent from {phone_number}: {message}")
            return True

        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False

    def _get_device_ip(self, device_name: str) -> Optional[str]:
        """Get device IP address for Genymotion Shell connection"""
        try:
            instances = self.get_available_instances()
            if device_name in instances:
                instance_info = instances[device_name]
                return instance_info.get('ip_address')
            return None
        except Exception as e:
            logger.error(f"Failed to get device IP: {e}")
            return None

    def _execute_genyshell_interactive(self, command: str, device_name: str = None, timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute genyshell command using interactive mode (for device selection)"""
        try:
            # Build the command to execute in genyshell
            if device_name:
                full_command = f"devices select {device_name}\n{command}\nexit"
            else:
                full_command = f"{command}\nexit"

            logger.debug(f"Executing genyshell interactive command: {command}")

            # Execute genyshell with the command
            process = subprocess.Popen(
                [self.genyshell_path],
                stdin=subprocess.PIPE,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            stdout, stderr = process.communicate(input=full_command, timeout=timeout)

            result = subprocess.CompletedProcess(
                args=[self.genyshell_path],
                returncode=process.returncode,
                stdout=stdout,
                stderr=stderr
            )

            if result.returncode == 0:
                logger.debug(f"Genyshell interactive command executed successfully: {command}")
            else:
                logger.warning(f"Genyshell interactive command failed: {command}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Genyshell interactive command timed out: {command}")
            return None
        except Exception as e:
            logger.error(f"Error executing genyshell interactive command: {e}")
            return None

    def start_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Starting Genymotion instance '{instance_name}'")

            # Check if instance exists
            if not self._instance_exists(instance_name):
                logger.warning(f"Instance '{instance_name}' does not exist")
                return False

            # Start the instance using gmtool (device startup can take 2-3 minutes)
            result = self._execute_gmtool_with_timeout(['admin', 'start', instance_name], timeout=240)
            if not result or result.returncode != 0:
                logger.error(f"Failed to start instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            logger.info(f"Instance '{instance_name}' started successfully")

            # Wait for device to appear in ADB (Android 14.0 can take longer to boot)
            max_wait = 120  # seconds
            wait_interval = 2
            for i in range(0, max_wait, wait_interval):
                time.sleep(wait_interval)
                try:
                    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                    device_lines = [line.strip() for line in result.stdout.split('\n') if 'emulator-' in line or '127.0.0.1:' in line]
                    if device_lines:
                        for line in device_lines:
                            parts = line.split()
                            if len(parts) >= 2:
                                device_id, device_status = parts[0], parts[1]
                                if device_status == 'device':
                                    logger.info(f"Genymotion instance started successfully. Device: {device_id}")
                                    self.actual_device_id = device_id
                                    return True
                except Exception as e:
                    logger.debug(f"ADB check failed: {e}")

            # If ADB connection failed, try to connect manually
            logger.warning("Device not detected via ADB, attempting manual connection...")
            if self._connect_network_device():
                return True

            logger.warning(f"Could not establish ADB connection to '{instance_name}', but instance may be running")
            return True

        except Exception as e:
            logger.error(f"Error starting Genymotion instance: {e}")
            return False

    def stop_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Stopping Genymotion instance '{instance_name}'")

            # Stop the instance using gmtool
            result = self._execute_gmtool(['admin', 'stop', instance_name])
            if not result or result.returncode != 0:
                logger.error(f"Failed to stop instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            if instance_name in self.running_instances:
                del self.running_instances[instance_name]

            logger.info(f"Genymotion instance '{instance_name}' stopped")
            return True
        except Exception as e:
            logger.error(f"Failed to stop Genymotion instance: {e}")
            return False

    def get_available_instances(self) -> Dict:
        """Get list of available Genymotion instances"""
        try:
            instances = {}

            # Get all instances using gmtool
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    # Skip header, separator lines, and empty lines
                    if (line.strip() and
                        not line.startswith('State') and
                        not line.startswith('----') and
                        not '+-' in line and
                        '|' in line):  # Must be a data line with pipe separators

                        # Parse pipe-separated format: State | ADB Serial | UUID | Name
                        parts = [part.strip() for part in line.split('|')]
                        if len(parts) >= 4:
                            state = parts[0].strip()
                            adb_serial = parts[1].strip()
                            uuid = parts[2].strip()
                            instance_name = parts[3].strip()

                            # Convert state to status
                            status = 'running' if state.lower() == 'on' else 'stopped'

                            instances[instance_name] = {
                                'name': instance_name,
                                'status': status,
                                'device_id': adb_serial if adb_serial != '127.0.0.1:6554' else None,
                                'adb_status': 'online' if status == 'running' else 'offline',
                                'uuid': uuid,
                                'hardware_profile': 'Unknown',
                                'android_version': 'Unknown',
                                'adb_serial': adb_serial if adb_serial else None
                            }

            # Get detailed information for each instance
            for instance_name, instance_info in instances.items():
                # Get detailed device info using gmtool admin details
                try:
                    details_result = self._execute_gmtool(['admin', 'details', instance_name])
                    if details_result and details_result.returncode == 0:
                        details_lines = details_result.stdout.strip().split('\n')

                        for line in details_lines:
                            line = line.strip()
                            if line.startswith('Android Version'):
                                # Extract Android version: "Android Version       : 14.0.0"
                                version = line.split(':', 1)[1].strip()
                                instance_info['android_version'] = f"Android {version}"
                            elif line.startswith('System property') and 'MODEL=' in line:
                                # Extract model: "System property       : MODEL=HTC One"
                                model_part = line.split('MODEL=', 1)[1].strip()
                                logger.debug(f"Found MODEL system property for {instance_name}: {model_part}")
                                instance_info['hardware_profile'] = model_part
                            elif line.startswith('ADB Serial'):
                                # Extract ADB Serial: "ADB Serial            : 127.0.0.1:6554"
                                adb_serial = line.split(':', 1)[1].strip()
                                instance_info['adb_serial'] = adb_serial
                                # Also update device_id if not already set from the main list
                                if not instance_info['device_id']:
                                    instance_info['device_id'] = adb_serial
                            elif line.startswith('Hardware profile:'):
                                # Extract hardware profile name (if available)
                                profile = line.split(':', 1)[1].strip()
                                instance_info['hardware_profile'] = profile
                except Exception as e:
                    logger.debug(f"Failed to get details for {instance_name}: {e}")

                # If we still don't have hardware profile, try to get it from device profiles
                if instance_info['hardware_profile'] == 'Unknown':
                    try:
                        # Try to match with known device profiles
                        if hasattr(self, 'device_profiles') and self.device_profiles:
                            for profile_name, profile_data in self.device_profiles.items():
                                if profile_name.lower() in instance_name.lower():
                                    instance_info['hardware_profile'] = profile_name
                                    break
                    except Exception as e:
                        logger.debug(f"Failed to match device profile for {instance_name}: {e}")

            # Get ADB devices to match with instances
            try:
                adb_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                for line in adb_result.stdout.split('\n')[1:]:  # Skip header
                    if line.strip() and '\t' in line:
                        device_id, status = line.strip().split('\t')
                        # Try to match with running instances
                        for instance_name, instance_info in instances.items():
                            if instance_info['status'] == 'running' and not instance_info['device_id']:
                                instance_info['device_id'] = device_id
                                instance_info['adb_status'] = status
                                break
            except Exception as e:
                logger.debug(f"Failed to get ADB devices: {e}")

            return instances

        except Exception as e:
            logger.error(f"Failed to get available instances: {e}")
            return {}

    def create_new_instance(self, instance_name: str, android_version: str = "11") -> bool:
        """Create a new Genymotion instance programmatically"""
        try:
            logger.info(f"Creating new Genymotion instance: {instance_name}")

            # First check if Genymotion Desktop is running
            if not self._check_genymotion_running():
                logger.error("Genymotion Desktop is not running")
                logger.info("Please start Genymotion Desktop and sign in before creating instances")
                return False

            # Get available hardware profiles and OS images
            logger.info("Getting available hardware profiles...")
            hwprofiles_result = self._execute_gmtool(['admin', 'hwprofiles'])

            logger.info("Getting available OS images...")
            osimages_result = self._execute_gmtool(['admin', 'osimages'])

            if not hwprofiles_result or hwprofiles_result.returncode != 0:
                logger.error("Failed to get hardware profiles")
                if hwprofiles_result and hwprofiles_result.stderr:
                    logger.error(f"Hardware profiles error: {hwprofiles_result.stderr}")
                    if "Unable to sign in" in hwprofiles_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                    elif "command not found" in hwprofiles_result.stderr:
                        logger.error("Genymotion Desktop not installed or gmtool not in PATH")
                return False

            if not osimages_result or osimages_result.returncode != 0:
                logger.error("Failed to get OS images")
                if osimages_result and osimages_result.stderr:
                    logger.error(f"OS images error: {osimages_result.stderr}")
                    if "Unable to sign in" in osimages_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                return False

            # Parse hardware profiles correctly
            hwprofile_name = self._parse_hardware_profile(hwprofiles_result.stdout)
            if not hwprofile_name:
                logger.error("No suitable hardware profile found")
                logger.error("Available profiles:")
                for line in hwprofiles_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using hardware profile: {hwprofile_name}")

            # Parse OS images correctly
            osimage_name = self._parse_os_image(osimages_result.stdout, android_version)
            if not osimage_name:
                logger.error("No suitable OS image found")
                logger.error("Available images:")
                for line in osimages_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using OS image: {osimage_name}")

            # Create the instance with proper syntax and enable root access
            create_cmd = ['admin', 'create', hwprofile_name, osimage_name, instance_name, '--root-access', 'on']
            logger.info(f"Executing: gmtool {' '.join(create_cmd)}")

            # Use longer timeout for device creation (can take up to 3 minutes)
            result = self._execute_gmtool_with_timeout(create_cmd, timeout=240)

            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' created successfully")
                return True
            else:
                logger.error(f"Failed to create instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                    logger.error(f"Return code: {result.returncode}")
                    self._provide_creation_troubleshooting(result.stderr)
                return False

        except Exception as e:
            logger.error(f"Failed to create instance {instance_name}: {e}")
            return False

    def open_genymotion_manager(self) -> bool:
        """Open Genymotion Desktop application"""
        try:
            logger.info("Opening Genymotion Desktop")

            # Check if Genymotion exists
            genymotion_app_path = '/Applications/Genymotion.app'
            if not os.path.exists(genymotion_app_path):
                logger.error(f"Genymotion not found at: {genymotion_app_path}")
                return False

            # Open Genymotion using the 'open' command
            try:
                subprocess.Popen(['open', '-a', 'Genymotion'], start_new_session=True)
                time.sleep(2)
                logger.info("Genymotion Desktop opened successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to open Genymotion: {e}")
                return False

        except Exception as e:
            logger.error(f"Failed to open Genymotion Desktop: {e}")
            return False

    def start_instance_by_name(self, instance_name: str) -> bool:
        """Start a specific Genymotion instance by name"""
        return self.start_instance(instance_name)

    def get_current_profile(self) -> Optional[Dict]:
        return self.current_profile

    def reset_instance(self, instance_name: str = "Pixel") -> bool:
        logger.info(f"Resetting Genymotion instance: {instance_name}")

        try:
            # Stop the instance first
            self.stop_instance(instance_name)
            time.sleep(2)

            # Factory reset the instance
            result = self._execute_gmtool(['admin', 'factoryreset', instance_name])
            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' reset successfully")

                # Reconfigure with new profile
                return self.configure_genymotion_instance(instance_name)
            else:
                logger.error(f"Failed to reset instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Failed to reset Genymotion instance: {e}")
            return False

    # Backward compatibility methods
    def configure_bluestacks_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        """Backward compatibility method"""
        return self.configure_genymotion_instance(instance_name, profile)

    def open_multi_instance_manager(self) -> bool:
        """Backward compatibility method"""
        return self.open_genymotion_manager()

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using genyshell"""
        try:
            command = f"gps setlatitude {latitude}"
            result1 = self._execute_genyshell(command, device_name)

            command = f"gps setlongitude {longitude}"
            result2 = self._execute_genyshell(command, device_name)

            if result1 and result2 and result1.returncode == 0 and result2.returncode == 0:
                logger.info(f"GPS location set to {latitude}, {longitude}")
                return True
            else:
                logger.error("Failed to set GPS location")
                return False

        except Exception as e:
            logger.error(f"Error setting GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using genyshell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            command = f"battery setlevel {level}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Battery level set to {level}%")
                return True
            else:
                logger.error("Failed to set battery level")
                return False

        except Exception as e:
            logger.error(f"Error setting battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using genyshell"""
        try:
            valid_orientations = ['portrait', 'landscape', 'reverse_portrait', 'reverse_landscape']
            if orientation not in valid_orientations:
                logger.error(f"Invalid orientation. Must be one of: {valid_orientations}")
                return False

            command = f"rotation setorientation {orientation}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Device rotated to {orientation}")
                return True
            else:
                logger.error("Failed to rotate device")
                return False

        except Exception as e:
            logger.error(f"Error rotating device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using genyshell"""
        try:
            command = f"phone call {phone_number}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Simulated phone call from {phone_number}")
                return True
            else:
                logger.error("Failed to simulate phone call")
                return False

        except Exception as e:
            logger.error(f"Error simulating phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using genyshell"""
        try:
            command = f"phone sms {phone_number} \"{message}\""
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"SMS sent from {phone_number}: {message}")
                return True
            else:
                logger.error("Failed to send SMS")
                return False

        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            return False

    def get_device_adb_id(self, instance_name: str) -> Optional[str]:
        """Get ADB device ID for a Genymotion instance"""
        try:
            instances = self.get_available_instances()
            if instance_name in instances:
                instance_info = instances[instance_name]
                # Try adb_serial first, then device_id
                device_id = instance_info.get('adb_serial') or instance_info.get('device_id')
                if device_id:
                    logger.debug(f"Found device ID for {instance_name}: {device_id}")
                    return device_id
                else:
                    logger.warning(f"No ADB device ID found for instance: {instance_name}")
                    return None
            else:
                logger.error(f"Instance not found: {instance_name}")
                return None
        except Exception as e:
            logger.error(f"Error getting device ID for {instance_name}: {e}")
            return None

    def customize_device_identifiers(self, instance_name: str, randomize_all: bool = True,
                                   android_id: str = None, device_id: str = None,
                                   phone_number: str = None, operator_name: str = "Android") -> bool:
        """Comprehensive device identifier customization using Genymotion widgets"""
        try:
            logger.info(f"Customizing device identifiers for {instance_name}")
            success = True

            # Randomize or set Android ID (Identifiers widget)
            if randomize_all or android_id:
                android_success = self.set_android_id(instance_name, android_id)
                success &= android_success
                if android_success:
                    logger.info("✅ Android ID customized")
                else:
                    logger.warning("❌ Android ID customization failed")

            # Randomize or set Device ID/IMEI (Identifiers widget)
            if randomize_all or device_id:
                imei_success = self.set_device_id_imei(instance_name, device_id)
                success &= imei_success
                if imei_success:
                    logger.info("✅ Device ID/IMEI customized")
                else:
                    logger.warning("❌ Device ID/IMEI customization failed")

            # Set SIM operator and phone number (Baseband widget)
            if phone_number:
                sim_success = self.set_baseband_sim_operator(instance_name, operator_name, "310260", phone_number)
                success &= sim_success
                if sim_success:
                    logger.info("✅ SIM operator and phone number customized")
                else:
                    logger.warning("❌ SIM operator customization failed")

            # Get and log final identifiers
            try:
                identifiers = self.get_device_identifiers(instance_name)
                if identifiers:
                    logger.info(f"Final device identifiers:")
                    for key, value in identifiers.items():
                        logger.info(f"  {key}: {value}")
            except Exception as e:
                logger.debug(f"Could not retrieve final identifiers: {e}")

            if success:
                logger.info(f"✅ All device identifiers customized successfully for {instance_name}")
            else:
                logger.warning(f"⚠️ Some device identifier customizations failed for {instance_name}")

            return success

        except Exception as e:
            logger.error(f"Error customizing device identifiers: {e}")
            return False

    def customize_android_device_properties(self, instance_name: str, device_name: str = None, phone_number: str = None) -> bool:
        """Customize Android device properties like device name and phone number"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for instance: {instance_name}")
                return False

            success = True

            # Set Android device name if provided
            if device_name:
                success &= self._set_android_device_name(device_id, device_name)
                # Note: Genymotion system properties should be set before device start

            # Use the new comprehensive identifier customization
            if phone_number:
                identifier_success = self.customize_device_identifiers(
                    instance_name,
                    randomize_all=True,  # Randomize Android ID and IMEI
                    phone_number=phone_number
                )
                success &= identifier_success

            # Reboot device to apply persistent properties
            if device_name and success:
                logger.info(f"Rebooting device {instance_name} to apply persistent properties...")
                try:
                    reboot_result = subprocess.run(f'adb -s {device_id} reboot', shell=True, capture_output=True, text=True, timeout=30)
                    if reboot_result.returncode == 0:
                        logger.info(f"Device {instance_name} rebooted successfully")
                        # Wait a moment for reboot to start
                        import time
                        time.sleep(3)
                    else:
                        logger.warning(f"Device reboot may have failed: {reboot_result.stderr}")
                except Exception as e:
                    logger.warning(f"Failed to reboot device: {e}")

            return success

        except Exception as e:
            logger.error(f"Error customizing Android device properties: {e}")
            return False

    def _set_android_device_name(self, device_id: str, device_name: str) -> bool:
        """Set Android device name using ADB"""
        try:
            # Set device name in Android settings and persistent properties
            commands = [
                f'adb -s {device_id} shell settings put global device_name "{device_name}"',
                f'adb -s {device_id} shell settings put secure bluetooth_name "{device_name}"',
                f'adb -s {device_id} shell setprop net.hostname "{device_name}"',
                # Set persistent properties that survive reboot (Genymotion method)
                f'adb -s {device_id} shell setprop persist.ro.product.model "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.device "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.name "{device_name}"'
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode != 0:
                        logger.warning(f"Command failed: {cmd}, Error: {result.stderr}")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Command timed out: {cmd}")
                except Exception as e:
                    logger.warning(f"Command error: {cmd}, Error: {e}")

            logger.info(f"Android device name and persistent properties set to: {device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Android device name: {e}")
            return False

    def _set_genymotion_model_properties(self, instance_name: str, device_name: str) -> bool:
        """Set Genymotion system properties using gmtool admin edit"""
        try:
            # Use gmtool to set system properties that show up in device info
            edit_commands = [
                ['admin', 'edit', instance_name, '--sysprop', f'MODEL:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'PRODUCT:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'DEVICE:{device_name}']
            ]

            for cmd in edit_commands:
                try:
                    result = self._execute_gmtool(cmd)
                    if not result or result.returncode != 0:
                        logger.warning(f"Gmtool command failed: {' '.join(cmd)}")
                        if result and result.stderr:
                            logger.warning(f"Error: {result.stderr}")
                except Exception as e:
                    logger.warning(f"Gmtool command error: {' '.join(cmd)}, Error: {e}")

            logger.info(f"Genymotion system properties set for {instance_name}: MODEL={device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Genymotion system properties: {e}")
            return False



    def set_baseband_sim_operator(self, instance_name: str, operator_name: str = "Android",
                                  mcc_mnc: str = "310260", phone_number: str = None) -> bool:
        """Set SIM operator information using Genymotion Shell only"""
        try:
            logger.info(f"Setting SIM operator for {instance_name} using Genymotion Shell")
            logger.info(f"Operator: {operator_name}, MCC/MNC: {mcc_mnc}")

            # Genymotion Shell baseband commands
            baseband_commands = [
                "phone baseband gsm status",
                "phone baseband gsm voice home",
                "phone baseband gsm data home"
            ]

            success = False
            for command in baseband_commands:
                try:
                    logger.debug(f"Executing baseband command: {command}")
                    result = self._execute_genyshell_interactive(command, timeout=30)
                    if result and result.returncode == 0:
                        success = True
                        logger.debug(f"✅ Baseband command succeeded: {command}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Baseband command failed: {command} - {error_msg}")
                except Exception as e:
                    logger.debug(f"Baseband command error: {command}, Error: {e}")

            # Phone number setting via Genymotion Shell
            phone_success = False
            if phone_number:
                try:
                    logger.debug(f"Setting phone number via SMS simulation: {phone_number}")
                    sms_command = f"phone baseband sms send {phone_number} SIM_CONFIG"
                    result = self._execute_genyshell_interactive(sms_command, timeout=30)
                    if result and result.returncode == 0:
                        phone_success = True
                        logger.debug(f"✅ Phone number SMS simulation succeeded: {phone_number}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Phone number SMS simulation failed: {error_msg}")
                except Exception as e:
                    logger.debug(f"Phone number setting error: {e}")

            if success:
                logger.info(f"✅ SIM operator configured via Genymotion Shell: {operator_name} ({mcc_mnc})")
                if phone_number and phone_success:
                    logger.info(f"✅ Phone number configured: {phone_number}")
                elif phone_number:
                    logger.warning(f"⚠️ Phone number setting failed: {phone_number}")
            else:
                logger.warning(f"❌ SIM operator configuration failed")

            return success

        except Exception as e:
            logger.error(f"Failed to set SIM operator: {e}")
            return False

    def _set_genymotion_phone_number(self, instance_name: str, phone_number: str) -> bool:
        """Set phone number using Genymotion Shell baseband commands"""
        try:
            # Use the comprehensive baseband configuration
            return self.set_baseband_sim_operator(instance_name, phone_number=phone_number)

        except Exception as e:
            logger.error(f"Failed to set Genymotion phone number: {e}")
            return False

    def _set_android_phone_number(self, device_id: str, phone_number: str) -> bool:
        """Set Android phone number using multiple approaches for better compatibility"""
        try:
            success = False

            # Method 1: Set phone number in Android settings database
            settings_commands = [
                f'adb -s {device_id} shell settings put global device_provisioned 1',
                f'adb -s {device_id} shell settings put system phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put secure phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put global phone_number "{phone_number}"'
            ]

            for cmd in settings_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Settings command succeeded: {cmd}")
                    else:
                        logger.debug(f"Settings command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Settings command error: {cmd}, Error: {e}")

            # Method 2: Set telephony system properties
            telephony_commands = [
                f'adb -s {device_id} shell setprop gsm.sim.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop gsm.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop ro.telephony.default_network "9"',
                f'adb -s {device_id} shell setprop telephony.lteOnCdmaDevice "1"'
            ]

            for cmd in telephony_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Telephony command succeeded: {cmd}")
                    else:
                        logger.debug(f"Telephony command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Telephony command error: {cmd}, Error: {e}")

            # Method 3: Try to write to telephony database (with root)
            db_commands = [
                f'adb -s {device_id} shell su -c "mkdir -p /data/misc/radio"',
                f'adb -s {device_id} shell su -c "echo \'{phone_number}\' > /data/misc/radio/phone_number.txt"',
                f'adb -s {device_id} shell su -c "chmod 644 /data/misc/radio/phone_number.txt"'
            ]

            for cmd in db_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Database command succeeded: {cmd}")
                    else:
                        logger.debug(f"Database command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Database command error: {cmd}, Error: {e}")

            # Method 4: Verify phone number was set by checking device settings
            if success:
                # Try to verify the phone number was actually set
                verify_commands = [
                    f'adb -s {device_id} shell settings get system phone_number',
                    f'adb -s {device_id} shell settings get secure phone_number',
                    f'adb -s {device_id} shell settings get global phone_number'
                ]

                for cmd in verify_commands:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                        if result.returncode == 0 and result.stdout.strip() and result.stdout.strip() != 'null':
                            logger.info(f"Phone number verification successful: {result.stdout.strip()}")
                            break
                    except Exception as e:
                        logger.debug(f"Verification command error: {cmd}, Error: {e}")

                logger.info(f"Android phone number set to: {phone_number}")
            else:
                logger.warning(f"All phone number setting methods failed for: {phone_number}")

            return success

        except Exception as e:
            logger.error(f"Failed to set Android phone number: {e}")
            return False



    def set_android_id(self, instance_name: str, android_id: str = None) -> bool:
        """Set Android ID using Genymotion Shell only"""
        try:
            logger.info(f"Setting Android ID for {instance_name} using Genymotion Shell")

            if android_id is None:
                # Generate random Android ID
                command = "android setandroidid random"
                logger.debug("Using random Android ID generation")
            else:
                # Set custom Android ID (must be 16 hex digits)
                if len(android_id) != 16 or not all(c in '0123456789abcdefABCDEF' for c in android_id):
                    logger.error("Android ID must be exactly 16 hexadecimal digits")
                    return False
                command = f"android setandroidid custom {android_id}"
                logger.debug(f"Using custom Android ID: {android_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Android ID set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Android ID via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Android ID: {e}")
            return False



    def set_device_id_imei(self, instance_name: str, device_id: str = None) -> bool:
        """Set Device ID/IMEI using Genymotion Shell only"""
        try:
            logger.info(f"Setting Device ID/IMEI for {instance_name} using Genymotion Shell")

            if device_id is None:
                # Generate random Device ID/IMEI
                command = "android setdeviceid random"
                logger.debug("Using random Device ID/IMEI generation")
            elif device_id.lower() == "none":
                # Remove Device ID
                command = "android setdeviceid none"
                logger.debug("Removing Device ID")
            else:
                # Set custom Device ID (alphanumeric, dots, dashes, underscores)
                command = f"android setdeviceid custom {device_id}"
                logger.debug(f"Using custom Device ID: {device_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Device ID/IMEI set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Device ID/IMEI via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Device ID/IMEI: {e}")
            return False

    def get_device_identifiers(self, instance_name: str) -> dict:
        """Get current Android ID and Device ID/IMEI using Genymotion Shell"""
        try:
            logger.debug(f"Getting device identifiers for {instance_name}")
            identifiers = {}

            # Get Android ID
            try:
                result = self._execute_genyshell_interactive("android getandroidid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Android ID from output
                    for line in result.stdout.split('\n'):
                        if 'Android ID:' in line:
                            identifiers['android_id'] = line.split('Android ID:')[1].strip()
                            logger.debug(f"Found Android ID: {identifiers['android_id']}")
                            break
                else:
                    logger.debug("Failed to get Android ID")
            except Exception as e:
                logger.debug(f"Error getting Android ID: {e}")

            # Get Device ID/IMEI
            try:
                result = self._execute_genyshell_interactive("android getdeviceid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Device ID from output
                    for line in result.stdout.split('\n'):
                        if 'Device ID:' in line:
                            identifiers['device_id'] = line.split('Device ID:')[1].strip()
                            logger.debug(f"Found Device ID: {identifiers['device_id']}")
                            break
                else:
                    logger.debug("Failed to get Device ID")
            except Exception as e:
                logger.debug(f"Error getting Device ID: {e}")

            return identifiers

        except Exception as e:
            logger.error(f"Error getting device identifiers: {e}")
            return {}

    def randomize_device_identifiers(self, instance_name: str) -> bool:
        """Randomize both Android ID and Device ID/IMEI (like clicking shuffle buttons)"""
        try:
            logger.info(f"Randomizing device identifiers for {instance_name}")

            # Randomize Android ID
            android_success = self.set_android_id(instance_name)

            # Randomize Device ID/IMEI
            imei_success = self.set_device_id_imei(instance_name)

            if android_success and imei_success:
                logger.info(f"All device identifiers randomized successfully for {instance_name}")
                return True
            else:
                logger.warning(f"Some device identifiers failed to randomize for {instance_name}")
                return False

        except Exception as e:
            logger.error(f"Error randomizing device identifiers: {e}")
            return False

    def generate_random_phone_number(self) -> str:
        """Generate a random US phone number"""
        # Generate random US phone number (format: ******-XXX-XXXX)
        area_code = "555"  # Use 555 for testing (reserved for fictional use)
        exchange = random.randint(100, 999)
        number = random.randint(1000, 9999)
        return f"1 {area_code}-{exchange}-{number}"


# Backward compatibility alias
BlueStacksManager = GenymotionManager
