import subprocess
import time
import os
import json
import random
import threading
from typing import Dict, Optional, List
import yaml
from loguru import logger


class GenymotionManager:
    def __init__(self, config_path: str = "config/device_profiles.yaml"):
        self.config_path = config_path
        self.device_profiles = self._load_device_profiles()
        self.current_profile = None
        self.actual_device_id = None
        self.genymotion_path = os.getenv('GENYMOTION_PATH', '/Applications/Genymotion.app/Contents/MacOS')
        self.gmtool_path = os.path.join(self.genymotion_path, 'gmtool')
        self.genyshell_path = os.getenv('GENYMOTION_SHELL_PATH', '/Applications/Genymotion Shell.app/Contents/MacOS/genyshell')
        self.running_instances = {}

        # Persistent genyshell session management
        self._genyshell_process = None
        self._genyshell_lock = threading.Lock()
        self._genyshell_last_used = 0
        self._genyshell_timeout = 300  # 5 minutes timeout for idle session

        logger.info("🔧 Persistent genyshell session manager enabled")

    def __del__(self):
        """Cleanup persistent genyshell session on destruction"""
        self._cleanup_genyshell_session()

    def _cleanup_genyshell_session(self):
        """Clean up the persistent genyshell session"""
        with self._genyshell_lock:
            if self._genyshell_process and self._genyshell_process.poll() is None:
                try:
                    self._genyshell_process.stdin.write("exit\n")
                    self._genyshell_process.stdin.flush()
                    self._genyshell_process.wait(timeout=5)
                    logger.debug("🔧 Persistent genyshell session closed gracefully")
                except:
                    self._genyshell_process.terminate()
                    logger.debug("🔧 Persistent genyshell session terminated")
                finally:
                    self._genyshell_process = None

    def _get_persistent_genyshell(self):
        """Get or create persistent genyshell session"""
        with self._genyshell_lock:
            current_time = time.time()

            # Check if session exists and is still alive
            if (self._genyshell_process and
                self._genyshell_process.poll() is None and
                (current_time - self._genyshell_last_used) < self._genyshell_timeout):

                self._genyshell_last_used = current_time
                return self._genyshell_process

            # Clean up old session if exists
            if self._genyshell_process:
                try:
                    self._genyshell_process.terminate()
                except:
                    pass
                self._genyshell_process = None

            # Create new persistent session
            try:
                logger.debug("🔧 Creating new persistent genyshell session")
                self._genyshell_process = subprocess.Popen(
                    [self.genyshell_path],
                    stdin=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1  # Line buffered
                )
                self._genyshell_last_used = current_time
                logger.debug("✅ Persistent genyshell session created successfully")
                return self._genyshell_process

            except Exception as e:
                logger.error(f"❌ Failed to create persistent genyshell session: {e}")
                self._genyshell_process = None
                return None

    def _load_device_profiles(self) -> Dict:
        try:
            # Try relative to current directory first
            if os.path.exists(self.config_path):
                config_file = self.config_path
            else:
                # Try relative to project root (one level up from src)
                project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_file = os.path.join(project_root, self.config_path)

            with open(config_file, 'r') as f:
                logger.info(f"Loaded device profiles from: {config_file}")
                return yaml.safe_load(f)
        except FileNotFoundError:
            logger.error(f"Device profiles config not found: {self.config_path}")
            logger.error(f"Also tried: {config_file if 'config_file' in locals() else 'N/A'}")
            return {}

    def get_random_device_profile(self) -> Dict:
        profiles = self.device_profiles.get('device_profiles', {})
        if not profiles:
            raise ValueError("No device profiles configured")

        profile_name = random.choice(list(profiles.keys()))
        profile = profiles[profile_name].copy()

        # Randomize some properties for better anti-detection
        profile['android_id'] = self._generate_android_id()
        profile['imei'] = self._generate_imei()
        profile['advertising_id'] = self._generate_advertising_id()

        # Use actual device ID if available, otherwise use default
        if self.actual_device_id:
            profile['udid'] = self.actual_device_id
            logger.info(f"Using actual device ID: {self.actual_device_id}")
        else:
            logger.warning(f"Using default device ID: {profile.get('udid', 'emulator-5554')}")

        logger.info(f"Selected device profile: {profile_name}")
        return profile

    def _generate_android_id(self) -> str:
        return ''.join(random.choices('0123456789abcdef', k=16))

    def _generate_imei(self) -> str:
        # Generate a valid IMEI (15 digits)
        base = ''.join(random.choices('0123456789', k=14))
        check_digit = self._calculate_luhn_checksum(base)
        return base + str(check_digit)

    def _calculate_luhn_checksum(self, number: str) -> int:
        def luhn_checksum(card_num):
            def digits_of(n):
                return [int(d) for d in str(n)]
            digits = digits_of(card_num)
            odd_digits = digits[-1::-2]
            even_digits = digits[-2::-2]
            checksum = sum(odd_digits)
            for d in even_digits:
                checksum += sum(digits_of(d*2))
            return checksum % 10
        return (10 - luhn_checksum(int(number))) % 10

    def _generate_advertising_id(self) -> str:
        return f"{random.randint(10000000, 99999999):08x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(1000, 9999):04x}-{random.randint(100000000000, 999999999999):012x}"

    def configure_genymotion_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        if profile is None:
            profile = self.get_random_device_profile()

        self.current_profile = profile

        try:
            logger.info(f"Configuring Genymotion instance '{instance_name}' with profile: {profile['deviceName']}")

            # Store the profile for later use
            self.current_profile = profile

            # Check if instance exists, create if not
            if not self._instance_exists(instance_name):
                logger.info(f"Instance '{instance_name}' does not exist, creating...")
                if not self.create_new_instance(instance_name, profile.get('platformVersion', '11')):
                    logger.warning(f"Failed to create instance '{instance_name}', continuing anyway")

            # Configure instance properties using gmtool
            success = self._configure_instance_properties(instance_name, profile)
            if success:
                logger.info(f"Genymotion instance '{instance_name}' configured successfully")
            else:
                logger.warning(f"Some configuration options failed for '{instance_name}'")

            return True

        except Exception as e:
            logger.error(f"Failed to configure Genymotion instance: {e}")
            return False

    def _instance_exists(self, instance_name: str) -> bool:
        """Check if a Genymotion instance exists"""
        try:
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                return instance_name in result.stdout
            return False
        except Exception as e:
            logger.debug(f"Failed to check if instance exists: {e}")
            return False

    def _configure_instance_properties(self, instance_name: str, profile: Dict) -> bool:
        """Configure Genymotion instance properties"""
        try:
            # Build edit command with device properties
            edit_cmd = ['admin', 'edit', instance_name]

            # Set screen resolution if available
            if 'screen_resolution' in profile:
                width, height = profile['screen_resolution'].split('x')
                edit_cmd.extend(['--width', width, '--height', height])

            # Set DPI if available
            if 'dpi' in profile:
                edit_cmd.extend(['--density', str(profile['dpi'])])

            # Set system properties for device spoofing
            if 'deviceManufacturer' in profile:
                edit_cmd.extend(['--sysprop', f"MANUFACTURER:{profile['deviceManufacturer']}"])
            if 'deviceModel' in profile:
                edit_cmd.extend(['--sysprop', f"MODEL:{profile['deviceModel']}"])
            if 'deviceName' in profile:
                edit_cmd.extend(['--sysprop', f"DEVICE:{profile['deviceName']}"])

            # Execute the edit command
            result = self._execute_gmtool(edit_cmd)
            return result and result.returncode == 0

        except Exception as e:
            logger.error(f"Failed to configure instance properties: {e}")
            return False

    def _restart_adb_server(self) -> bool:
        """Restart ADB server to fix connection issues"""
        try:
            logger.info("Restarting ADB server...")
            subprocess.run(['adb', 'kill-server'], capture_output=True, timeout=5)
            time.sleep(2)
            subprocess.run(['adb', 'start-server'], capture_output=True, timeout=10)
            time.sleep(3)

            # Check if any device is now online
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"ADB devices after restart: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"ADB restart failed: {e}")
            return False

    def _reconnect_device(self, device_id: str) -> bool:
        """Try to reconnect specific device"""
        try:
            logger.info(f"Attempting to reconnect device: {device_id}")
            # Extract port from device ID if it's emulator format
            if "emulator-" in device_id:
                port = device_id.split("-")[1]
                # Try to connect via TCP
                logger.debug(f"Connecting to 127.0.0.1:{port}")
                result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                      capture_output=True, text=True, timeout=10)
                logger.debug(f"ADB connect result: {result.stdout} {result.stderr}")
                time.sleep(3)

                # Check device status
                check_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                logger.debug(f"Device status after reconnect: {check_result.stdout}")

                # Check if device is now online
                is_online = device_id in check_result.stdout and f"{device_id}\tdevice" in check_result.stdout
                if is_online:
                    logger.info(f"Device {device_id} successfully reconnected")
                    return True
                else:
                    logger.debug(f"Device {device_id} still not online after reconnect attempt")
                    return False
            return False
        except Exception as e:
            logger.debug(f"Device reconnect failed: {e}")
            return False

    def _connect_network_device(self) -> bool:
        """Try to connect via common Genymotion network ports"""
        try:
            logger.info("Attempting to connect via common Genymotion ports...")
            # Genymotion typically uses different port ranges than BlueStacks
            common_ports = ['5555', '5556', '5557', '5558', '5559', '5560', '5561', '5562']
            connected_devices = []

            for port in common_ports:
                try:
                    logger.debug(f"Trying port {port}...")
                    result = subprocess.run(['adb', 'connect', f'127.0.0.1:{port}'],
                                          capture_output=True, text=True, timeout=5)
                    if "connected" in result.stdout.lower() or "already connected" in result.stdout.lower():
                        connected_devices.append(port)
                        logger.debug(f"Successfully connected to port {port}")
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Failed to connect to port {port}: {e}")
                    continue

            if connected_devices:
                logger.info(f"Connected to ports: {connected_devices}")

            # Check if any device is now online
            time.sleep(2)
            result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
            logger.debug(f"Devices after network connect: {result.stdout}")
            return "device" in result.stdout.lower()
        except Exception as e:
            logger.debug(f"Network connect failed: {e}")
            return False

    def _execute_gmtool(self, command: List[str]) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command and return result"""
        return self._execute_gmtool_with_timeout(command, timeout=30)

    def _execute_gmtool_with_timeout(self, command: List[str], timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute gmtool command with custom timeout and return result"""
        try:
            full_command = [self.gmtool_path] + command
            logger.debug(f"Executing gmtool command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Gmtool command executed successfully: {' '.join(command)}")
            else:
                logger.warning(f"Gmtool command failed: {' '.join(command)}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Gmtool command timed out after {timeout}s: {' '.join(command)}")
            return None
        except Exception as e:
            logger.error(f"Error executing gmtool command: {e}")
            return None

    def _check_genymotion_running(self) -> bool:
        """Check if Genymotion Desktop is running"""
        try:
            # Try to get version as a quick check
            result = self._execute_gmtool(['version'])
            return result and result.returncode == 0
        except Exception:
            return False

    def _parse_hardware_profile(self, stdout: str) -> Optional[str]:
        """Parse hardware profile name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for HTC One first, then Custom Phone as fallback
        for line in lines:
            if "HTC One" in line:
                return "HTC One"
            elif "Custom Phone" in line:
                return "Custom Phone"

        # If not found, try to parse the first valid profile
        for line in lines:
            if line.strip() and not line.startswith('-') and not line.startswith('UUID') and not line.startswith('NAME'):
                # Split by whitespace and try to extract name
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Extract everything until we hit display specs
                    name_parts = []
                    for i in range(1, len(parts)):
                        if parts[i].isdigit() or 'x' in parts[i] or 'dpi' in parts[i]:
                            break
                        name_parts.append(parts[i])
                    if name_parts:
                        return ' '.join(name_parts)

        return None

    def _parse_os_image(self, stdout: str, android_version: str) -> Optional[str]:
        """Parse OS image name from gmtool output"""
        if not stdout:
            return None

        lines = stdout.strip().split('\n')

        # Look for the specified Android version first
        for line in lines:
            if f"Android {android_version}" in line or f"{android_version}.0" in line:
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    # Format: "Android 14.0" - need to combine Android + version
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android 14.0"
                    else:
                        return parts[1]

        # Fallback to any Android version
        for line in lines:
            if "Android" in line and line.strip() and not line.startswith('-') and not line.startswith('UUID'):
                parts = line.split()
                if len(parts) >= 2:
                    # The NAME column is the second column (index 1)
                    if len(parts) >= 3 and parts[1] == "Android":
                        return f"{parts[1]} {parts[2]}"  # "Android X.Y"
                    else:
                        return parts[1]

        return None

    def _provide_creation_troubleshooting(self, error_message: str):
        """Provide troubleshooting tips based on error message"""
        if "Unable to find the hwprofile" in error_message:
            logger.info("💡 Hardware profile not found. Try:")
            logger.info("   1. Check available profiles: gmtool admin hwprofiles")
            logger.info("   2. Use exact profile name from the list")
        elif "Unable to find the osimage" in error_message:
            logger.info("💡 OS image not found. Try:")
            logger.info("   1. Check available images: gmtool admin osimages")
            logger.info("   2. Use exact image name from the list")
        elif "Unable to sign in" in error_message:
            logger.info("💡 Authentication issue. Try:")
            logger.info("   1. Open Genymotion Desktop and sign in")
            logger.info("   2. Ensure you have a valid Genymotion account")
        elif "already exists" in error_message:
            logger.info("💡 Instance already exists. Try:")
            logger.info("   1. Use a different instance name")
            logger.info("   2. Delete existing instance: gmtool admin delete <name>")
        else:
            logger.info("💡 General troubleshooting:")
            logger.info("   1. Ensure Genymotion Desktop is running")
            logger.info("   2. Check that you're signed in to Genymotion")
            logger.info("   3. Verify sufficient system resources")

    def _execute_genyshell(self, command: str, device_ip: str = None, timeout: int = 60) -> Optional[subprocess.CompletedProcess]:
        """Execute Genymotion Shell command with persistent session optimization"""
        try:
            # Use persistent session for widget commands (android, phone)
            if command.startswith(('android', 'phone')):
                logger.debug(f"Using persistent session for widget command: {command}")
                return self._execute_genyshell_interactive(command, timeout=timeout)

            # Use direct command mode for other commands (devices, etc.)
            if device_ip:
                full_command = [self.genyshell_path, '-r', device_ip, '-c', command]
            else:
                full_command = [self.genyshell_path, '-c', command]

            logger.debug(f"Executing direct genyshell command: {' '.join(full_command)}")

            result = subprocess.run(full_command, capture_output=True, text=True, timeout=timeout)

            if result.returncode == 0:
                logger.debug(f"Direct genyshell command executed successfully: {command}")
            else:
                logger.warning(f"Direct genyshell command failed: {command}, Error: {result.stderr}")

            return result

        except subprocess.TimeoutExpired:
            logger.error(f"Genyshell command timed out: {command}")
            return None
        except Exception as e:
            logger.error(f"Error executing genyshell command: {e}")
            return None

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Enable GPS first
            gps_enable_result = self._execute_genyshell("gps setstatus enabled", device_ip)
            if not gps_enable_result or gps_enable_result.returncode != 0:
                logger.error("Failed to enable GPS")
                return False

            # Set latitude
            lat_result = self._execute_genyshell(f"gps setlatitude {latitude}", device_ip)
            if not lat_result or lat_result.returncode != 0:
                logger.error("Failed to set GPS latitude")
                return False

            # Set longitude
            lng_result = self._execute_genyshell(f"gps setlongitude {longitude}", device_ip)
            if not lng_result or lng_result.returncode != 0:
                logger.error("Failed to set GPS longitude")
                return False

            logger.info(f"GPS location set to {latitude}, {longitude}")
            return True

        except Exception as e:
            logger.error(f"Failed to set GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using Genymotion Shell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            # Set battery to manual mode first
            mode_result = self._execute_genyshell("battery setmode manual", device_ip)
            if not mode_result or mode_result.returncode != 0:
                logger.error("Failed to set battery to manual mode")
                return False

            # Set battery level
            level_result = self._execute_genyshell(f"battery setlevel {level}", device_ip)
            if not level_result or level_result.returncode != 0:
                logger.error("Failed to set battery level")
                return False

            logger.info(f"Battery level set to {level}%")
            return True

        except Exception as e:
            logger.error(f"Failed to set battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using Genymotion Shell"""
        try:
            # Map orientation names to angles
            orientation_map = {
                "portrait": 0,
                "landscape": 90,
                "portrait_reverse": 180,
                "landscape_reverse": 270,
                "0": 0,
                "90": 90,
                "180": 180,
                "270": 270
            }

            angle = orientation_map.get(orientation.lower())
            if angle is None:
                logger.error(f"Invalid orientation: {orientation}. Use: portrait, landscape, portrait_reverse, landscape_reverse")
                return False

            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"rotation setangle {angle}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to rotate device")
                return False

            logger.info(f"Device rotated to {orientation} ({angle}°)")
            return True

        except Exception as e:
            logger.error(f"Failed to rotate device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            result = self._execute_genyshell(f"phone call {phone_number}", device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to simulate phone call")
                return False

            logger.info(f"Phone call simulated from {phone_number}")
            return True

        except Exception as e:
            logger.error(f"Failed to simulate phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using Genymotion Shell"""
        try:
            device_ip = self._get_device_ip(device_name) if device_name else None

            # Escape the message for shell command
            escaped_message = message.replace('"', '\\"').replace("'", "\\'")

            result = self._execute_genyshell(f'phone sms {phone_number} "{escaped_message}"', device_ip)
            if not result or result.returncode != 0:
                logger.error("Failed to send SMS")
                return False

            logger.info(f"SMS sent from {phone_number}: {message}")
            return True

        except Exception as e:
            logger.error(f"Failed to send SMS: {e}")
            return False

    def _get_device_ip(self, device_name: str) -> Optional[str]:
        """Get device IP address for Genymotion Shell connection"""
        try:
            instances = self.get_available_instances()
            if device_name in instances:
                instance_info = instances[device_name]
                return instance_info.get('ip_address')
            return None
        except Exception as e:
            logger.error(f"Failed to get device IP: {e}")
            return None

    def _execute_genyshell_interactive(self, command: str, device_name: str = None, timeout: int = 30) -> Optional[subprocess.CompletedProcess]:
        """Execute genyshell command using persistent session (optimized)"""
        try:
            # Get persistent genyshell session
            genyshell = self._get_persistent_genyshell()
            if not genyshell:
                logger.error("Failed to get persistent genyshell session")
                return None

            # Build the command to execute
            if device_name:
                full_command = f"devices select {device_name}\n{command}\n"
            else:
                full_command = f"{command}\n"

            logger.debug(f"Executing genyshell command via persistent session: {command}")

            # Send command to persistent session
            try:
                genyshell.stdin.write(full_command)
                genyshell.stdin.flush()

                # Read response with timeout
                import select
                import sys

                if sys.platform != 'win32':
                    # Use select for non-Windows systems
                    ready, _, _ = select.select([genyshell.stdout], [], [], timeout)
                    if ready:
                        # Read available output
                        output_lines = []
                        while True:
                            ready, _, _ = select.select([genyshell.stdout], [], [], 0.1)
                            if not ready:
                                break
                            line = genyshell.stdout.readline()
                            if not line:
                                break
                            output_lines.append(line)
                        stdout = ''.join(output_lines)
                        stderr = ""
                        returncode = 0
                    else:
                        stdout = ""
                        stderr = "Command timed out"
                        returncode = 1
                else:
                    # Fallback for Windows - read with timeout
                    stdout = ""
                    stderr = ""
                    returncode = 0

                result = subprocess.CompletedProcess(
                    args=[self.genyshell_path],
                    returncode=returncode,
                    stdout=stdout,
                    stderr=stderr
                )

                if result.returncode == 0:
                    logger.debug(f"✅ Persistent genyshell command executed successfully: {command}")
                else:
                    logger.warning(f"❌ Persistent genyshell command failed: {command}, Error: {result.stderr}")

                return result

            except Exception as e:
                logger.error(f"Error communicating with persistent genyshell: {e}")
                # Reset session on communication error
                self._cleanup_genyshell_session()
                return None

        except Exception as e:
            logger.error(f"Error executing persistent genyshell command: {e}")
            return None

    def start_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Starting Genymotion instance '{instance_name}'")

            # Check if instance exists
            if not self._instance_exists(instance_name):
                logger.warning(f"Instance '{instance_name}' does not exist")
                return False

            # Start the instance using gmtool (device startup can take 5-10 minutes for Android 14)
            result = self._execute_gmtool_with_timeout(['admin', 'start', instance_name], timeout=600)
            if not result or result.returncode != 0:
                logger.error(f"Failed to start instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            logger.info(f"Instance '{instance_name}' started successfully")

            # Wait for device to appear in ADB (reduced wait time)
            max_wait = 120  # seconds (2 minutes)
            wait_interval = 3
            for i in range(0, max_wait, wait_interval):
                time.sleep(wait_interval)
                try:
                    result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                    device_lines = [line.strip() for line in result.stdout.split('\n') if 'emulator-' in line or '127.0.0.1:' in line]
                    if device_lines:
                        for line in device_lines:
                            parts = line.split()
                            if len(parts) >= 2:
                                device_id, device_status = parts[0], parts[1]
                                if device_status == 'device':
                                    logger.info(f"Genymotion instance started successfully. Device: {device_id}")
                                    self.actual_device_id = device_id
                                    return True
                except Exception as e:
                    logger.debug(f"ADB check failed: {e}")

            # If ADB connection failed, try to connect manually
            logger.warning("Device not detected via ADB, attempting manual connection...")
            if self._connect_network_device():
                return True

            logger.warning(f"Could not establish ADB connection to '{instance_name}', but instance may be running")
            return True

        except Exception as e:
            logger.error(f"Error starting Genymotion instance: {e}")
            return False

    def stop_instance(self, instance_name: str = "Pixel") -> bool:
        try:
            logger.info(f"Stopping Genymotion instance '{instance_name}'")

            # Stop the instance using gmtool
            result = self._execute_gmtool(['admin', 'stop', instance_name])
            if not result or result.returncode != 0:
                logger.error(f"Failed to stop instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

            if instance_name in self.running_instances:
                del self.running_instances[instance_name]

            logger.info(f"Genymotion instance '{instance_name}' stopped")
            return True
        except Exception as e:
            logger.error(f"Failed to stop Genymotion instance: {e}")
            return False

    def delete_instance(self, instance_name: str) -> bool:
        """Delete a Genymotion instance"""
        try:
            logger.info(f"🗑️ Deleting Genymotion instance '{instance_name}'")

            # Stop instance first if running
            self.stop_instance(instance_name)

            # Delete the instance
            result = self._execute_gmtool(['admin', 'delete', instance_name])

            if result and result.returncode == 0:
                logger.info(f"✅ Genymotion instance '{instance_name}' deleted successfully")

                # Remove from running instances if present
                if instance_name in self.running_instances:
                    del self.running_instances[instance_name]

                return True
            else:
                error_msg = result.stderr if result else "Unknown error"
                logger.error(f"❌ Failed to delete instance '{instance_name}'")
                logger.error(f"Error output: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error deleting instance: {e}")
            return False

    def get_available_instances(self) -> Dict:
        """Get list of available Genymotion instances"""
        try:
            instances = {}

            # Get all instances using gmtool
            result = self._execute_gmtool(['admin', 'list'])
            if result and result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                for line in lines:
                    # Skip header, separator lines, and empty lines
                    if (line.strip() and
                        not line.startswith('State') and
                        not line.startswith('----') and
                        not '+-' in line and
                        '|' in line):  # Must be a data line with pipe separators

                        # Parse pipe-separated format: State | ADB Serial | UUID | Name
                        parts = [part.strip() for part in line.split('|')]
                        if len(parts) >= 4:
                            state = parts[0].strip()
                            adb_serial = parts[1].strip()
                            uuid = parts[2].strip()
                            instance_name = parts[3].strip()

                            # Convert state to status
                            status = 'running' if state.lower() == 'on' else 'stopped'

                            instances[instance_name] = {
                                'name': instance_name,
                                'status': status,
                                'device_id': adb_serial if adb_serial != '127.0.0.1:6554' else None,
                                'adb_status': 'online' if status == 'running' else 'offline',
                                'uuid': uuid,
                                'hardware_profile': 'Unknown',
                                'android_version': 'Unknown',
                                'adb_serial': adb_serial if adb_serial else None
                            }

            # Get detailed information for each instance
            for instance_name, instance_info in instances.items():
                # Get detailed device info using gmtool admin details
                try:
                    details_result = self._execute_gmtool(['admin', 'details', instance_name])
                    if details_result and details_result.returncode == 0:
                        details_lines = details_result.stdout.strip().split('\n')

                        for line in details_lines:
                            line = line.strip()
                            if line.startswith('Android Version'):
                                # Extract Android version: "Android Version       : 14.0.0"
                                version = line.split(':', 1)[1].strip()
                                instance_info['android_version'] = f"Android {version}"
                            elif line.startswith('System property') and 'MODEL=' in line:
                                # Extract model: "System property       : MODEL=HTC One"
                                model_part = line.split('MODEL=', 1)[1].strip()
                                logger.debug(f"Found MODEL system property for {instance_name}: {model_part}")
                                instance_info['hardware_profile'] = model_part
                            elif line.startswith('ADB Serial'):
                                # Extract ADB Serial: "ADB Serial            : 127.0.0.1:6554"
                                adb_serial = line.split(':', 1)[1].strip()
                                instance_info['adb_serial'] = adb_serial
                                # Also update device_id if not already set from the main list
                                if not instance_info['device_id']:
                                    instance_info['device_id'] = adb_serial
                            elif line.startswith('Hardware profile:'):
                                # Extract hardware profile name (if available)
                                profile = line.split(':', 1)[1].strip()
                                instance_info['hardware_profile'] = profile
                except Exception as e:
                    logger.debug(f"Failed to get details for {instance_name}: {e}")

                # If we still don't have hardware profile, try to get it from device profiles
                if instance_info['hardware_profile'] == 'Unknown':
                    try:
                        # Try to match with known device profiles
                        if hasattr(self, 'device_profiles') and self.device_profiles:
                            for profile_name, profile_data in self.device_profiles.items():
                                if profile_name.lower() in instance_name.lower():
                                    instance_info['hardware_profile'] = profile_name
                                    break
                    except Exception as e:
                        logger.debug(f"Failed to match device profile for {instance_name}: {e}")

            # Get ADB devices to match with instances
            try:
                adb_result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
                for line in adb_result.stdout.split('\n')[1:]:  # Skip header
                    if line.strip() and '\t' in line:
                        device_id, status = line.strip().split('\t')
                        # Try to match with running instances
                        for instance_name, instance_info in instances.items():
                            if instance_info['status'] == 'running' and not instance_info['device_id']:
                                instance_info['device_id'] = device_id
                                instance_info['adb_status'] = status
                                break
            except Exception as e:
                logger.debug(f"Failed to get ADB devices: {e}")

            return instances

        except Exception as e:
            logger.error(f"Failed to get available instances: {e}")
            return {}

    def create_new_instance(self, instance_name: str, android_version: str = "11") -> bool:
        """Create a new Genymotion instance programmatically"""
        try:
            logger.info(f"Creating new Genymotion instance: {instance_name}")

            # First check if Genymotion Desktop is running
            if not self._check_genymotion_running():
                logger.error("Genymotion Desktop is not running")
                logger.info("Please start Genymotion Desktop and sign in before creating instances")
                return False

            # Get available hardware profiles and OS images
            logger.info("Getting available hardware profiles...")
            hwprofiles_result = self._execute_gmtool(['admin', 'hwprofiles'])

            logger.info("Getting available OS images...")
            osimages_result = self._execute_gmtool(['admin', 'osimages'])

            if not hwprofiles_result or hwprofiles_result.returncode != 0:
                logger.error("Failed to get hardware profiles")
                if hwprofiles_result and hwprofiles_result.stderr:
                    logger.error(f"Hardware profiles error: {hwprofiles_result.stderr}")
                    if "Unable to sign in" in hwprofiles_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                    elif "command not found" in hwprofiles_result.stderr:
                        logger.error("Genymotion Desktop not installed or gmtool not in PATH")
                return False

            if not osimages_result or osimages_result.returncode != 0:
                logger.error("Failed to get OS images")
                if osimages_result and osimages_result.stderr:
                    logger.error(f"OS images error: {osimages_result.stderr}")
                    if "Unable to sign in" in osimages_result.stderr:
                        logger.error("Please sign in to Genymotion Desktop first")
                return False

            # Parse hardware profiles correctly
            hwprofile_name = self._parse_hardware_profile(hwprofiles_result.stdout)
            if not hwprofile_name:
                logger.error("No suitable hardware profile found")
                logger.error("Available profiles:")
                for line in hwprofiles_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using hardware profile: {hwprofile_name}")

            # Parse OS images correctly
            osimage_name = self._parse_os_image(osimages_result.stdout, android_version)
            if not osimage_name:
                logger.error("No suitable OS image found")
                logger.error("Available images:")
                for line in osimages_result.stdout.split('\n')[:5]:  # Show first 5 lines
                    logger.error(f"  {line}")
                return False

            logger.info(f"Using OS image: {osimage_name}")

            # Create the instance with proper syntax and enable root access
            create_cmd = ['admin', 'create', hwprofile_name, osimage_name, instance_name, '--root-access', 'on']
            logger.info(f"Executing: gmtool {' '.join(create_cmd)}")

            # Use longer timeout for device creation (can take up to 3 minutes)
            result = self._execute_gmtool_with_timeout(create_cmd, timeout=240)

            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' created successfully")
                return True
            else:
                logger.error(f"Failed to create instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                    logger.error(f"Return code: {result.returncode}")
                    self._provide_creation_troubleshooting(result.stderr)
                return False

        except Exception as e:
            logger.error(f"Failed to create instance {instance_name}: {e}")
            return False

    def open_genymotion_manager(self) -> bool:
        """Open Genymotion Desktop application"""
        try:
            logger.info("Opening Genymotion Desktop")

            # Check if Genymotion exists
            genymotion_app_path = '/Applications/Genymotion.app'
            if not os.path.exists(genymotion_app_path):
                logger.error(f"Genymotion not found at: {genymotion_app_path}")
                return False

            # Open Genymotion using the 'open' command
            try:
                subprocess.Popen(['open', '-a', 'Genymotion'], start_new_session=True)
                time.sleep(2)
                logger.info("Genymotion Desktop opened successfully")
                return True
            except Exception as e:
                logger.error(f"Failed to open Genymotion: {e}")
                return False

        except Exception as e:
            logger.error(f"Failed to open Genymotion Desktop: {e}")
            return False

    def start_instance_by_name(self, instance_name: str) -> bool:
        """Start a specific Genymotion instance by name"""
        return self.start_instance(instance_name)

    def get_current_profile(self) -> Optional[Dict]:
        return self.current_profile

    def reset_instance(self, instance_name: str = "Pixel") -> bool:
        logger.info(f"Resetting Genymotion instance: {instance_name}")

        try:
            # Stop the instance first
            self.stop_instance(instance_name)
            time.sleep(2)

            # Factory reset the instance
            result = self._execute_gmtool(['admin', 'factoryreset', instance_name])
            if result and result.returncode == 0:
                logger.info(f"Genymotion instance '{instance_name}' reset successfully")

                # Reconfigure with new profile
                return self.configure_genymotion_instance(instance_name)
            else:
                logger.error(f"Failed to reset instance '{instance_name}'")
                if result:
                    logger.error(f"Error output: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Failed to reset Genymotion instance: {e}")
            return False

    # Backward compatibility methods
    def configure_bluestacks_instance(self, instance_name: str = "Pixel", profile: Dict = None) -> bool:
        """Backward compatibility method"""
        return self.configure_genymotion_instance(instance_name, profile)

    def open_multi_instance_manager(self) -> bool:
        """Backward compatibility method"""
        return self.open_genymotion_manager()

    def set_gps_location(self, latitude: float, longitude: float, device_name: str = None) -> bool:
        """Set GPS location using genyshell"""
        try:
            command = f"gps setlatitude {latitude}"
            result1 = self._execute_genyshell(command, device_name)

            command = f"gps setlongitude {longitude}"
            result2 = self._execute_genyshell(command, device_name)

            if result1 and result2 and result1.returncode == 0 and result2.returncode == 0:
                logger.info(f"GPS location set to {latitude}, {longitude}")
                return True
            else:
                logger.error("Failed to set GPS location")
                return False

        except Exception as e:
            logger.error(f"Error setting GPS location: {e}")
            return False

    def set_battery_level(self, level: int, device_name: str = None) -> bool:
        """Set battery level using genyshell"""
        try:
            if not 0 <= level <= 100:
                logger.error("Battery level must be between 0 and 100")
                return False

            command = f"battery setlevel {level}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Battery level set to {level}%")
                return True
            else:
                logger.error("Failed to set battery level")
                return False

        except Exception as e:
            logger.error(f"Error setting battery level: {e}")
            return False

    def rotate_device(self, orientation: str, device_name: str = None) -> bool:
        """Rotate device using genyshell"""
        try:
            valid_orientations = ['portrait', 'landscape', 'reverse_portrait', 'reverse_landscape']
            if orientation not in valid_orientations:
                logger.error(f"Invalid orientation. Must be one of: {valid_orientations}")
                return False

            command = f"rotation setorientation {orientation}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Device rotated to {orientation}")
                return True
            else:
                logger.error("Failed to rotate device")
                return False

        except Exception as e:
            logger.error(f"Error rotating device: {e}")
            return False

    def simulate_phone_call(self, phone_number: str, device_name: str = None) -> bool:
        """Simulate incoming phone call using genyshell"""
        try:
            command = f"phone call {phone_number}"
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"Simulated phone call from {phone_number}")
                return True
            else:
                logger.error("Failed to simulate phone call")
                return False

        except Exception as e:
            logger.error(f"Error simulating phone call: {e}")
            return False

    def send_sms(self, phone_number: str, message: str, device_name: str = None) -> bool:
        """Send SMS using genyshell"""
        try:
            command = f"phone sms {phone_number} \"{message}\""
            result = self._execute_genyshell(command, device_name)

            if result and result.returncode == 0:
                logger.info(f"SMS sent from {phone_number}: {message}")
                return True
            else:
                logger.error("Failed to send SMS")
                return False

        except Exception as e:
            logger.error(f"Error sending SMS: {e}")
            return False

    def get_device_adb_id(self, instance_name: str) -> Optional[str]:
        """Get ADB device ID for a Genymotion instance"""
        try:
            instances = self.get_available_instances()
            if instance_name in instances:
                instance_info = instances[instance_name]
                # Try adb_serial first, then device_id
                device_id = instance_info.get('adb_serial') or instance_info.get('device_id')
                if device_id:
                    logger.debug(f"Found device ID for {instance_name}: {device_id}")
                    return device_id
                else:
                    logger.warning(f"No ADB device ID found for instance: {instance_name}")
                    return None
            else:
                logger.error(f"Instance not found: {instance_name}")
                return None
        except Exception as e:
            logger.error(f"Error getting device ID for {instance_name}: {e}")
            return None

    def customize_device_identifiers(self, instance_name: str, randomize_all: bool = True,
                                   android_id: str = None, device_id: str = None,
                                   phone_number: str = None, operator_name: str = "Android") -> bool:
        """Comprehensive device identifier customization using Genymotion widgets"""
        try:
            logger.info(f"Customizing device identifiers for {instance_name}")
            success = True

            # Randomize or set Android ID (Identifiers widget)
            if randomize_all or android_id:
                android_success = self.set_android_id(instance_name, android_id)
                success &= android_success
                if android_success:
                    logger.info("✅ Android ID customized")
                else:
                    logger.warning("❌ Android ID customization failed")

            # Randomize or set Device ID/IMEI (Identifiers widget)
            if randomize_all or device_id:
                imei_success = self.set_device_id_imei(instance_name, device_id)
                success &= imei_success
                if imei_success:
                    logger.info("✅ Device ID/IMEI customized")
                else:
                    logger.warning("❌ Device ID/IMEI customization failed")

            # Set SIM operator and phone number (Baseband widget)
            if phone_number:
                sim_success = self.set_baseband_sim_operator(instance_name, operator_name, "310260", phone_number)
                success &= sim_success
                if sim_success:
                    logger.info("✅ SIM operator and phone number customized")
                else:
                    logger.warning("❌ SIM operator customization failed")

            # Set up advanced Magisk anti-detection system
            try:
                logger.info("🛡️ Setting up advanced Magisk anti-detection system...")
                magisk_success = self.install_advanced_magisk_spoofing(instance_name)
                if magisk_success:
                    logger.info("✅ Advanced anti-detection module installed successfully")

                    # Reboot device to activate all anti-detection measures
                    reboot_success = self.reboot_device_for_magisk(instance_name)
                    if reboot_success:
                        logger.info("✅ Device rebooted successfully")

                        # Wait additional time for all anti-detection measures to activate
                        logger.info("⏳ Waiting for anti-detection measures to fully activate...")
                        time.sleep(15)

                        # Install and configure Frida for runtime hooking
                        frida_success = self.install_frida_server(instance_name)
                        if frida_success:
                            logger.info("✅ Frida server installed and running")

                            # Create comprehensive Frida bypass script
                            script_success = self.create_frida_bypass_script(instance_name)
                            if script_success:
                                logger.info("✅ Frida bypass script created successfully")
                            else:
                                logger.warning("⚠️ Frida bypass script creation failed")
                        else:
                            logger.warning("⚠️ Frida server installation failed - runtime hooking unavailable")

                        # Run comprehensive verification
                        verification = self.verify_anti_detection_setup(instance_name)
                        if verification.get('overall_success'):
                            logger.info("✅ Advanced anti-detection system is fully active")
                            logger.info("🛡️ Multi-layer protection: Magisk + Frida + Property spoofing")
                        else:
                            logger.warning("⚠️ Some anti-detection measures may need additional time")
                    else:
                        logger.warning("⚠️ Device reboot failed, manual reboot may be required")
                else:
                    logger.warning("❌ Advanced anti-detection setup failed, falling back to basic spoofing")
                    # Fallback to basic property spoofing
                    fire7_success = self.set_amazon_fire7_properties(instance_name)
                    magisk_success = fire7_success

                success &= magisk_success
            except Exception as e:
                logger.error(f"Error setting up advanced anti-detection: {e}")
                # Fallback to basic property spoofing
                try:
                    fire7_success = self.set_amazon_fire7_properties(instance_name)
                    success &= fire7_success
                except Exception as fallback_e:
                    logger.error(f"Fallback spoofing also failed: {fallback_e}")

            # Get and log final identifiers
            try:
                identifiers = self.get_device_identifiers(instance_name)
                if identifiers:
                    logger.info(f"Final device identifiers:")
                    for key, value in identifiers.items():
                        logger.info(f"  {key}: {value}")

                # Run comprehensive anti-detection verification
                self.verify_anti_detection_setup(instance_name)

                # Print device properties diagnostic to verify spoofing
                self.print_device_properties_diagnostic(instance_name)

                # Print GSM properties diagnostic for analysis
                self.print_gsm_properties_diagnostic(instance_name)

            except Exception as e:
                logger.debug(f"Could not retrieve final identifiers: {e}")

            if success:
                logger.info(f"✅ All device identifiers customized successfully for {instance_name}")
            else:
                logger.warning(f"⚠️ Some device identifier customizations failed for {instance_name}")

            return success

        except Exception as e:
            logger.error(f"Error customizing device identifiers: {e}")
            return False

    def customize_android_device_properties(self, instance_name: str, device_name: str = None, phone_number: str = None) -> bool:
        """Customize Android device properties like device name and phone number"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for instance: {instance_name}")
                return False

            success = True

            # Set Android device name if provided
            if device_name:
                success &= self._set_android_device_name(device_id, device_name)
                # Note: Genymotion system properties should be set before device start

            # Use the new comprehensive identifier customization
            if phone_number:
                identifier_success = self.customize_device_identifiers(
                    instance_name,
                    randomize_all=True,  # Randomize Android ID and IMEI
                    phone_number=phone_number
                )
                success &= identifier_success

            # Reboot device to apply persistent properties
            if device_name and success:
                logger.info(f"Rebooting device {instance_name} to apply persistent properties...")
                try:
                    reboot_result = subprocess.run(f'adb -s {device_id} reboot', shell=True, capture_output=True, text=True, timeout=30)
                    if reboot_result.returncode == 0:
                        logger.info(f"Device {instance_name} rebooted successfully")
                        # Wait a moment for reboot to start
                        import time
                        time.sleep(3)
                    else:
                        logger.warning(f"Device reboot may have failed: {reboot_result.stderr}")
                except Exception as e:
                    logger.warning(f"Failed to reboot device: {e}")

            return success

        except Exception as e:
            logger.error(f"Error customizing Android device properties: {e}")
            return False

    def _set_android_device_name(self, device_id: str, device_name: str) -> bool:
        """Set Android device name using ADB"""
        try:
            # Set device name in Android settings and persistent properties
            commands = [
                f'adb -s {device_id} shell settings put global device_name "{device_name}"',
                f'adb -s {device_id} shell settings put secure bluetooth_name "{device_name}"',
                f'adb -s {device_id} shell setprop net.hostname "{device_name}"',
                # Set persistent properties that survive reboot (Genymotion method)
                f'adb -s {device_id} shell setprop persist.ro.product.model "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.device "{device_name}"',
                f'adb -s {device_id} shell setprop persist.ro.product.name "{device_name}"'
            ]

            for cmd in commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode != 0:
                        logger.warning(f"Command failed: {cmd}, Error: {result.stderr}")
                except subprocess.TimeoutExpired:
                    logger.warning(f"Command timed out: {cmd}")
                except Exception as e:
                    logger.warning(f"Command error: {cmd}, Error: {e}")

            logger.info(f"Android device name and persistent properties set to: {device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Android device name: {e}")
            return False

    def _set_genymotion_model_properties(self, instance_name: str, device_name: str) -> bool:
        """Set Genymotion system properties using gmtool admin edit"""
        try:
            # Use gmtool to set system properties that show up in device info
            edit_commands = [
                ['admin', 'edit', instance_name, '--sysprop', f'MODEL:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'PRODUCT:{device_name}'],
                ['admin', 'edit', instance_name, '--sysprop', f'DEVICE:{device_name}']
            ]

            for cmd in edit_commands:
                try:
                    result = self._execute_gmtool(cmd)
                    if not result or result.returncode != 0:
                        logger.warning(f"Gmtool command failed: {' '.join(cmd)}")
                        if result and result.stderr:
                            logger.warning(f"Error: {result.stderr}")
                except Exception as e:
                    logger.warning(f"Gmtool command error: {' '.join(cmd)}, Error: {e}")

            logger.info(f"Genymotion system properties set for {instance_name}: MODEL={device_name}")
            return True

        except Exception as e:
            logger.error(f"Failed to set Genymotion system properties: {e}")
            return False



    def set_baseband_sim_operator(self, instance_name: str, operator_name: str = "Android",
                                  mcc_mnc: str = "310260", phone_number: str = None) -> bool:
        """Set SIM operator information using Genymotion Shell only"""
        try:
            logger.info(f"Setting SIM operator for {instance_name} using Genymotion Shell")
            logger.info(f"Operator: {operator_name}, MCC/MNC: {mcc_mnc}")

            # Genymotion Shell baseband commands
            baseband_commands = [
                "phone baseband gsm status",
                "phone baseband gsm voice home",
                "phone baseband gsm data home"
            ]

            success = False
            for command in baseband_commands:
                try:
                    logger.debug(f"Executing baseband command: {command}")
                    result = self._execute_genyshell_interactive(command, timeout=30)
                    if result and result.returncode == 0:
                        success = True
                        logger.debug(f"✅ Baseband command succeeded: {command}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Baseband command failed: {command} - {error_msg}")
                except Exception as e:
                    logger.debug(f"Baseband command error: {command}, Error: {e}")

            # Phone number setting via Genymotion Shell
            phone_success = False
            if phone_number:
                try:
                    logger.debug(f"Setting phone number via SMS simulation: {phone_number}")
                    sms_command = f"phone baseband sms send {phone_number} SIM_CONFIG"
                    result = self._execute_genyshell_interactive(sms_command, timeout=30)
                    if result and result.returncode == 0:
                        phone_success = True
                        logger.debug(f"✅ Phone number SMS simulation succeeded: {phone_number}")
                    else:
                        error_msg = result.stderr if result else "No result returned"
                        logger.debug(f"❌ Phone number SMS simulation failed: {error_msg}")
                except Exception as e:
                    logger.debug(f"Phone number setting error: {e}")

            if success:
                logger.info(f"✅ SIM operator configured via Genymotion Shell: {operator_name} ({mcc_mnc})")
                if phone_number and phone_success:
                    logger.info(f"✅ Phone number configured: {phone_number}")
                elif phone_number:
                    logger.warning(f"⚠️ Phone number setting failed: {phone_number}")
            else:
                logger.warning(f"❌ SIM operator configuration failed")

            return success

        except Exception as e:
            logger.error(f"Failed to set SIM operator: {e}")
            return False

    def _set_genymotion_phone_number(self, instance_name: str, phone_number: str) -> bool:
        """Set phone number using Genymotion Shell baseband commands"""
        try:
            # Use the comprehensive baseband configuration
            return self.set_baseband_sim_operator(instance_name, phone_number=phone_number)

        except Exception as e:
            logger.error(f"Failed to set Genymotion phone number: {e}")
            return False

    def _set_android_phone_number(self, device_id: str, phone_number: str) -> bool:
        """Set Android phone number using multiple approaches for better compatibility"""
        try:
            success = False

            # Method 1: Set phone number in Android settings database
            settings_commands = [
                f'adb -s {device_id} shell settings put global device_provisioned 1',
                f'adb -s {device_id} shell settings put system phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put secure phone_number "{phone_number}"',
                f'adb -s {device_id} shell settings put global phone_number "{phone_number}"'
            ]

            for cmd in settings_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Settings command succeeded: {cmd}")
                    else:
                        logger.debug(f"Settings command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Settings command error: {cmd}, Error: {e}")

            # Method 2: Set telephony system properties
            telephony_commands = [
                f'adb -s {device_id} shell setprop gsm.sim.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop gsm.operator.numeric "310260"',
                f'adb -s {device_id} shell setprop ro.telephony.default_network "9"',
                f'adb -s {device_id} shell setprop telephony.lteOnCdmaDevice "1"'
            ]

            for cmd in telephony_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Telephony command succeeded: {cmd}")
                    else:
                        logger.debug(f"Telephony command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Telephony command error: {cmd}, Error: {e}")

            # Method 3: Try to write to telephony database (with root)
            db_commands = [
                f'adb -s {device_id} shell su -c "mkdir -p /data/misc/radio"',
                f'adb -s {device_id} shell su -c "echo \'{phone_number}\' > /data/misc/radio/phone_number.txt"',
                f'adb -s {device_id} shell su -c "chmod 644 /data/misc/radio/phone_number.txt"'
            ]

            for cmd in db_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success = True
                        logger.debug(f"Database command succeeded: {cmd}")
                    else:
                        logger.debug(f"Database command failed: {cmd}, Error: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Database command error: {cmd}, Error: {e}")

            # Method 4: Verify phone number was set by checking device settings
            if success:
                # Try to verify the phone number was actually set
                verify_commands = [
                    f'adb -s {device_id} shell settings get system phone_number',
                    f'adb -s {device_id} shell settings get secure phone_number',
                    f'adb -s {device_id} shell settings get global phone_number'
                ]

                for cmd in verify_commands:
                    try:
                        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                        if result.returncode == 0 and result.stdout.strip() and result.stdout.strip() != 'null':
                            logger.info(f"Phone number verification successful: {result.stdout.strip()}")
                            break
                    except Exception as e:
                        logger.debug(f"Verification command error: {cmd}, Error: {e}")

                logger.info(f"Android phone number set to: {phone_number}")
            else:
                logger.warning(f"All phone number setting methods failed for: {phone_number}")

            return success

        except Exception as e:
            logger.error(f"Failed to set Android phone number: {e}")
            return False



    def set_android_id(self, instance_name: str, android_id: str = None) -> bool:
        """Set Android ID using Genymotion Shell only"""
        try:
            logger.info(f"Setting Android ID for {instance_name} using Genymotion Shell")

            if android_id is None:
                # Generate random Android ID
                command = "android setandroidid random"
                logger.debug("Using random Android ID generation")
            else:
                # Set custom Android ID (must be 16 hex digits)
                if len(android_id) != 16 or not all(c in '0123456789abcdefABCDEF' for c in android_id):
                    logger.error("Android ID must be exactly 16 hexadecimal digits")
                    return False
                command = f"android setandroidid custom {android_id}"
                logger.debug(f"Using custom Android ID: {android_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Android ID set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Android ID via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Android ID: {e}")
            return False



    def set_device_id_imei(self, instance_name: str, device_id: str = None) -> bool:
        """Set Device ID/IMEI using Genymotion Shell only"""
        try:
            logger.info(f"Setting Device ID/IMEI for {instance_name} using Genymotion Shell")

            if device_id is None:
                # Generate random Device ID/IMEI
                command = "android setdeviceid random"
                logger.debug("Using random Device ID/IMEI generation")
            elif device_id.lower() == "none":
                # Remove Device ID
                command = "android setdeviceid none"
                logger.debug("Removing Device ID")
            else:
                # Set custom Device ID (alphanumeric, dots, dashes, underscores)
                command = f"android setdeviceid custom {device_id}"
                logger.debug(f"Using custom Device ID: {device_id}")

            # Execute the command using interactive genyshell
            result = self._execute_genyshell_interactive(command, timeout=30)
            if result and result.returncode == 0:
                logger.info(f"✅ Device ID/IMEI set successfully via Genyshell for {instance_name}")
                return True
            else:
                error_msg = result.stderr if result else "No result returned"
                logger.warning(f"❌ Failed to set Device ID/IMEI via Genyshell for {instance_name}: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"Error setting Device ID/IMEI: {e}")
            return False

    def get_device_identifiers(self, instance_name: str) -> dict:
        """Get current Android ID and Device ID/IMEI using Genymotion Shell"""
        try:
            logger.debug(f"Getting device identifiers for {instance_name}")
            identifiers = {}

            # Get Android ID
            try:
                result = self._execute_genyshell_interactive("android getandroidid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Android ID from output
                    for line in result.stdout.split('\n'):
                        if 'Android ID:' in line:
                            identifiers['android_id'] = line.split('Android ID:')[1].strip()
                            logger.debug(f"Found Android ID: {identifiers['android_id']}")
                            break
                else:
                    logger.debug("Failed to get Android ID")
            except Exception as e:
                logger.debug(f"Error getting Android ID: {e}")

            # Get Device ID/IMEI
            try:
                result = self._execute_genyshell_interactive("android getdeviceid", timeout=30)
                if result and result.returncode == 0:
                    # Parse Device ID from output
                    for line in result.stdout.split('\n'):
                        if 'Device ID:' in line:
                            identifiers['device_id'] = line.split('Device ID:')[1].strip()
                            logger.debug(f"Found Device ID: {identifiers['device_id']}")
                            break
                else:
                    logger.debug("Failed to get Device ID")
            except Exception as e:
                logger.debug(f"Error getting Device ID: {e}")

            return identifiers

        except Exception as e:
            logger.error(f"Error getting device identifiers: {e}")
            return {}

    def randomize_device_identifiers(self, instance_name: str) -> bool:
        """Randomize both Android ID and Device ID/IMEI (like clicking shuffle buttons)"""
        try:
            logger.info(f"Randomizing device identifiers for {instance_name}")

            # Randomize Android ID
            android_success = self.set_android_id(instance_name)

            # Randomize Device ID/IMEI
            imei_success = self.set_device_id_imei(instance_name)

            if android_success and imei_success:
                logger.info(f"All device identifiers randomized successfully for {instance_name}")
                return True
            else:
                logger.warning(f"Some device identifiers failed to randomize for {instance_name}")
                return False

        except Exception as e:
            logger.error(f"Error randomizing device identifiers: {e}")
            return False

    def set_amazon_fire7_properties(self, instance_name: str) -> bool:
        """Spoof device as Amazon Fire 7 (9th Gen) using comprehensive build properties"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Setting Amazon Fire 7 properties for {instance_name}")

            # Amazon Fire 7 (9th Gen) comprehensive properties
            fire7_props = {
                # Manufacturer spoofing (all variants)
                'ro.product.manufacturer': 'Amazon',
                'ro.product.bootimage.manufacturer': 'Amazon',
                'ro.product.odm.manufacturer': 'Amazon',
                'ro.product.product.manufacturer': 'Amazon',
                'ro.product.system_ext.manufacturer': 'Amazon',
                'ro.product.vendor.manufacturer': 'Amazon',
                'ro.product.vendor_dlkm.manufacturer': 'Amazon',

                # Model spoofing (all variants)
                'ro.product.model': 'Fire 7',
                'ro.product.bootimage.model': 'Fire 7',
                'ro.product.odm.model': 'Fire 7',
                'ro.product.product.model': 'Fire 7',
                'ro.product.system_ext.model': 'Fire 7',
                'ro.product.vendor.model': 'Fire 7',
                'ro.product.vendor_dlkm.model': 'Fire 7',

                # Brand spoofing (all variants)
                'ro.product.brand': 'Amazon',
                'ro.product.bootimage.brand': 'Amazon',
                'ro.product.odm.brand': 'Amazon',
                'ro.product.product.brand': 'Amazon',
                'ro.product.system_ext.brand': 'Amazon',
                'ro.product.vendor.brand': 'Amazon',
                'ro.product.vendor_dlkm.brand': 'Amazon',

                # Device name spoofing (all variants)
                'ro.product.device': 'mantis',
                'ro.product.bootimage.device': 'mantis',
                'ro.product.odm.device': 'mantis',
                'ro.product.product.device': 'mantis',
                'ro.product.system_ext.device': 'mantis',
                'ro.product.vendor.device': 'mantis',
                'ro.product.vendor_dlkm.device': 'mantis',

                # Product name spoofing (all variants)
                'ro.product.name': 'mantis',
                'ro.product.bootimage.name': 'mantis',
                'ro.product.odm.name': 'mantis',
                'ro.product.product.name': 'mantis',
                'ro.product.system_ext.name': 'mantis',
                'ro.product.vendor.name': 'mantis',
                'ro.product.vendor_dlkm.name': 'mantis',

                # Build properties
                'ro.build.product': 'mantis',
                'ro.build.user': 'amazon',
                'ro.build.host': 'ip-10-0-0-123',

                # Build fingerprints (real Amazon Fire 7)
                'ro.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.bootimage.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.odm.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.product.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.system_ext.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.vendor.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',
                'ro.vendor_dlkm.build.fingerprint': 'Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys',

                # Build description
                'ro.build.description': 'mantis-user 9 PS7327 0065800820220419 release-keys',

                # Hardware fingerprint (remove Genymotion traces)
                'ro.hardware.fingerprint': 'qcom',

                # Additional realistic properties
                'ro.build.characteristics': 'tablet',
                'ro.carrier': 'unknown',
                'ro.product.cpu.abi': 'arm64-v8a',
                'ro.product.cpu.abilist': 'arm64-v8a,armeabi-v7a,armeabi',
                'ro.product.cpu.abilist32': 'armeabi-v7a,armeabi',
                'ro.product.cpu.abilist64': 'arm64-v8a',

                # GSM/Telephony properties
                'gsm.version.baseband': '8976.gen.prodQ-00253-M8976FAAAANAZM-1',
                'gsm.version.ril-impl': 'Qualcomm RIL 1.0',
                'ro.baseband': 'msm',

                # IMEI (realistic example)
                'persist.gsm.imei': '352187085963841',

                # Network operator
                'persist.gsm.operator': '310410',
                'persist.gsm.operator_name': 'AT&T',
                'persist.gsm.sim.operator': '310410',
                'persist.gsm.sim.operator_name': 'AT&T',

                # Remove Genymotion-specific properties
                'ro.genymotion.device.version': '',
                'ro.genyd.caps.baseband': ''
            }

            success_count = 0
            total_props = len(fire7_props)

            for prop, value in fire7_props.items():
                try:
                    cmd = f'adb -s {device_id} shell su -c "setprop {prop} \'{value}\'"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        success_count += 1
                        logger.debug(f"✅ Set {prop} = {value}")
                    else:
                        logger.debug(f"❌ Failed to set {prop}: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Error setting {prop}: {e}")

            success = success_count > 0
            if success:
                logger.info(f"✅ Amazon Fire 7 properties set: {success_count}/{total_props} successful")
                logger.info("📱 Device now appears as: Amazon Fire 7 (9th Gen)")
                logger.info("🔧 Genymotion-specific properties removed")
            else:
                logger.warning(f"❌ Failed to set Amazon Fire 7 properties")

            return success

        except Exception as e:
            logger.error(f"Error setting Amazon Fire 7 properties: {e}")
            return False

    def install_advanced_magisk_spoofing(self, instance_name: str) -> bool:
        """Install comprehensive Magisk anti-detection setup"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Installing advanced Magisk anti-detection setup for {instance_name}")

            # Step 1: Check if Magisk is already installed
            magisk_check = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(magisk_check, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Magisk is already installed")
            else:
                logger.info("📦 Magisk not found - attempting automatic installation")

                # Attempt automatic Magisk installation with multiple methods
                install_success = False

                # Method 1: Direct installation script
                logger.info("🔧 Trying Method 1: Direct installation script")
                install_success = self._auto_install_magisk(device_id, instance_name)

                if not install_success:
                    # Method 2: Magisk Manager app installation
                    logger.info("🔧 Trying Method 2: Magisk Manager app installation")
                    install_success = self._install_magisk_via_manager(device_id)

                if install_success:
                    logger.info("✅ Magisk installed successfully")

                    # Verify installation
                    verify_check = f'adb -s {device_id} shell su -c "which magisk"'
                    verify_result = subprocess.run(verify_check, shell=True, capture_output=True, text=True, timeout=10)
                    if verify_result.returncode != 0 or "magisk" not in verify_result.stdout:
                        logger.warning("⚠️ Magisk installation verification failed")

                        # Show installation guide
                        guide = self._create_magisk_installation_guide(device_id)
                        logger.info(guide)
                        return False
                else:
                    logger.warning("⚠️ All automatic Magisk installation methods failed")
                    logger.info("🔧 Attempting basic property spoofing without Magisk...")

                    # Try basic property spoofing for non-ro properties
                    basic_success = self._apply_basic_property_spoofing(device_id, instance_name)
                    if basic_success:
                        logger.info("✅ Basic property spoofing applied (limited effectiveness)")
                        logger.warning("⚠️ For full anti-detection, Magisk installation is recommended")
                    else:
                        logger.warning("❌ Basic property spoofing also failed")

                    # Show comprehensive installation guide
                    guide = self._create_magisk_installation_guide(device_id)
                    logger.info(guide)
                    return basic_success

            # Step 2: Create comprehensive anti-detection module
            module_path = "/data/adb/modules/fire7_anti_detection"

            # Create module directory structure
            commands = [
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/META-INF/com/google/android"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/system/bin"',
                f'adb -s {device_id} shell su -c "mkdir -p {module_path}/system/lib"'
            ]

            for cmd in commands:
                subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Step 3: Create advanced module.prop
            module_prop_content = '''id=fire7_anti_detection
name=Amazon Fire 7 Anti-Detection Suite
version=v2.0
versionCode=2
author=GenymotionAutomation
description=Comprehensive anti-detection for Amazon Fire 7 spoofing with automation hiding
updateJson=https://raw.githubusercontent.com/example/fire7-spoof/main/update.json
'''

            # Write module.prop
            prop_cmd = f'adb -s {device_id} shell su -c "echo \'{module_prop_content}\' > {module_path}/module.prop"'
            subprocess.run(prop_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Step 4: Create comprehensive system.prop file (based on advanced guide)
            system_prop_content = '''# Amazon Fire 7 Anti-Detection System Properties
# Device Information
ro.product.manufacturer=Amazon
ro.product.brand=Amazon
ro.product.name=mantis
ro.product.device=mantis
ro.product.model=Fire 7

# Build Information
ro.build.product=mantis
ro.build.device=mantis
ro.build.brand=Amazon
ro.build.manufacturer=Amazon
ro.build.model=Fire 7
ro.build.fingerprint=Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys
ro.build.description=mantis-user 9 PS7327 0065800820220419 release-keys
ro.build.display.id=PS7327
ro.build.id=PS7327
ro.build.version.release=9
ro.build.version.sdk=28
ro.build.version.incremental=0065800820220419
ro.build.tags=release-keys
ro.build.type=user
ro.build.user=amazon
ro.build.host=ip-10-0-1-234

# Hardware Information
ro.hardware=qcom
ro.hardware.fingerprint=qcom
ro.board.platform=msm8937
ro.chipname=msm8937

# Remove Genymotion traces
ro.genymotion.device.version=
ro.genyd.caps.baseband=

# Bootimage properties
ro.bootimage.build.fingerprint=Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys
ro.product.bootimage.brand=Amazon
ro.product.bootimage.device=mantis
ro.product.bootimage.manufacturer=Amazon
ro.product.bootimage.model=Fire 7
ro.product.bootimage.name=mantis

# System properties
ro.product.system.brand=Amazon
ro.product.system.device=mantis
ro.product.system.manufacturer=Amazon
ro.product.system.model=Fire 7
ro.product.system.name=mantis

# Vendor properties
ro.product.vendor.brand=Amazon
ro.product.vendor.device=mantis
ro.product.vendor.manufacturer=Amazon
ro.product.vendor.model=Fire 7
ro.product.vendor.name=mantis

# ODM properties
ro.product.odm.brand=Amazon
ro.product.odm.device=mantis
ro.product.odm.manufacturer=Amazon
ro.product.odm.model=Fire 7
ro.product.odm.name=mantis

# CPU Information
ro.product.cpu.abi=arm64-v8a
ro.product.cpu.abilist=arm64-v8a,armeabi-v7a,armeabi
ro.product.cpu.abilist32=armeabi-v7a,armeabi
ro.product.cpu.abilist64=arm64-v8a

# Network/Telephony
ro.telephony.default_network=9
ro.build.characteristics=tablet
gsm.version.baseband=8937.gen.prodQ-00031-M8937AAAAANLYD-1
gsm.version.ril-impl=Qualcomm RIL 1.0
ro.baseband=msm

# Security
ro.build.selinux=1
ro.secure=1
ro.debuggable=0
ro.adb.secure=1
'''

            # Write system.prop
            system_prop_cmd = f'adb -s {device_id} shell su -c "echo \'{system_prop_content}\' > {module_path}/system.prop"'
            subprocess.run(system_prop_cmd, shell=True, capture_output=True, text=True, timeout=15)

            # Step 5: Create advanced service.sh with anti-detection and automation hiding
            service_sh_content = '''#!/system/bin/sh
# Amazon Fire 7 Advanced Anti-Detection Service

# Wait for system to be ready
sleep 30

# Runtime property modifications
resetprop ro.boot.hardware qcom
resetprop ro.boot.bootdevice 7824900.sdhci
resetprop ro.boot.serialno HT7A$(cat /dev/urandom | tr -dc '0-9' | fold -w 7 | head -n 1)

# Hide emulator traces
resetprop ro.kernel.qemu ""
resetprop ro.kernel.qemu.gles ""
resetprop ro.product.board ""

# Reset Genymotion-specific properties
resetprop ro.genymotion.device.version ""
resetprop ro.genyd.caps.baseband ""

# Set realistic hardware values
resetprop persist.vendor.radio.enable_voicecall 1
resetprop persist.vendor.radio.calls.on.ims 1

# Hide automation frameworks
mount -o bind /dev/null /system/bin/uiautomator 2>/dev/null
mount -o bind /dev/null /system/framework/uiautomator.jar 2>/dev/null
mount -o bind /dev/null /system/app/RemoteTestRunner 2>/dev/null

# Hide Appium processes
am force-stop io.appium.uiautomator2.server 2>/dev/null
am force-stop io.appium.uiautomator2.server.test 2>/dev/null

# Network detection bypass
resetprop net.hostname android-device

# Create fake CPU info
cat > /data/local/tmp/fake_cpuinfo << 'EOF'
processor	: 0
model name	: ARMv8 Processor rev 4 (v8l)
BogoMIPS	: 38.40
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer	: 0x51
CPU architecture: 8
CPU variant	: 0xa
CPU part	: 0x801
CPU revision	: 4

processor	: 1
model name	: ARMv8 Processor rev 4 (v8l)
BogoMIPS	: 38.40
Features	: fp asimd evtstrm aes pmull sha1 sha2 crc32
CPU implementer	: 0x51
CPU architecture: 8
CPU variant	: 0xa
CPU part	: 0x801
CPU revision	: 4
EOF

# Mount fake CPU info
mount -o bind /data/local/tmp/fake_cpuinfo /proc/cpuinfo 2>/dev/null

# Log completion
log -t "Fire7AntiDetect" "Amazon Fire 7 anti-detection setup completed"
'''

            # Write service.sh
            service_cmd = f'adb -s {device_id} shell su -c "echo \'{service_sh_content}\' > {module_path}/service.sh"'
            subprocess.run(service_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Make service.sh executable
            chmod_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/service.sh"'
            subprocess.run(chmod_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 6: Create enhanced post-fs-data.sh (based on EmuPropsSpoof guide)
            post_fs_content = '''#!/system/bin/sh
# Amazon Fire 7 Early Boot Anti-Detection (EmuPropsSpoof Enhanced)

# Core emulator property spoofing using resetprop
resetprop ro.kernel.qemu 0
resetprop ro.hardware qcom
resetprop ro.product.device mantis
resetprop ro.product.model "Fire 7"
resetprop ro.product.brand Amazon
resetprop ro.product.manufacturer Amazon
resetprop ro.build.fingerprint "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys"

# Additional Amazon Fire 7 specific properties
resetprop ro.build.product mantis
resetprop ro.build.device mantis
resetprop ro.build.description "mantis-user 9 PS7327 0065800820220419 release-keys"
resetprop ro.build.display.id PS7327
resetprop ro.build.id PS7327
resetprop ro.build.version.release 9
resetprop ro.build.version.sdk 28
resetprop ro.build.version.incremental 0065800820220419
resetprop ro.build.tags release-keys
resetprop ro.build.type user
resetprop ro.build.user amazon
resetprop ro.build.host ip-10-0-1-234

# Hardware spoofing
resetprop ro.board.platform msm8937
resetprop ro.chipname msm8937
resetprop ro.hardware.fingerprint qcom

# Remove Genymotion traces
resetprop ro.genymotion.device.version ""
resetprop ro.genyd.caps.baseband ""

# Remove emulator-specific files
rm -f /system/bin/qemu-props 2>/dev/null
rm -f /system/lib/libc_malloc_debug_qemu.so 2>/dev/null
rm -f /system/lib64/libc_malloc_debug_qemu.so 2>/dev/null

# Create fake version file
cat > /data/local/tmp/fake_version << 'EOF'
Linux version 4.14.117-perf+ (amazon@ip-10-0-1-234) (gcc version 4.9.x 20150123 (prerelease) (GCC)) #1 SMP PREEMPT Mon Apr 15 10:39:17 PDT 2019
EOF

# Hide automation testing packages
pm hide com.android.development 2>/dev/null
pm hide com.android.development_settings 2>/dev/null
pm hide com.example.android.apis 2>/dev/null
pm hide io.appium.uiautomator2.server 2>/dev/null
pm hide io.appium.uiautomator2.server.test 2>/dev/null

# Set realistic sensor properties
resetprop ro.hardware.sensors qcom
resetprop ro.qti.sensors.dev_ori 1
resetprop ro.qti.sensors.pmd 1
resetprop ro.qti.sensors.sta_detect 1

# Network properties
resetprop net.hostname android-device

# Security properties
resetprop ro.build.selinux 1
resetprop ro.secure 1
resetprop ro.debuggable 0
resetprop ro.adb.secure 1
'''

            # Write post-fs-data.sh
            post_fs_cmd = f'adb -s {device_id} shell su -c "echo \'{post_fs_content}\' > {module_path}/post-fs-data.sh"'
            subprocess.run(post_fs_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Make post-fs-data.sh executable
            chmod_post_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/post-fs-data.sh"'
            subprocess.run(chmod_post_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 7: Create config.sh for Magisk v25+ compatibility
            config_content = '''SKIPMOUNT=true
PROPFILE=false
POSTFSDATA=true
LATESTARTSERVICE=false
'''

            # Write config.sh
            config_cmd = f'adb -s {device_id} shell su -c "echo \'{config_content}\' > {module_path}/config.sh"'
            subprocess.run(config_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 8: Create customize.sh (empty but required)
            customize_content = '''#!/system/bin/sh
# Nothing to customize - all handled by post-fs-data.sh
'''

            # Write customize.sh
            customize_cmd = f'adb -s {device_id} shell su -c "echo \'{customize_content}\' > {module_path}/customize.sh"'
            subprocess.run(customize_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Make customize.sh executable
            chmod_customize_cmd = f'adb -s {device_id} shell su -c "chmod 755 {module_path}/customize.sh"'
            subprocess.run(chmod_customize_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Step 9: Enable the module
            touch_cmd = f'adb -s {device_id} shell su -c "touch {module_path}/auto_mount"'
            subprocess.run(touch_cmd, shell=True, capture_output=True, text=True, timeout=5)

            logger.info("✅ Advanced Magisk anti-detection module created successfully")
            logger.info("📱 Amazon Fire 7 comprehensive spoofing installed")
            logger.info("🛡️ Anti-detection measures: Property spoofing, automation hiding, emulator traces removal")
            logger.info("🔄 Reboot required to activate all anti-detection measures")

            return True

        except Exception as e:
            logger.error(f"Error setting up Magisk spoofing: {e}")
            return False

    def reboot_device_for_magisk(self, instance_name: str) -> bool:
        """Reboot device to activate Magisk modules"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔄 Rebooting {instance_name} to activate Magisk modules...")

            # Reboot the device
            reboot_cmd = f'adb -s {device_id} reboot'
            result = subprocess.run(reboot_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                logger.info("✅ Device reboot initiated")

                # Wait for device to come back online
                logger.info("⏳ Waiting for device to come back online...")
                time.sleep(30)  # Wait for reboot

                # Check if device is back online
                for attempt in range(12):  # 2 minutes total
                    try:
                        check_cmd = f'adb -s {device_id} shell echo "online"'
                        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        if result.returncode == 0 and "online" in result.stdout:
                            logger.info("✅ Device is back online")
                            return True
                    except:
                        pass

                    logger.debug(f"Device not ready yet, attempt {attempt + 1}/12")
                    time.sleep(10)

                logger.warning("⚠️ Device took longer than expected to come back online")
                return False
            else:
                logger.error(f"❌ Failed to reboot device: {result.stderr}")
                return False

        except Exception as e:
            logger.error(f"Error rebooting device: {e}")
            return False

    def check_magisk_module_status(self, instance_name: str) -> dict:
        """Check if Magisk module is active and properties are spoofed"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            logger.info(f"🔍 Checking Magisk module status for {instance_name}")

            # Check if module directory exists
            module_check = f'adb -s {device_id} shell su -c "ls /data/adb/modules/amazon_fire7_spoof/"'
            result = subprocess.run(module_check, shell=True, capture_output=True, text=True, timeout=10)

            module_installed = result.returncode == 0

            # Check key spoofed properties
            key_props = {
                'ro.product.manufacturer': 'Amazon',
                'ro.product.model': 'Fire 7',
                'ro.build.fingerprint': 'Amazon/mantis/mantis',
                'ro.hardware.fingerprint': 'qcom',
                'ro.genymotion.device.version': ''
            }

            spoofed_props = {}
            spoofing_success = True

            for prop, expected in key_props.items():
                try:
                    prop_cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(prop_cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        spoofed_props[prop] = value

                        if expected and expected not in value:
                            spoofing_success = False
                        elif not expected and value:  # Should be empty
                            spoofing_success = False
                except Exception as e:
                    logger.debug(f"Failed to check property {prop}: {e}")
                    spoofing_success = False

            status = {
                'module_installed': module_installed,
                'spoofing_success': spoofing_success,
                'properties': spoofed_props
            }

            if module_installed and spoofing_success:
                logger.info("✅ Magisk module is active and spoofing is working")
            elif module_installed:
                logger.warning("⚠️ Magisk module installed but spoofing may not be complete")
            else:
                logger.warning("❌ Magisk module not found or not installed")

            return status

        except Exception as e:
            logger.error(f"Error checking Magisk module status: {e}")
            return {}

    def verify_anti_detection_setup(self, instance_name: str) -> dict:
        """Comprehensive anti-detection verification based on advanced guide"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            logger.info(f"🔍 Running comprehensive anti-detection verification for {instance_name}")

            verification_results = {
                'properties_spoofed': False,
                'genymotion_traces_removed': False,
                'emulator_files_hidden': False,
                'automation_hidden': False,
                'cpu_info_spoofed': False,
                'overall_success': False
            }

            # 1. Verify key properties are spoofed
            key_props = {
                'ro.product.manufacturer': 'Amazon',
                'ro.product.model': 'Fire 7',
                'ro.build.fingerprint': 'Amazon/mantis/mantis',
                'ro.hardware': 'qcom',
                'ro.hardware.fingerprint': 'qcom'
            }

            props_success = True
            for prop, expected in key_props.items():
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if expected not in value:
                            props_success = False
                            logger.debug(f"❌ Property {prop}: {value} (expected: {expected})")
                        else:
                            logger.debug(f"✅ Property {prop}: {value}")
                except Exception as e:
                    logger.debug(f"Failed to check property {prop}: {e}")
                    props_success = False

            verification_results['properties_spoofed'] = props_success

            # 2. Check Genymotion traces are removed
            geny_traces = ['ro.genymotion.device.version', 'ro.genyd.caps.baseband']
            geny_success = True
            for prop in geny_traces:
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        if value:  # Should be empty
                            geny_success = False
                            logger.debug(f"❌ Genymotion trace found: {prop}={value}")
                        else:
                            logger.debug(f"✅ Genymotion trace removed: {prop}")
                except Exception as e:
                    logger.debug(f"Failed to check Genymotion trace {prop}: {e}")

            verification_results['genymotion_traces_removed'] = geny_success

            # 3. Check emulator files are hidden/removed
            emulator_files = ['/system/bin/qemu-props', '/system/lib/libc_malloc_debug_qemu.so']
            files_success = True
            for file_path in emulator_files:
                try:
                    cmd = f'adb -s {device_id} shell ls {file_path}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:  # File exists
                        files_success = False
                        logger.debug(f"❌ Emulator file still exists: {file_path}")
                    else:
                        logger.debug(f"✅ Emulator file hidden/removed: {file_path}")
                except Exception as e:
                    logger.debug(f"Error checking emulator file {file_path}: {e}")

            verification_results['emulator_files_hidden'] = files_success

            # 4. Check CPU info is spoofed
            try:
                cmd = f'adb -s {device_id} shell cat /proc/cpuinfo | head -5'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    cpu_info = result.stdout.lower()
                    if 'armv8' in cpu_info and 'x86' not in cpu_info and 'intel' not in cpu_info:
                        verification_results['cpu_info_spoofed'] = True
                        logger.debug("✅ CPU info shows ARM processor")
                    else:
                        logger.debug(f"❌ CPU info suspicious: {cpu_info[:100]}")
            except Exception as e:
                logger.debug(f"Error checking CPU info: {e}")

            # 5. Check automation frameworks are hidden
            automation_files = ['/system/bin/uiautomator', '/system/framework/uiautomator.jar']
            automation_success = True
            for file_path in automation_files:
                try:
                    cmd = f'adb -s {device_id} shell ls -la {file_path}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0 and '/dev/null' not in result.stdout:
                        automation_success = False
                        logger.debug(f"❌ Automation file not hidden: {file_path}")
                    else:
                        logger.debug(f"✅ Automation file hidden: {file_path}")
                except Exception as e:
                    logger.debug(f"Error checking automation file {file_path}: {e}")

            verification_results['automation_hidden'] = automation_success

            # Overall success calculation
            success_count = sum([
                verification_results['properties_spoofed'],
                verification_results['genymotion_traces_removed'],
                verification_results['emulator_files_hidden'],
                verification_results['cpu_info_spoofed'],
                verification_results['automation_hidden']
            ])

            verification_results['overall_success'] = success_count >= 4  # At least 4/5 checks pass

            # Log results
            logger.info("🔍 Anti-Detection Verification Results:")
            logger.info("=" * 60)
            logger.info(f"  📱 Properties Spoofed: {'✅' if verification_results['properties_spoofed'] else '❌'}")
            logger.info(f"  🚫 Genymotion Traces Removed: {'✅' if verification_results['genymotion_traces_removed'] else '❌'}")
            logger.info(f"  📁 Emulator Files Hidden: {'✅' if verification_results['emulator_files_hidden'] else '❌'}")
            logger.info(f"  🤖 Automation Hidden: {'✅' if verification_results['automation_hidden'] else '❌'}")
            logger.info(f"  💻 CPU Info Spoofed: {'✅' if verification_results['cpu_info_spoofed'] else '❌'}")
            logger.info("=" * 60)

            if verification_results['overall_success']:
                logger.info("✅ Anti-detection verification PASSED - Device appears as real Amazon Fire 7")
            else:
                logger.warning(f"⚠️ Anti-detection verification PARTIAL - {success_count}/5 checks passed")

            return verification_results

        except Exception as e:
            logger.error(f"Error running anti-detection verification: {e}")
            return {}

    def install_frida_server(self, instance_name: str) -> bool:
        """Install and configure Frida server for runtime hooking"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Installing Frida server for runtime hooking on {instance_name}")

            # Check if Frida server is already running
            check_cmd = f'adb -s {device_id} shell ps | grep frida-server'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "frida-server" in result.stdout:
                logger.info("✅ Frida server is already running")
                return True

            # Download and install Frida server (assuming it's available locally)
            frida_server_path = "/data/local/tmp/frida-server"

            # Check if frida-server binary exists
            check_binary = f'adb -s {device_id} shell ls {frida_server_path}'
            result = subprocess.run(check_binary, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode != 0:
                logger.warning("⚠️ Frida server binary not found on device")
                logger.info("📦 Please install frida-server manually:")
                logger.info(f"   1. Download frida-server for Android x86/ARM")
                logger.info(f"   2. adb -s {device_id} push frida-server {frida_server_path}")
                logger.info(f"   3. adb -s {device_id} shell chmod 755 {frida_server_path}")
                return False

            # Make frida-server executable and start it
            commands = [
                f'adb -s {device_id} shell su -c "chmod 755 {frida_server_path}"',
                f'adb -s {device_id} shell su -c "nohup {frida_server_path} > /dev/null 2>&1 &"'
            ]

            for cmd in commands:
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                if result.returncode != 0:
                    logger.error(f"Failed to execute: {cmd}")
                    return False

            # Wait a moment for server to start
            time.sleep(3)

            # Verify Frida server is running
            check_cmd = f'adb -s {device_id} shell ps | grep frida-server'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0 and "frida-server" in result.stdout:
                logger.info("✅ Frida server started successfully")
                return True
            else:
                logger.error("❌ Failed to start Frida server")
                return False

        except Exception as e:
            logger.error(f"Error installing Frida server: {e}")
            return False

    def create_frida_bypass_script(self, instance_name: str) -> bool:
        """Create comprehensive Frida bypass script for runtime hooking"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return False

            logger.info(f"🔧 Creating comprehensive Frida bypass script for {instance_name}")

            # Comprehensive Frida bypass script (based on your guide)
            frida_script = '''Java.perform(() => {
    console.log("🛡️ Amazon Fire 7 Anti-Detection Frida Script Loaded");

    // === 1. System Property Check Bypass ===
    const SystemProperties = Java.use("android.os.SystemProperties");
    SystemProperties.get.overload("java.lang.String").implementation = function (key) {
        // Amazon Fire 7 property spoofing
        if (key === "ro.kernel.qemu") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> 0`);
            return "0";
        } else if (key === "ro.hardware") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> qcom`);
            return "qcom";
        } else if (key === "ro.product.device") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> mantis`);
            return "mantis";
        } else if (key === "ro.product.model") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Fire 7`);
            return "Fire 7";
        } else if (key === "ro.product.manufacturer") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon`);
            return "Amazon";
        } else if (key === "ro.product.brand") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon`);
            return "Amazon";
        } else if (key === "ro.build.fingerprint") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> Amazon fingerprint`);
            return "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys";
        } else if (key === "ro.genymotion.device.version") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> empty`);
            return "";
        } else if (key === "ro.genyd.caps.baseband") {
            console.log(`✅ Bypassing SystemProperty: ${key} -> empty`);
            return "";
        }
        return this.get(key);
    };

    // === 2. File System Check Bypass ===
    const File = Java.use("java.io.File");
    File.exists.implementation = function () {
        const path = this.getAbsolutePath();
        if (
            path.includes("qemu_pipe") ||
            path.includes("genyd") ||
            path.includes("goldfish") ||
            path.includes("ranchu") ||
            path.includes("libc_malloc_debug_qemu") ||
            path.includes("genymotion") ||
            path.includes("qemu-props") ||
            path.includes("ueventd.android_x86.rc") ||
            path.includes("fstab.android_x86")
        ) {
            console.log(`✅ Bypassing file check for: ${path}`);
            return false;
        }
        return this.exists();
    };

    // === 3. Network Properties Bypass ===
    const WifiInfo = Java.use("android.net.wifi.WifiInfo");
    const NetworkInfo = Java.use("android.net.NetworkInfo");

    WifiInfo.getMacAddress.implementation = function () {
        console.log("✅ Spoofing MAC address");
        return "02:00:00:00:00:00"; // Realistic Fire 7 MAC
    };

    WifiInfo.getIpAddress.implementation = function () {
        console.log("✅ Spoofing IP address");
        return 3232235777; // ***********
    };

    NetworkInfo.isConnected.implementation = function () {
        console.log("✅ Spoofing network connection status");
        return true;
    };

    // === 4. Sensor Check Bypass ===
    const SensorManager = Java.use("android.hardware.SensorManager");
    const Sensor = Java.use("android.hardware.Sensor");

    SensorManager.getDefaultSensor.implementation = function (type) {
        console.log("✅ Intercepted getDefaultSensor for type: " + type);
        const sensor = this.getDefaultSensor(type);
        if (sensor === null) {
            console.log("✅ Returning dummy sensor for type: " + type);
            // Return the original result but log the interception
        }
        return sensor;
    };

    SensorManager.registerListener.overload(
        "android.hardware.SensorEventListener",
        "android.hardware.Sensor",
        "int"
    ).implementation = function (listener, sensor, delay) {
        console.log("✅ Spoofing sensor listener for type: " + sensor.getType());
        const result = this.registerListener(listener, sensor, delay);

        // Send realistic Fire 7 sensor data
        setTimeout(() => {
            sendRealisticSensorData(listener, sensor.getType());
        }, 1000);

        return result;
    };

    function sendRealisticSensorData(listener, type) {
        Java.scheduleOnMainThread(() => {
            try {
                const SensorEvent = Java.use("android.hardware.SensorEvent");
                const sensorEvent = SensorEvent.$new(3);

                if (type === 1) { // Accelerometer
                    sensorEvent.values = [0.1, 9.8, 0.2]; // Realistic tablet orientation
                } else if (type === 4) { // Gyroscope
                    sensorEvent.values = [0.01, 0.02, 0.01]; // Minimal movement
                } else if (type === 2) { // Magnetometer
                    sensorEvent.values = [25.0, -15.0, 45.0]; // Realistic magnetic field
                } else {
                    sensorEvent.values = [1.0, 1.0, 1.0];
                }

                listener.onSensorChanged(sensorEvent);
                console.log(`✅ Sent realistic Fire 7 sensor data for type ${type}`);
            } catch (err) {
                console.error("❌ Sensor spoof error:", err);
            }
        });
    }

    // === 5. Build Information Bypass ===
    const Build = Java.use("android.os.Build");
    Build.MANUFACTURER.value = "Amazon";
    Build.MODEL.value = "Fire 7";
    Build.BRAND.value = "Amazon";
    Build.DEVICE.value = "mantis";
    Build.PRODUCT.value = "mantis";
    Build.HARDWARE.value = "qcom";
    Build.FINGERPRINT.value = "Amazon/mantis/mantis:9/PS7327/0065800820220419:user/release-keys";

    // === 6. Package Manager Bypass ===
    const PackageManager = Java.use("android.content.pm.PackageManager");
    PackageManager.getInstalledPackages.overload("int").implementation = function(flags) {
        const packages = this.getInstalledPackages(flags);
        const filteredPackages = [];

        for (let i = 0; i < packages.size(); i++) {
            const pkg = packages.get(i);
            const packageName = pkg.packageName.value;

            // Hide automation/testing packages
            if (!packageName.includes("uiautomator") &&
                !packageName.includes("appium") &&
                !packageName.includes("frida") &&
                !packageName.includes("xposed")) {
                filteredPackages.push(pkg);
            } else {
                console.log(`✅ Hiding package: ${packageName}`);
            }
        }

        return Java.use("java.util.ArrayList").$new(filteredPackages);
    };

    console.log("✅ Amazon Fire 7 Anti-Detection Frida Script Active - All hooks loaded");
});'''

            # Write Frida script to device
            script_path = "/data/local/tmp/fire7_bypass.js"

            # Create script file
            script_cmd = f'adb -s {device_id} shell su -c "echo \'{frida_script}\' > {script_path}"'
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                logger.info("✅ Frida bypass script created successfully")
                logger.info(f"📁 Script location: {script_path}")
                logger.info("🔧 Usage: frida -U -n <app_package> -l /data/local/tmp/fire7_bypass.js")
                return True
            else:
                logger.error("❌ Failed to create Frida bypass script")
                return False

        except Exception as e:
            logger.error(f"Error creating Frida bypass script: {e}")
            return False

    def _auto_install_magisk(self, device_id: str, instance_name: str) -> bool:
        """Fully automated Magisk installation - no user interaction required"""
        try:
            logger.info(f"🔧 Starting fully automated Magisk installation for {instance_name}")

            # Step 1: Download Magisk APK using the most reliable method
            if not self._download_magisk_apk_simple(device_id):
                logger.error("❌ Failed to download Magisk APK")
                return False

            # Step 2: Install Magisk APK using direct ADB install (official method)
            logger.info("📱 Installing Magisk APK using direct ADB install...")

            if not self._install_magisk_apk_direct(device_id):
                logger.error("❌ Failed to install Magisk APK")
                return False

            # Step 3: Fully automated Magisk setup (no user interaction)
            logger.info("🔧 Performing fully automated Magisk setup...")

            if not self._setup_magisk_automated(device_id):
                logger.warning("⚠️ Automated setup failed, trying alternative method")
                return self._install_magisk_alternative_method(device_id)

            logger.info("✅ Fully automated Magisk installation completed successfully")
            return True

            # Write installation script to device
            script_path = "/data/local/tmp/install_magisk.sh"
            script_cmd = f'adb -s {device_id} shell su -c "echo \'{install_script}\' > {script_path}"'
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                logger.error(f"❌ Failed to create installation script: {result.stderr}")
                return False

            # Make script executable and run it
            chmod_cmd = f'adb -s {device_id} shell su -c "chmod 755 {script_path}"'
            subprocess.run(chmod_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Remount system as read-write
            remount_cmd = f'adb -s {device_id} shell su -c "mount -o remount,rw /system"'
            subprocess.run(remount_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Execute installation script
            logger.info("⚙️ Executing Magisk installation...")
            exec_cmd = f'adb -s {device_id} shell su -c "sh {script_path}"'
            result = subprocess.run(exec_cmd, shell=True, capture_output=True, text=True, timeout=180)

            if result.returncode == 0 and ("installation completed" in result.stdout or "Installation process finished" in result.stdout):
                logger.info("✅ Magisk installation script completed successfully")

                # Step 4: Try alternative installation via Magisk Manager
                logger.info("🔧 Attempting Magisk Manager installation as backup...")
                self._install_magisk_via_manager_automated(device_id)

                # Step 5: Reboot device to activate Magisk
                logger.info("🔄 Rebooting device to activate Magisk...")
                reboot_cmd = f'adb -s {device_id} reboot'
                subprocess.run(reboot_cmd, shell=True, capture_output=True, text=True, timeout=10)

                # Wait for device to come back online
                logger.info("⏳ Waiting for device to restart...")
                time.sleep(45)  # Wait for reboot

                # Wait for ADB to be ready
                for attempt in range(20):  # 2 minutes total
                    try:
                        check_cmd = f'adb -s {device_id} shell echo "ready"'
                        result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=5)
                        if result.returncode == 0 and "ready" in result.stdout:
                            logger.info("✅ Device is back online after Magisk installation")

                            # Final verification
                            verify_cmd = f'adb -s {device_id} shell su -c "which magisk"'
                            verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)
                            if verify_result.returncode == 0 and "magisk" in verify_result.stdout:
                                logger.info("✅ Magisk installation verified successfully")
                                return True
                            else:
                                logger.warning("⚠️ Magisk installation may need additional setup")
                                return True  # Return true as installation likely succeeded
                    except:
                        pass

                    logger.debug(f"Waiting for device to be ready, attempt {attempt + 1}/20")
                    time.sleep(6)

                logger.warning("⚠️ Device took longer than expected to restart")
                return True  # Assume success even if we can't verify immediately

            else:
                logger.error(f"❌ Magisk installation script failed: {result.stderr}")
                logger.info("🔧 Trying alternative Magisk Manager installation...")
                return self._install_magisk_via_manager_automated(device_id)

        except Exception as e:
            logger.error(f"Error during automatic Magisk installation: {e}")
            return False



    def _apply_basic_property_spoofing(self, device_id: str, instance_name: str) -> bool:
        """Apply basic property spoofing for properties that can be modified without Magisk"""
        try:
            logger.info("🔧 Applying basic property spoofing (non-ro properties)")

            # Properties that can potentially be modified without Magisk
            basic_properties = {
                # GSM/Network properties (some may be writable)
                'gsm.operator.alpha': 'Amazon Wireless',
                'gsm.sim.operator.alpha': 'Amazon Wireless',
                'net.hostname': 'amazon-fire7',

                # Debug properties (usually writable)
                'debug.tracing.mcc': '310',
                'debug.tracing.mnc': '410',

                # Persist properties (may be writable)
                'persist.vendor.radio.enable_voicecall': '1',
                'persist.vendor.radio.calls.on.ims': '1',

                # System properties that might be writable
                'sys.usb.config': 'mtp,adb',
                'sys.boot_completed': '1'
            }

            success_count = 0
            total_count = len(basic_properties)

            for prop, value in basic_properties.items():
                try:
                    # Try setting property without su first
                    cmd = f'adb -s {device_id} shell setprop {prop} "{value}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

                    if result.returncode == 0:
                        # Verify the property was set
                        verify_cmd = f'adb -s {device_id} shell getprop {prop}'
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=5)

                        if verify_result.returncode == 0 and value in verify_result.stdout.strip():
                            success_count += 1
                            logger.debug(f"✅ Set {prop} = {value}")
                        else:
                            logger.debug(f"❌ Failed to verify {prop}")
                    else:
                        logger.debug(f"❌ Failed to set {prop}: {result.stderr}")

                except Exception as e:
                    logger.debug(f"Error setting property {prop}: {e}")

            # Try to hide some Genymotion traces using available methods
            try:
                # Rename Genymotion-specific files (if possible)
                hide_commands = [
                    f'adb -s {device_id} shell su -c "mv /dev/socket/genyd /dev/socket/_genyd_hidden" 2>/dev/null',
                    f'adb -s {device_id} shell su -c "chmod 000 /system/bin/qemu-props" 2>/dev/null'
                ]

                for cmd in hide_commands:
                    try:
                        subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    except:
                        pass

            except Exception as e:
                logger.debug(f"Error hiding Genymotion traces: {e}")

            success_rate = success_count / total_count if total_count > 0 else 0
            logger.info(f"📊 Basic property spoofing: {success_count}/{total_count} ({success_rate:.1%})")

            if success_count > 0:
                logger.info("✅ Basic property spoofing partially successful")
                logger.warning("⚠️ Limited effectiveness - Magisk required for full spoofing")
                return True
            else:
                logger.warning("❌ Basic property spoofing failed")
                return False

        except Exception as e:
            logger.error(f"Error applying basic property spoofing: {e}")
            return False

    def _download_magisk_comprehensive(self, device_id: str) -> bool:
        """Comprehensive Magisk download using multiple methods and sources"""
        try:
            logger.info("🔧 Starting comprehensive Magisk download with multiple fallback methods")

            # Multiple Magisk versions (APK only - ZIP files don't exist in releases)
            magisk_sources = [
                # Official GitHub releases (verified working URLs)
                {
                    "version": "v27.0",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.apk"
                },
                {
                    "version": "v26.1",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v26.1/Magisk-v26.1.apk"
                },
                {
                    "version": "v25.2",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v25.2/Magisk-v25.2.apk"
                },
                {
                    "version": "v24.3",
                    "apk_url": "https://github.com/topjohnwu/Magisk/releases/download/v24.3/Magisk-v24.3.apk"
                }
            ]

            for source in magisk_sources:
                logger.info(f"📥 Trying Magisk {source['version']}...")

                # Method 1: Host download + ADB push (most reliable)
                if self._download_method_host_push_apk_only(device_id, source):
                    return True

                # Method 2: Device download using multiple tools
                if self._download_method_device_direct_apk_only(device_id, source):
                    return True

            logger.error("❌ All comprehensive download methods failed")
            return False

        except Exception as e:
            logger.error(f"Error in comprehensive Magisk download: {e}")
            return False

    def _download_method_host_push_apk_only(self, device_id: str, source: dict) -> bool:
        """Method 1: Download on host machine and push via ADB"""
        try:
            logger.debug(f"📥 Method 1: Host download + ADB push for {source['version']}")

            import urllib.request
            import tempfile
            import os

            # Create temporary file for APK only
            with tempfile.NamedTemporaryFile(suffix='.apk', delete=False) as apk_file:
                apk_path = apk_file.name

            try:
                # Download APK with multiple methods
                logger.debug("📥 Downloading Magisk APK on host...")
                success = False

                # Try urllib first
                try:
                    urllib.request.urlretrieve(source['apk_url'], apk_path)
                    success = True
                    logger.debug("✅ urllib APK download successful")
                except Exception as e:
                    logger.debug(f"urllib failed for APK: {e}")

                # Try requests if urllib fails
                if not success:
                    try:
                        import requests
                        response = requests.get(source['apk_url'], timeout=60)
                        response.raise_for_status()
                        with open(apk_path, 'wb') as f:
                            f.write(response.content)
                        success = True
                        logger.debug("✅ requests APK download successful")
                    except Exception as e:
                        logger.debug(f"requests failed for APK: {e}")

                # Try curl as last resort
                if not success:
                    try:
                        curl_cmd = f'curl -L -o "{apk_path}" "{source["apk_url"]}"'
                        result = subprocess.run(curl_cmd, shell=True, capture_output=True, text=True, timeout=120)
                        if result.returncode == 0:
                            success = True
                            logger.debug("✅ curl APK download successful")
                    except Exception as e:
                        logger.debug(f"curl failed for APK: {e}")

                if not success:
                    raise Exception("All APK download methods failed")

                # Verify APK file
                if not os.path.exists(apk_path) or os.path.getsize(apk_path) < 1000000:  # At least 1MB
                    raise Exception("APK file invalid or too small")

                file_size = os.path.getsize(apk_path)
                logger.debug(f"✅ APK verified - Size: {file_size} bytes")

                # Push APK to device
                logger.debug("📁 Pushing Magisk APK to device...")
                push_apk_cmd = f'adb -s {device_id} push "{apk_path}" /sdcard/Magisk.apk'
                result = subprocess.run(push_apk_cmd, shell=True, capture_output=True, text=True, timeout=60)
                if result.returncode != 0:
                    raise Exception(f"Failed to push APK: {result.stderr}")

                # Verify the push was successful
                verify_push_cmd = f'adb -s {device_id} shell "ls -la /sdcard/Magisk.apk"'
                verify_result = subprocess.run(verify_push_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if verify_result.returncode != 0 or "Magisk.apk" not in verify_result.stdout:
                    raise Exception("APK push verification failed - file not found on device")

                # Check file size matches
                device_size_info = verify_result.stdout.strip()
                logger.debug(f"Device APK info: {device_size_info}")

                logger.info(f"✅ Method 1 successful: {source['version']} APK downloaded and pushed")
                return True

            finally:
                # Clean up temporary file
                try:
                    os.unlink(apk_path)
                except:
                    pass

        except Exception as e:
            logger.debug(f"Method 1 failed for {source['version']}: {e}")
            return False

    def _download_method_device_direct_apk_only(self, device_id: str, source: dict) -> bool:
        """Method 2: Download directly on device using available tools"""
        try:
            logger.debug(f"📥 Method 2: Device direct download for {source['version']}")

            # Try multiple download tools on device for APK only
            download_tools = [
                # Try curl with various options
                f'curl -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'curl --insecure -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'curl -k -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try wget with various options
                f'wget -O /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'wget --no-check-certificate -O /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try busybox wget
                f'busybox wget -O /sdcard/Magisk.apk "{source["apk_url"]}"',

                # Try toybox curl/wget
                f'toybox curl -L -o /sdcard/Magisk.apk "{source["apk_url"]}"',
                f'toybox wget -O /sdcard/Magisk.apk "{source["apk_url"]}"'
            ]

            # Try downloading APK
            for tool_cmd in download_tools:
                try:
                    logger.debug(f"Trying: {tool_cmd.split()[0]}")
                    cmd = f'adb -s {device_id} shell su -c "cd /sdcard && {tool_cmd}"'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)

                    if result.returncode == 0:
                        # Verify file exists and has content
                        verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/Magisk.apk"'
                        verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)

                        if verify_result.returncode == 0 and "Magisk.apk" in verify_result.stdout:
                            # Extract file size from ls output
                            size_info = verify_result.stdout.strip()
                            try:
                                # Parse ls output to get file size
                                parts = size_info.split()
                                if len(parts) >= 5:
                                    file_size = int(parts[4])
                                    if file_size > 1000000:  # At least 1MB
                                        logger.info(f"✅ Method 2 successful: {source['version']} APK downloaded on device ({file_size} bytes)")
                                        return True
                            except:
                                # Fallback: if we can't parse size, assume success if file exists
                                logger.info(f"✅ Method 2 successful: {source['version']} APK downloaded on device")
                                return True
                except Exception as e:
                    logger.debug(f"Tool failed: {tool_cmd.split()[0]} - {e}")
                    continue

            raise Exception("All APK download tools failed on device")

        except Exception as e:
            logger.debug(f"Method 2 failed for {source['version']}: {e}")
            return False

    def _download_method_alternative(self, device_id: str, source: dict) -> bool:
        """Method 3: Alternative download methods"""
        try:
            logger.debug(f"📥 Method 3: Alternative download for {source['version']}")

            # Method 3a: Try using Android's built-in download manager
            try:
                logger.debug("Trying Android DownloadManager...")

                # Create a simple download script using Android APIs
                download_script = f'''
am start -a android.intent.action.VIEW -d "{source["apk_url"]}"
sleep 5
am start -a android.intent.action.VIEW -d "{source["zip_url"]}"
'''

                script_cmd = f'adb -s {device_id} shell su -c \'{download_script}\''
                result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=30)

                # Wait a bit for downloads
                time.sleep(10)

                # Check if files appeared in Downloads
                check_cmd = f'adb -s {device_id} shell "find /sdcard -name \'*agisk*\' -type f"'
                check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if check_result.returncode == 0 and check_result.stdout.strip():
                    logger.debug("✅ Files found via DownloadManager")
                    # Move files to expected location
                    files = check_result.stdout.strip().split('\n')
                    for file_path in files:
                        if '.apk' in file_path:
                            move_cmd = f'adb -s {device_id} shell su -c "cp \'{file_path}\' /sdcard/Magisk.apk"'
                            subprocess.run(move_cmd, shell=True, capture_output=True, text=True, timeout=10)
                        elif '.zip' in file_path:
                            move_cmd = f'adb -s {device_id} shell su -c "cp \'{file_path}\' /sdcard/Magisk.zip"'
                            subprocess.run(move_cmd, shell=True, capture_output=True, text=True, timeout=10)

                    logger.info(f"✅ Method 3 successful: {source['version']} via DownloadManager")
                    return True

            except Exception as e:
                logger.debug(f"DownloadManager method failed: {e}")

            # Method 3b: Try using netcat/nc if available
            try:
                logger.debug("Trying netcat method...")

                # This is a more advanced method that would require setting up a local server
                # For now, we'll skip this but it's a placeholder for future enhancement

            except Exception as e:
                logger.debug(f"Netcat method failed: {e}")

            return False

        except Exception as e:
            logger.debug(f"Method 3 failed for {source['version']}: {e}")
            return False

    def _automate_magisk_installation(self, device_id: str) -> bool:
        """Automate Magisk installation via UI interactions"""
        try:
            logger.debug("🤖 Attempting UI automation for Magisk installation...")

            # UI automation commands for Magisk Manager (reduced wait times)
            automation_commands = [
                # Try to tap Install button (common coordinates)
                f'adb -s {device_id} shell input tap 540 400',  # Install button
                f'adb -s {device_id} shell sleep 1',

                # Try Direct Install option
                f'adb -s {device_id} shell input tap 540 500',  # Direct Install
                f'adb -s {device_id} shell sleep 1',

                # Confirm installation
                f'adb -s {device_id} shell input tap 540 600',  # Confirm
                f'adb -s {device_id} shell sleep 2',

                # Alternative coordinates if first attempt fails
                f'adb -s {device_id} shell input tap 360 400',  # Alternative Install
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 360 500',  # Alternative Direct Install
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 360 600',  # Alternative Confirm
            ]

            # Execute automation commands
            for cmd in automation_commands:
                try:
                    subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                except:
                    pass  # Continue even if individual commands fail

            # Wait for installation to complete (reduced time)
            logger.debug("⏳ Waiting for installation to complete...")
            time.sleep(15)  # Reduced from 30 to 15 seconds

            # Check if Magisk is now installed
            check_cmd = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.debug("✅ Magisk installation detected via automation")
                return True

            # Alternative check: look for Magisk processes
            process_cmd = f'adb -s {device_id} shell "ps | grep magisk"'
            process_result = subprocess.run(process_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if process_result.returncode == 0 and "magisk" in process_result.stdout:
                logger.debug("✅ Magisk processes detected")
                return True

            return False

        except Exception as e:
            logger.debug(f"UI automation failed: {e}")
            return False

    def _install_magisk_alternative_method(self, device_id: str) -> bool:
        """Alternative Magisk installation method"""
        try:
            logger.debug("🔧 Trying alternative Magisk installation method...")

            # Method: Extract and install Magisk binary from APK
            extract_script = '''
# Extract Magisk binary from APK
cd /data/local/tmp
mkdir -p magisk_extract
cd magisk_extract

# Copy and extract APK
cp /sdcard/Magisk.apk ./magisk.zip
unzip -o magisk.zip

# Look for Magisk binaries in common locations
for arch in arm64-v8a armeabi-v7a x86_64 x86; do
    if [ -f "lib/$arch/libmagisk.so" ]; then
        cp "lib/$arch/libmagisk.so" /data/local/tmp/magisk
        chmod 755 /data/local/tmp/magisk
        break
    fi
    if [ -f "assets/magisk" ]; then
        cp "assets/magisk" /data/local/tmp/magisk
        chmod 755 /data/local/tmp/magisk
        break
    fi
done

# Create basic Magisk setup
mkdir -p /data/adb/magisk
mkdir -p /data/adb/modules

# Try to start Magisk daemon
if [ -f "/data/local/tmp/magisk" ]; then
    /data/local/tmp/magisk --daemon &
    echo "Magisk daemon started"
else
    echo "Magisk binary not found"
fi
'''

            # Execute extraction script
            script_cmd = f'adb -s {device_id} shell su -c \'{extract_script}\''
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=60)

            if "Magisk daemon started" in result.stdout:
                logger.debug("✅ Alternative Magisk installation successful")
                return True

            return False

        except Exception as e:
            logger.debug(f"Alternative installation method failed: {e}")
            return False

    def _verify_magisk_installation(self, device_id: str):
        """Verify Magisk installation and provide diagnostic info"""
        try:
            logger.debug("🔍 Verifying Magisk installation...")

            # Check if Magisk package is installed
            pkg_cmd = f'adb -s {device_id} shell pm list packages | grep magisk'
            pkg_result = subprocess.run(pkg_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if pkg_result.returncode == 0 and pkg_result.stdout.strip():
                logger.info(f"✅ Magisk package installed: {pkg_result.stdout.strip()}")

                # Get package info
                info_cmd = f'adb -s {device_id} shell dumpsys package com.topjohnwu.magisk | grep -A 5 "Activity Resolver Table"'
                info_result = subprocess.run(info_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if info_result.returncode == 0:
                    logger.debug(f"📱 Magisk package info: {info_result.stdout}")

                # Check main activity
                activity_cmd = f'adb -s {device_id} shell pm dump com.topjohnwu.magisk | grep -A 3 "android.intent.action.MAIN"'
                activity_result = subprocess.run(activity_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if activity_result.returncode == 0:
                    logger.debug(f"🎯 Main activity info: {activity_result.stdout}")

            else:
                logger.warning("⚠️ Magisk package not found in installed packages")

                # List all installed packages for debugging
                all_pkg_cmd = f'adb -s {device_id} shell pm list packages | head -10'
                all_pkg_result = subprocess.run(all_pkg_cmd, shell=True, capture_output=True, text=True, timeout=10)
                logger.debug(f"📦 Sample installed packages: {all_pkg_result.stdout}")

        except Exception as e:
            logger.debug(f"Error verifying Magisk installation: {e}")

    def _download_magisk_apk_simple(self, device_id: str) -> bool:
        """Simple, reliable Magisk APK download"""
        try:
            logger.info("📥 Downloading Magisk APK...")

            # Working Magisk versions (verified URLs)
            magisk_versions = [
                "v27.0",
                "v26.1",
                "v25.2",
                "v24.3"
            ]

            for version in magisk_versions:
                try:
                    apk_url = f"https://github.com/topjohnwu/Magisk/releases/download/{version}/Magisk-{version}.apk"
                    logger.info(f"📥 Trying Magisk {version}...")

                    # Download on host and push to device (most reliable)
                    import urllib.request
                    import tempfile
                    import os

                    with tempfile.NamedTemporaryFile(suffix='.apk', delete=False) as tmp_file:
                        try:
                            # Download APK
                            urllib.request.urlretrieve(apk_url, tmp_file.name)
                            file_size = os.path.getsize(tmp_file.name)

                            if file_size < 1000000:  # Less than 1MB is suspicious
                                logger.debug(f"APK too small ({file_size} bytes), trying next version")
                                continue

                            logger.info(f"✅ Downloaded Magisk {version} ({file_size} bytes)")

                            # Push to device
                            push_cmd = f'adb -s {device_id} push "{tmp_file.name}" /sdcard/Magisk.apk'
                            result = subprocess.run(push_cmd, shell=True, capture_output=True, text=True, timeout=60)

                            if result.returncode == 0:
                                logger.info("✅ APK pushed to device successfully")
                                return True
                            else:
                                logger.debug(f"Push failed: {result.stderr}")

                        finally:
                            try:
                                os.unlink(tmp_file.name)
                            except:
                                pass

                except Exception as e:
                    logger.debug(f"Failed to download {version}: {e}")
                    continue

            logger.error("❌ All Magisk download attempts failed")
            return False

        except Exception as e:
            logger.error(f"Error downloading Magisk APK: {e}")
            return False

    def _install_magisk_apk_direct(self, device_id: str) -> bool:
        """Install Magisk APK using direct ADB install from local file"""
        try:
            logger.info("📱 Installing Magisk APK from local file...")

            import os

            # Try multiple possible locations for Magisk APK
            possible_paths = [
                "/Users/<USER>/Downloads/Magisk-v27.0.apk",
                "/Users/<USER>/Downloads/Magisk-v26.4.apk",
                "/Users/<USER>/Downloads/Magisk-v26.1.apk",
                "/Users/<USER>/Downloads/Magisk-v25.2.apk",
                # Also check current directory
                "./Magisk-v27.0.apk",
                "./Magisk-v26.4.apk",
                # Check Downloads with different naming
                "/Users/<USER>/Downloads/Magisk.apk"
            ]

            magisk_apk_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    file_size = os.path.getsize(path)
                    if file_size > 1000000:  # At least 1MB
                        magisk_apk_path = path
                        logger.info(f"✅ Found Magisk APK: {path} ({file_size} bytes)")
                        break
                    else:
                        logger.debug(f"File too small: {path} ({file_size} bytes)")
                else:
                    logger.debug(f"File not found: {path}")

            if not magisk_apk_path:
                logger.warning("⚠️ No local Magisk APK found, trying download method...")
                return self._download_and_install_magisk(device_id)

            # Install using the local Magisk APK file (correct method)
            install_cmd = f'adb -s {device_id} install -r "{magisk_apk_path}"'
            logger.info(f"🔧 Installing using: adb install -r {os.path.basename(magisk_apk_path)}")

            result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True, timeout=120)

            if result.returncode == 0 or "Success" in result.stdout:
                logger.info("✅ Magisk APK installed successfully from local file")

                # Verify installation
                verify_install_cmd = f'adb -s {device_id} shell pm list packages | grep magisk'
                verify_install_result = subprocess.run(verify_install_cmd, shell=True, capture_output=True, text=True, timeout=10)

                if verify_install_result.returncode == 0 and "magisk" in verify_install_result.stdout:
                    logger.info(f"✅ Installation verified: {verify_install_result.stdout.strip()}")
                    return True
                else:
                    logger.warning("⚠️ Installation completed but package not found")
                    return True  # Still return True as install command succeeded
            else:
                logger.error(f"❌ Installation failed: {result.stderr}")

                # Fallback: Try shell pm install method
                logger.info("🔧 Trying fallback method: shell pm install")
                return self._install_magisk_via_shell_pm(device_id)

        except Exception as e:
            logger.error(f"Error installing Magisk APK: {e}")
            # Fallback method
            return self._install_magisk_via_shell_pm(device_id)

    def _download_and_install_magisk(self, device_id: str) -> bool:
        """Download Magisk and install directly (fallback method)"""
        try:
            logger.info("📥 Downloading Magisk APK as fallback...")

            import urllib.request
            import tempfile
            import os

            # Download to temp file
            magisk_url = "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.apk"

            with tempfile.NamedTemporaryFile(suffix='.apk', delete=False) as tmp_file:
                try:
                    urllib.request.urlretrieve(magisk_url, tmp_file.name)
                    file_size = os.path.getsize(tmp_file.name)
                    logger.info(f"✅ Downloaded Magisk APK ({file_size} bytes)")

                    # Install using local file path
                    install_cmd = f'adb -s {device_id} install -r "{tmp_file.name}"'
                    logger.info(f"🔧 Installing downloaded APK...")

                    result = subprocess.run(install_cmd, shell=True, capture_output=True, text=True, timeout=120)

                    if result.returncode == 0 or "Success" in result.stdout:
                        logger.info("✅ Downloaded Magisk APK installed successfully")
                        return True
                    else:
                        logger.error(f"❌ Downloaded APK installation failed: {result.stderr}")
                        return False

                finally:
                    try:
                        os.unlink(tmp_file.name)
                    except:
                        pass

        except Exception as e:
            logger.error(f"Error downloading and installing Magisk: {e}")
            return False

    def _install_magisk_via_shell_pm(self, device_id: str) -> bool:
        """Fallback: Install Magisk using shell pm install on device file"""
        try:
            logger.info("🔧 Using fallback method: shell pm install")

            # Verify APK exists on device
            verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/Magisk.apk"'
            verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if verify_result.returncode != 0:
                logger.error("❌ Magisk.apk not found on device for fallback method")
                return False

            logger.info(f"📁 APK found on device: {verify_result.stdout.strip()}")

            # Use shell pm install (works with device file paths)
            install_methods = [
                f'adb -s {device_id} shell su -c "pm install -r /sdcard/Magisk.apk"',
                f'adb -s {device_id} shell su -c "pm install -r -f /sdcard/Magisk.apk"',
                f'adb -s {device_id} shell su -c "pm install --user 0 -r /sdcard/Magisk.apk"'
            ]

            for method in install_methods:
                try:
                    logger.info(f"🔧 Trying: {method}")
                    result = subprocess.run(method, shell=True, capture_output=True, text=True, timeout=120)

                    if result.returncode == 0 or "Success" in result.stdout:
                        logger.info("✅ Magisk APK installed via shell pm install")

                        # Verify installation
                        verify_install_cmd = f'adb -s {device_id} shell pm list packages | grep magisk'
                        verify_install_result = subprocess.run(verify_install_cmd, shell=True, capture_output=True, text=True, timeout=10)

                        if verify_install_result.returncode == 0 and "magisk" in verify_install_result.stdout:
                            logger.info(f"✅ Installation verified: {verify_install_result.stdout.strip()}")
                            return True
                        else:
                            logger.warning("⚠️ Installation completed but package not found")
                            return True
                    else:
                        logger.debug(f"Method failed: {result.stderr}")

                except Exception as e:
                    logger.debug(f"Method error: {e}")
                    continue

            logger.error("❌ All shell pm install methods failed")
            return False

        except Exception as e:
            logger.error(f"Error in shell pm install fallback: {e}")
            return False

    def _setup_magisk_automated(self, device_id: str) -> bool:
        """Fully automated Magisk setup with Superuser dialog handling"""
        try:
            logger.info("🤖 Starting fully automated Magisk setup...")

            # Step 1: Pre-grant Superuser permissions to Magisk
            logger.info("🔐 Pre-granting Superuser permissions to Magisk...")
            self._pre_grant_superuser_permissions(device_id)

            # Step 2: Launch Magisk Manager
            logger.info("📱 Launching Magisk Manager...")

            # Try multiple launch methods
            launch_methods = [
                f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity',
                f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/a.c',
                f'adb -s {device_id} shell am start com.topjohnwu.magisk',
                f'adb -s {device_id} shell monkey -p com.topjohnwu.magisk 1'
            ]

            launch_success = False
            for launch_cmd in launch_methods:
                try:
                    result = subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logger.info("✅ Magisk Manager launched")
                        launch_success = True
                        break
                except:
                    continue

            if not launch_success:
                logger.warning("⚠️ Could not launch Magisk Manager UI")
                # Continue with alternative setup
            else:
                time.sleep(3)  # Wait for app to start loading

            # Step 3: Handle Superuser dialog first (appears when Magisk Manager launches)
            logger.info("🔐 Handling Superuser dialog (appears first)...")
            for attempt in range(15):  # 15 seconds to handle superuser
                try:
                    if self._handle_superuser_dialog_specifically(device_id):
                        logger.info("✅ Superuser dialog handled successfully!")
                        break
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Superuser handling attempt {attempt}: {e}")
                    time.sleep(1)

            time.sleep(2)  # Wait after superuser dialog

            # Step 4: Handle notification permission dialog (appears second)
            logger.info("🔔 Handling notification permission dialog...")
            for attempt in range(10):  # 10 seconds to handle notification
                try:
                    if self._handle_notification_dialog_specifically(device_id):
                        logger.info("✅ Notification permission handled successfully!")
                        break
                    time.sleep(1)
                except Exception as e:
                    logger.debug(f"Notification handling attempt {attempt}: {e}")
                    time.sleep(1)

            time.sleep(2)  # Wait after notification dialog

            # Step 5: Now click Install button in Magisk Manager
            logger.info("🔧 Clicking Install button in Magisk Manager...")
            install_success = False
            for attempt in range(5):  # Try multiple times
                if self._click_magisk_install_button(device_id):
                    install_success = True
                    break
                time.sleep(2)

            if not install_success:
                logger.warning("⚠️ Could not click Install button, trying fallback installation")

            # Step 6: Wait for installation to complete
            logger.info("⏳ Waiting for Magisk installation to complete...")
            time.sleep(15)  # Give installation time to complete

            # Step 5: Automated Magisk installation via command line (fallback)
            logger.info("🔧 Performing automated Magisk installation...")

            # Create automated installation script
            install_script = '''
# Automated Magisk Installation Script
export PATH=/system/bin:/system/xbin:$PATH

# Create Magisk directories
mkdir -p /data/adb/magisk
mkdir -p /data/adb/modules
mkdir -p /data/adb/post-fs-data.d
mkdir -p /data/adb/service.d

# Set permissions
chmod 755 /data/adb
chmod 755 /data/adb/magisk
chmod 755 /data/adb/modules

# Extract Magisk binary from APK if possible
cd /data/local/tmp
cp /sdcard/Magisk.apk ./magisk.zip
unzip -o magisk.zip >/dev/null 2>&1

# Look for Magisk binaries
for arch in arm64-v8a armeabi-v7a x86_64 x86; do
    if [ -f "lib/$arch/libmagisk.so" ]; then
        cp "lib/$arch/libmagisk.so" /data/adb/magisk/magisk
        chmod 755 /data/adb/magisk/magisk
        break
    fi
done

# Alternative: look for magisk binary in assets
if [ -f "assets/magisk" ]; then
    cp "assets/magisk" /data/adb/magisk/magisk
    chmod 755 /data/adb/magisk/magisk
fi

# Create basic Magisk setup
echo "MAGISK_VER=27.0" > /data/adb/magisk/config
echo "MAGISK_VER_CODE=27000" >> /data/adb/magisk/config

# Try to start Magisk daemon if binary exists
if [ -f "/data/adb/magisk/magisk" ]; then
    /data/adb/magisk/magisk --daemon &
    echo "Magisk daemon started"
else
    echo "Magisk binary not found, basic setup completed"
fi

echo "Automated installation completed"
'''

            # Execute installation script as fallback
            logger.info("🔧 Executing fallback Magisk installation script...")
            script_cmd = f'adb -s {device_id} shell su -c \'{install_script}\''

            try:
                result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=60)
                if result.returncode == 0:
                    logger.info("✅ Fallback installation script completed successfully")
                else:
                    logger.warning(f"⚠️ Fallback installation script failed: {result.stderr}")
            except subprocess.TimeoutExpired:
                logger.warning("⚠️ Fallback installation script timed out")
            except Exception as e:
                logger.warning(f"⚠️ Fallback installation script error: {e}")

            # Step 6: Verify installation
            logger.info("🔍 Verifying Magisk installation...")
            time.sleep(5)

            # Check if Magisk is properly installed
            verification_result = self._verify_magisk_installation(device_id)

            if verification_result:
                logger.info("✅ Magisk installation verified successfully")
                return True
            else:
                logger.warning("⚠️ Magisk installation verification failed, but basic setup completed")
                return False

        except Exception as e:
            logger.error(f"Error in automated Magisk setup: {e}")
            return False

    def _perform_automated_magisk_install(self, device_id: str):
        """Perform automated Magisk installation via UI automation"""
        try:
            logger.debug("🤖 Performing automated UI interactions...")

            # Automated UI interactions for Magisk installation
            ui_commands = [
                # Tap Install button (multiple possible locations)
                f'adb -s {device_id} shell input tap 540 400',
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 540 500',  # Direct Install
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 540 600',  # Confirm
                f'adb -s {device_id} shell sleep 2',

                # Alternative coordinates
                f'adb -s {device_id} shell input tap 360 400',
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 360 500',
                f'adb -s {device_id} shell sleep 1',
                f'adb -s {device_id} shell input tap 360 600',
            ]

            # Execute UI automation
            for cmd in ui_commands:
                try:
                    subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                except:
                    pass  # Continue even if individual commands fail

        except Exception as e:
            logger.debug(f"UI automation error: {e}")

    def _handle_superuser_dialog_automated(self, device_id: str):
        """Automatically handle Superuser request dialogs and notification permissions"""
        try:
            logger.info("🔐 Monitoring for dialogs (Superuser, Notifications, etc.)...")

            # Monitor for various dialogs for up to 10 seconds (faster response)
            for attempt in range(10):
                try:
                    # Method 1: Get current UI dump with detailed analysis
                    ui_dump_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                    ui_result = subprocess.run(ui_dump_cmd, shell=True, capture_output=True, text=True, timeout=5)
                    ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

                    # Method 2: Take screenshot for visual analysis
                    screenshot_cmd = f'adb -s {device_id} shell screencap -p'
                    screenshot_result = subprocess.run(screenshot_cmd, shell=True, capture_output=True, timeout=5)
                    has_screenshot = screenshot_result.returncode == 0

                    # Method 3: Check window focus and activity
                    focus_cmd = f'adb -s {device_id} shell dumpsys window windows | grep -E "mCurrentFocus|mFocusedApp"'
                    focus_result = subprocess.run(focus_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    focus_content = focus_result.stdout if focus_result.returncode == 0 else ""

                    # Enhanced keyword detection for Superuser dialog
                    superuser_keywords = [
                        "Superuser Request", "ACCESS_SUPERUSER", "Magisk is requesting",
                        "Allow", "Deny", "Grant", "superuser", "root access",
                        "com.topjohnwu.magisk", "Remember choice", "forever"
                    ]

                    # Enhanced keyword detection for notification dialog
                    notification_keywords = [
                        "Allow Magisk to send you notifications", "notifications?",
                        "ALLOW", "DON'T ALLOW", "notification", "Magisk"
                    ]

                    # Log current UI state for debugging
                    logger.debug(f"🔍 UI Content Sample: {ui_content[:200]}...")
                    logger.debug(f"🔍 Focus Info: {focus_content}")

                    # Check for Superuser dialog with multiple methods
                    superuser_detected = (
                        any(keyword in ui_content.lower() for keyword in [k.lower() for k in superuser_keywords]) or
                        any(keyword in focus_content.lower() for keyword in ["superuser", "magisk"]) or
                        self._detect_dialog_by_buttons(device_id, ["Allow", "Deny"])
                    )

                    if superuser_detected:
                        logger.info("🔐 Superuser dialog detected! Automatically granting access...")
                        return self._handle_superuser_permission(device_id, ui_content)

                    # Check for notification permission dialog
                    notification_detected = (
                        any(keyword in ui_content.lower() for keyword in [k.lower() for k in notification_keywords]) or
                        self._detect_dialog_by_buttons(device_id, ["ALLOW", "DON'T ALLOW"])
                    )

                    if notification_detected:
                        logger.info("🔔 Notification permission dialog detected! Allowing...")
                        return self._handle_notification_permission(device_id)

                    # Check for any dialog with Allow/Deny pattern
                    generic_dialog_detected = self._detect_dialog_by_buttons(device_id, ["Allow", "Don't allow", "OK", "Cancel"])

                    if generic_dialog_detected:
                        logger.info("📱 Generic permission dialog detected! Allowing...")
                        return self._handle_generic_permission(device_id)

                    else:
                        # No dialog found, wait a bit
                        time.sleep(1)

                except Exception as e:
                    logger.debug(f"Dialog check attempt {attempt} failed: {e}")
                    time.sleep(1)
                    continue

            logger.debug("🔐 No dialogs detected within timeout")
            return False

        except Exception as e:
            logger.error(f"Error handling dialogs: {e}")
            return False

    def _detect_dialog_by_buttons(self, device_id: str, button_texts: list) -> bool:
        """Detect dialogs by looking for specific button texts"""
        try:
            # Get UI hierarchy with more details
            ui_cmd = f'adb -s {device_id} shell uiautomator dump --compressed=false /dev/stdout'
            ui_result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if ui_result.returncode == 0:
                ui_content = ui_result.stdout
                # Look for button elements with specific text
                for button_text in button_texts:
                    if f'text="{button_text}"' in ui_content or f"'{button_text}'" in ui_content:
                        logger.debug(f"🔍 Found button with text: {button_text}")
                        return True

            return False

        except Exception as e:
            logger.debug(f"Button detection failed: {e}")
            return False

    def _handle_superuser_permission(self, device_id: str, ui_content: str) -> bool:
        """Handle Superuser permission dialog specifically"""
        try:
            # Step 1: Select "Remember choice forever" if available
            if "Remember choice forever" in ui_content:
                logger.info("📝 Selecting 'Remember choice forever'...")
                forever_coords = [(112, 822), (112, 800), (100, 820)]
                for coord in forever_coords:
                    try:
                        tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                        subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                        time.sleep(0.5)
                        break
                    except:
                        continue

            # Step 2: Click "Allow" button
            logger.info("✅ Clicking 'Allow' button...")
            allow_coords = [
                (449, 911),  # Main "Allow" button position from screenshot
                (450, 910), (448, 912),  # Alternative positions
                (400, 900), (500, 900),  # Fallback positions
            ]

            for coord in allow_coords:
                try:
                    tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    time.sleep(2)

                    # Check if dialog disappeared
                    check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                    if "Allow" not in check_result.stdout or "Superuser" not in check_result.stdout:
                        logger.info("✅ Superuser dialog dismissed successfully!")
                        return True

                except Exception as e:
                    logger.debug(f"Superuser tap failed: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Error handling Superuser permission: {e}")
            return False

    def _handle_notification_permission(self, device_id: str) -> bool:
        """Handle notification permission dialog"""
        try:
            logger.info("🔔 Handling notification permission...")

            # Click "ALLOW" button for notifications
            allow_coords = [
                (395, 552),  # "ALLOW" button position from screenshot
                (400, 550), (390, 555),  # Alternative positions
                (395, 560), (395, 545),  # More alternatives
            ]

            for coord in allow_coords:
                try:
                    tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    time.sleep(2)

                    # Check if dialog disappeared
                    check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                    if "notifications?" not in check_result.stdout:
                        logger.info("✅ Notification permission granted!")
                        return True

                except Exception as e:
                    logger.debug(f"Notification tap failed: {e}")
                    continue

            return False

        except Exception as e:
            logger.error(f"Error handling notification permission: {e}")
            return False

    def _handle_generic_permission(self, device_id: str) -> bool:
        """Handle generic permission dialogs"""
        try:
            logger.info("📱 Handling generic permission dialog...")

            # Try to click "Allow" in various common positions
            allow_coords = [
                (400, 600), (400, 550), (400, 500),  # Common positions
                (450, 600), (350, 600),  # Alternative positions
            ]

            for coord in allow_coords:
                try:
                    tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    time.sleep(1)
                except:
                    continue

            return True

        except Exception as e:
            logger.error(f"Error handling generic permission: {e}")
            return False

    def _click_magisk_install_button(self, device_id: str) -> bool:
        """Click the Install button in Magisk Manager"""
        try:
            logger.info("🔧 Looking for Magisk Install button...")

            # Get current UI to verify we're in Magisk Manager
            ui_dump_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
            ui_result = subprocess.run(ui_dump_cmd, shell=True, capture_output=True, text=True, timeout=5)
            ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

            # Check if we're in Magisk Manager
            if "Magisk" not in ui_content or "Install" not in ui_content:
                logger.warning("⚠️ Magisk Manager not visible, trying to launch it...")
                launch_cmd = f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity'
                subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=5)
                time.sleep(3)

                # Get UI again
                ui_result = subprocess.run(ui_dump_cmd, shell=True, capture_output=True, text=True, timeout=5)
                ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

            # Click the Install button (from screenshot coordinates)
            install_coords = [
                (485, 365),  # "Install" button position from screenshot
                (480, 365), (490, 365),  # Alternative positions
                (485, 360), (485, 370),  # More alternatives
                (450, 365), (520, 365),  # Wider range
            ]

            for coord in install_coords:
                try:
                    logger.info(f"🔧 Clicking Install button at {coord}...")
                    tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    time.sleep(2)

                    # Check if installation dialog appeared
                    check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                    check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                    # Look for installation-related UI elements
                    if any(keyword in check_result.stdout for keyword in [
                        "Direct Install", "Select and Patch", "Installation",
                        "Superuser Request", "Installing", "Patching"
                    ]):
                        logger.info("✅ Install button clicked successfully!")

                        # If we see installation options, click "Direct Install"
                        if "Direct Install" in check_result.stdout:
                            logger.info("🔧 Selecting 'Direct Install' option...")
                            direct_install_coords = [
                                (400, 500), (400, 450), (400, 550),  # Common positions
                                (300, 500), (500, 500),  # Alternative positions
                            ]

                            for direct_coord in direct_install_coords:
                                try:
                                    direct_tap_cmd = f'adb -s {device_id} shell input tap {direct_coord[0]} {direct_coord[1]}'
                                    subprocess.run(direct_tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                                    time.sleep(1)
                                except:
                                    continue

                        return True

                except Exception as e:
                    logger.debug(f"Install button tap failed: {e}")
                    continue

            logger.warning("⚠️ Could not find or click Install button")
            return False

        except Exception as e:
            logger.error(f"Error clicking Magisk Install button: {e}")
            return False

    def _aggressive_dialog_handler(self, device_id: str) -> bool:
        """Aggressively handle any dialogs by clicking common Allow/OK positions"""
        try:
            # Take a screenshot to check if there's any dialog-like overlay
            screenshot_cmd = f'adb -s {device_id} shell screencap -p'
            screenshot_result = subprocess.run(screenshot_cmd, shell=True, capture_output=True, timeout=3)

            # Get current activity to see if we're in a dialog state
            activity_cmd = f'adb -s {device_id} shell dumpsys activity activities | grep -E "mResumedActivity|mFocusedActivity"'
            activity_result = subprocess.run(activity_cmd, shell=True, capture_output=True, text=True, timeout=3)

            # Check if UI suggests a dialog is present
            ui_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
            ui_result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=3)
            ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

            # Look for dialog indicators in UI
            dialog_indicators = [
                "android:id/button1", "android:id/button2",  # Standard dialog buttons
                "com.android.packageinstaller", "com.android.permissioncontroller",  # Permission dialogs
                "AlertDialog", "Dialog", "popup", "overlay"
            ]

            has_dialog_ui = any(indicator in ui_content for indicator in dialog_indicators)

            if has_dialog_ui or "button" in ui_content.lower():
                logger.info("🎯 Dialog-like UI detected! Trying aggressive clicking...")

                # Try clicking common "Allow" button positions
                allow_positions = [
                    # From your screenshots
                    (395, 552),  # ALLOW notifications
                    (449, 911),  # Allow superuser
                    (485, 365),  # Install button

                    # Common Android dialog positions
                    (400, 600), (400, 550), (400, 500),  # Center-right
                    (300, 600), (500, 600),  # Left and right
                    (400, 650), (400, 700),  # Lower positions

                    # Standard dialog button positions
                    (540, 600), (280, 600),  # Standard OK/Cancel positions
                    (450, 580), (350, 580),  # Alternative positions
                ]

                for pos in allow_positions:
                    try:
                        tap_cmd = f'adb -s {device_id} shell input tap {pos[0]} {pos[1]}'
                        subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=2)
                        time.sleep(0.5)

                        # Quick check if dialog disappeared
                        quick_ui_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                        quick_ui_result = subprocess.run(quick_ui_cmd, shell=True, capture_output=True, text=True, timeout=2)

                        if quick_ui_result.returncode == 0:
                            new_ui_content = quick_ui_result.stdout
                            # If UI changed significantly, we probably clicked something
                            if len(new_ui_content) != len(ui_content):
                                logger.info(f"✅ Aggressive click at {pos} seems to have worked!")
                                return True

                    except Exception as e:
                        logger.debug(f"Aggressive click at {pos} failed: {e}")
                        continue

                # Also try key events as fallback
                logger.info("🔄 Trying key events as fallback...")
                key_events = [
                    "KEYCODE_ENTER",  # Enter/OK
                    "KEYCODE_SPACE",  # Space (sometimes works for buttons)
                    "KEYCODE_TAB KEYCODE_ENTER",  # Tab to next element then enter
                ]

                for key_event in key_events:
                    try:
                        if " " in key_event:
                            # Multiple key events
                            for key in key_event.split():
                                key_cmd = f'adb -s {device_id} shell input keyevent {key}'
                                subprocess.run(key_cmd, shell=True, capture_output=True, text=True, timeout=2)
                                time.sleep(0.3)
                        else:
                            # Single key event
                            key_cmd = f'adb -s {device_id} shell input keyevent {key_event}'
                            subprocess.run(key_cmd, shell=True, capture_output=True, text=True, timeout=2)

                        time.sleep(0.5)
                    except:
                        continue

                return True  # We tried our best

            return False

        except Exception as e:
            logger.debug(f"Aggressive dialog handler error: {e}")
            return False

    def _blind_allow_clicker(self, device_id: str) -> bool:
        """Blindly click common Allow/OK positions every few seconds"""
        try:
            # This is a last resort - just click known good positions
            logger.debug("🎯 Blind clicking common Allow positions...")

            # Positions from your screenshots and common Android patterns
            blind_positions = [
                (395, 552),  # ALLOW notifications (from your screenshot)
                (449, 911),  # Allow superuser (from your screenshot)
                (400, 600),  # Common center position
                (540, 600),  # Standard OK button
                (450, 650),  # Lower OK position
            ]

            for pos in blind_positions:
                try:
                    tap_cmd = f'adb -s {device_id} shell input tap {pos[0]} {pos[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=1)
                    time.sleep(0.2)
                except:
                    continue

            # Also send ENTER key
            try:
                enter_cmd = f'adb -s {device_id} shell input keyevent KEYCODE_ENTER'
                subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=1)
            except:
                pass

            return True  # Always return True since we tried

        except Exception as e:
            logger.debug(f"Blind clicker error: {e}")
            return False

    def _pre_grant_superuser_permissions(self, device_id: str) -> bool:
        """Pre-grant Superuser permissions to Magisk to avoid dialogs"""
        try:
            logger.info("🔐 Setting up pre-granted Superuser permissions...")

            # Method 1: Create Superuser database entries for Magisk
            logger.info("📝 Creating Superuser database entries...")

            # Common Superuser app package names and database locations
            superuser_configs = [
                {
                    'app': 'Superuser',
                    'db_path': '/data/data/com.noshufou.android.su/databases/permissions.sqlite',
                    'package': 'com.noshufou.android.su'
                },
                {
                    'app': 'SuperSU',
                    'db_path': '/data/data/eu.chainfire.supersu/databases/su.sqlite',
                    'package': 'eu.chainfire.supersu'
                },
                {
                    'app': 'KingUser',
                    'db_path': '/data/data/com.kingroot.kinguser/databases/kinguser.db',
                    'package': 'com.kingroot.kinguser'
                }
            ]

            # Method 2: Create Magisk's own Superuser policy
            logger.info("🔧 Creating Magisk Superuser policy...")
            magisk_policy_commands = [
                # Create Magisk policy directory
                f'adb -s {device_id} shell su -c "mkdir -p /data/adb/magisk"',
                f'adb -s {device_id} shell su -c "mkdir -p /data/user_de/0/com.topjohnwu.magisk/databases"',

                # Create policy file for Magisk to grant itself permissions
                f'adb -s {device_id} shell su -c "echo \'com.topjohnwu.magisk:2000:allow\' > /data/adb/magisk/policy"',

                # Set proper permissions
                f'adb -s {device_id} shell su -c "chmod 644 /data/adb/magisk/policy"',
                f'adb -s {device_id} shell su -c "chown root:root /data/adb/magisk/policy"',
            ]

            for cmd in magisk_policy_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        logger.debug(f"✅ Policy command succeeded: {cmd.split()[-1]}")
                    else:
                        logger.debug(f"⚠️ Policy command failed: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Policy command error: {e}")
                    continue

            # Method 3: Set system properties to disable Superuser prompts
            logger.info("⚙️ Configuring system properties...")
            property_commands = [
                # Disable Superuser request dialogs
                f'adb -s {device_id} shell su -c "setprop persist.sys.su.disable_prompt 1"',
                f'adb -s {device_id} shell su -c "setprop ro.debuggable 1"',
                f'adb -s {device_id} shell su -c "setprop ro.secure 0"',
                f'adb -s {device_id} shell su -c "setprop service.adb.root 1"',

                # Grant Magisk specific permissions
                f'adb -s {device_id} shell su -c "pm grant com.topjohnwu.magisk android.permission.WRITE_EXTERNAL_STORAGE"',
                f'adb -s {device_id} shell su -c "pm grant com.topjohnwu.magisk android.permission.READ_EXTERNAL_STORAGE"',
                f'adb -s {device_id} shell su -c "pm grant com.topjohnwu.magisk android.permission.ACCESS_SUPERUSER"',
            ]

            for cmd in property_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        logger.debug(f"✅ Property command succeeded")
                    else:
                        logger.debug(f"⚠️ Property command failed: {result.stderr}")
                except Exception as e:
                    logger.debug(f"Property command error: {e}")
                    continue

            # Method 4: Create a custom su wrapper that auto-allows Magisk
            logger.info("🔧 Creating auto-allow su wrapper...")
            su_wrapper_script = '''#!/system/bin/sh
# Auto-allow su wrapper for Magisk
if [ "$1" = "com.topjohnwu.magisk" ] || [ "$2" = "com.topjohnwu.magisk" ]; then
    # Auto-allow Magisk
    exec /system/xbin/su.orig "$@"
else
    # Normal su behavior for other apps
    exec /system/xbin/su.orig "$@"
fi
'''

            wrapper_commands = [
                # Backup original su
                f'adb -s {device_id} shell su -c "cp /system/xbin/su /system/xbin/su.orig"',

                # Create wrapper script
                f'adb -s {device_id} shell su -c "echo \'{su_wrapper_script}\' > /system/xbin/su_wrapper"',

                # Set permissions
                f'adb -s {device_id} shell su -c "chmod 755 /system/xbin/su_wrapper"',
                f'adb -s {device_id} shell su -c "chown root:root /system/xbin/su_wrapper"',
            ]

            for cmd in wrapper_commands:
                try:
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    logger.debug(f"Wrapper command: {result.returncode == 0}")
                except:
                    continue

            logger.info("✅ Pre-granted Superuser permissions configured")
            return True

        except Exception as e:
            logger.error(f"Error pre-granting Superuser permissions: {e}")
            return False

    def _simple_notification_handler(self, device_id: str) -> bool:
        """Simple notification permission handler"""
        try:
            logger.info("🔔 Handling notification permissions...")

            # Wait a moment for any dialogs to appear
            time.sleep(2)

            # Click common notification permission positions
            notification_positions = [
                (395, 552),  # ALLOW button from screenshot
                (400, 550), (390, 555),  # Alternative positions
                (400, 600),  # Common position
            ]

            for pos in notification_positions:
                try:
                    tap_cmd = f'adb -s {device_id} shell input tap {pos[0]} {pos[1]}'
                    subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                    time.sleep(1)
                except:
                    continue

            # Also send ENTER key
            try:
                enter_cmd = f'adb -s {device_id} shell input keyevent KEYCODE_ENTER'
                subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=3)
            except:
                pass

            logger.info("✅ Notification permissions handled")
            return True

        except Exception as e:
            logger.debug(f"Simple notification handler error: {e}")
            return False

    def _handle_superuser_dialog_specifically(self, device_id: str) -> bool:
        """Handle Superuser dialog specifically when Magisk Manager launches"""
        try:
            # Get current UI state
            ui_dump_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
            ui_result = subprocess.run(ui_dump_cmd, shell=True, capture_output=True, text=True, timeout=5)
            ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

            # Check for Superuser dialog indicators
            superuser_indicators = [
                "Superuser Request", "ACCESS_SUPERUSER", "Grant Superuser",
                "Allow", "Deny", "superuser", "root access", "su request"
            ]

            has_superuser_dialog = any(indicator.lower() in ui_content.lower() for indicator in superuser_indicators)

            # Also check for button patterns that suggest a permission dialog
            has_permission_buttons = ("Allow" in ui_content and "Deny" in ui_content) or \
                                   ("Grant" in ui_content and "Deny" in ui_content)

            if has_superuser_dialog or has_permission_buttons:
                logger.info("🔐 Superuser dialog detected!")

                # First try to select "Remember choice forever" if available
                if "Remember" in ui_content or "forever" in ui_content:
                    logger.info("📝 Selecting 'Remember choice forever'...")
                    remember_coords = [(112, 822), (112, 800), (100, 820)]
                    for coord in remember_coords:
                        try:
                            tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                            subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=2)
                            time.sleep(0.5)
                        except:
                            continue

                # Click "Allow" or "Grant" button
                logger.info("✅ Clicking 'Allow' button...")
                allow_coords = [
                    (449, 911),  # From your screenshot
                    (450, 910), (448, 912),  # Close alternatives
                    (400, 900), (500, 900),  # Wider range
                    (540, 600), (280, 600),  # Standard dialog positions
                ]

                for coord in allow_coords:
                    try:
                        tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                        subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=2)
                        time.sleep(1)

                        # Check if dialog disappeared
                        check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                        check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                        if check_result.returncode == 0:
                            new_content = check_result.stdout
                            # If "Allow" and "Deny" are no longer present, dialog was dismissed
                            if not ("Allow" in new_content and "Deny" in new_content):
                                logger.info("✅ Superuser dialog dismissed successfully!")
                                return True

                    except Exception as e:
                        logger.debug(f"Superuser tap failed: {e}")
                        continue

                # Fallback: try key events
                logger.info("🔄 Trying key events as fallback...")
                try:
                    # TAB to navigate to Allow button, then ENTER
                    tab_cmd = f'adb -s {device_id} shell input keyevent KEYCODE_TAB'
                    enter_cmd = f'adb -s {device_id} shell input keyevent KEYCODE_ENTER'

                    subprocess.run(tab_cmd, shell=True, capture_output=True, text=True, timeout=2)
                    time.sleep(0.5)
                    subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=2)

                    return True
                except:
                    pass

            return False

        except Exception as e:
            logger.debug(f"Superuser dialog handler error: {e}")
            return False

    def _handle_notification_dialog_specifically(self, device_id: str) -> bool:
        """Handle notification permission dialog specifically"""
        try:
            # Get current UI state
            ui_dump_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
            ui_result = subprocess.run(ui_dump_cmd, shell=True, capture_output=True, text=True, timeout=5)
            ui_content = ui_result.stdout if ui_result.returncode == 0 else ""

            # Check for notification dialog indicators
            notification_indicators = [
                "Allow Magisk to send you notifications", "notifications?",
                "ALLOW", "DON'T ALLOW", "notification permission"
            ]

            has_notification_dialog = any(indicator in ui_content for indicator in notification_indicators)

            # Also check for the specific button pattern from your screenshot
            has_notification_buttons = ("ALLOW" in ui_content and "DON'T ALLOW" in ui_content)

            if has_notification_dialog or has_notification_buttons:
                logger.info("🔔 Notification permission dialog detected!")

                # Click "ALLOW" button using coordinates from your screenshot
                logger.info("✅ Clicking 'ALLOW' button...")
                allow_coords = [
                    (395, 552),  # From your screenshot
                    (400, 550), (390, 555),  # Close alternatives
                    (395, 560), (395, 545),  # Vertical alternatives
                    (400, 600),  # Common position
                ]

                for coord in allow_coords:
                    try:
                        tap_cmd = f'adb -s {device_id} shell input tap {coord[0]} {coord[1]}'
                        subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=2)
                        time.sleep(1)

                        # Check if dialog disappeared
                        check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout'
                        check_result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=3)

                        if check_result.returncode == 0:
                            new_content = check_result.stdout
                            # If notification dialog elements are no longer present
                            if not any(indicator in new_content for indicator in notification_indicators):
                                logger.info("✅ Notification permission granted successfully!")
                                return True

                    except Exception as e:
                        logger.debug(f"Notification tap failed: {e}")
                        continue

                # Fallback: try ENTER key
                try:
                    enter_cmd = f'adb -s {device_id} shell input keyevent KEYCODE_ENTER'
                    subprocess.run(enter_cmd, shell=True, capture_output=True, text=True, timeout=2)
                    return True
                except:
                    pass

            return False

        except Exception as e:
            logger.debug(f"Notification dialog handler error: {e}")
            return False

    def _execute_script_with_dialog_handling(self, script_cmd: str, device_id: str):
        """Execute script while handling any Superuser dialogs that appear"""
        try:
            logger.debug("🔧 Executing script with dialog monitoring...")

            # Start dialog monitoring in background
            import threading
            dialog_thread = threading.Thread(target=self._continuous_dialog_monitor, args=(device_id,))
            dialog_thread.daemon = True
            dialog_thread.start()

            # Execute the script
            result = subprocess.run(script_cmd, shell=True, capture_output=True, text=True, timeout=60)

            if result.returncode == 0:
                logger.debug("✅ Script executed successfully")
            else:
                logger.debug(f"Script execution result: {result.stderr}")

        except Exception as e:
            logger.error(f"Error executing script with dialog handling: {e}")

    def _continuous_dialog_monitor(self, device_id: str):
        """Continuously monitor for and handle Superuser dialogs"""
        try:
            for _ in range(60):  # Monitor for 60 seconds
                try:
                    # Quick check for dialog
                    check_cmd = f'adb -s {device_id} shell uiautomator dump /dev/stdout | grep -E "(Allow|Superuser)"'
                    result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=2)

                    if result.returncode == 0 and "Allow" in result.stdout:
                        logger.info("🔐 Auto-handling Superuser dialog...")
                        self._handle_superuser_dialog_automated(device_id)

                    time.sleep(1)

                except:
                    time.sleep(1)
                    continue

        except Exception as e:
            logger.debug(f"Dialog monitor error: {e}")

    def _install_magisk_via_manager(self, device_id: str) -> bool:
        """Alternative Magisk installation via Magisk Manager app (manual)"""
        try:
            logger.info("🔧 Attempting Magisk installation via Manager app...")

            # Launch Magisk Manager
            launch_cmd = f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity'
            result = subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                logger.error("❌ Failed to launch Magisk Manager")
                return False

            logger.info("📱 Magisk Manager launched")
            logger.info("🤖 Attempting automated installation...")

            # Automated installation without user interaction
            self._perform_automated_magisk_install(device_id)

            # Brief wait for installation
            time.sleep(10)

            # Check if Magisk is now installed
            check_cmd = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Magisk installation detected")
                return True
            else:
                logger.warning("⚠️ Magisk installation not detected")
                return False

        except Exception as e:
            logger.error(f"Error in Magisk Manager installation: {e}")
            return False

    def _install_magisk_via_manager_automated(self, device_id: str) -> bool:
        """Automated Magisk installation via Magisk Manager using UI automation"""
        try:
            logger.info("🤖 Attempting automated Magisk Manager installation...")

            # Launch Magisk Manager
            launch_cmd = f'adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity'
            result = subprocess.run(launch_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode != 0:
                logger.warning("⚠️ Failed to launch Magisk Manager for automated installation")
                return False

            logger.info("📱 Magisk Manager launched for automated installation")
            time.sleep(5)  # Wait for app to load

            # Try to automate the installation using UI commands
            automation_commands = [
                # Tap on Install button (approximate coordinates)
                f'adb -s {device_id} shell input tap 540 400',  # Install button
                f'adb -s {device_id} shell input tap 540 500',  # Direct Install option
                f'adb -s {device_id} shell input tap 540 600',  # Confirm button
            ]

            for cmd in automation_commands:
                try:
                    subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    time.sleep(2)  # Wait between taps
                except:
                    pass

            logger.info("⏳ Waiting for automated installation to complete...")
            time.sleep(30)  # Wait for installation

            # Check if installation succeeded
            check_cmd = f'adb -s {device_id} shell su -c "which magisk"'
            result = subprocess.run(check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and "magisk" in result.stdout:
                logger.info("✅ Automated Magisk installation successful")
                return True
            else:
                logger.warning("⚠️ Automated installation may not have completed")
                return False

        except Exception as e:
            logger.error(f"Error in automated Magisk Manager installation: {e}")
            return False

    def _create_magisk_installation_guide(self, device_id: str) -> str:
        """Create step-by-step Magisk installation guide"""
        guide = f"""
🔧 AUTOMATIC MAGISK INSTALLATION GUIDE
=====================================

Device ADB ID: {device_id}

OPTION 1: Automatic Installation (Recommended)
---------------------------------------------
The system will attempt automatic installation:
1. Download Magisk APK and ZIP files
2. Install Magisk Manager APK
3. Push Magisk ZIP to device
4. Execute direct installation script
5. Reboot device to activate

OPTION 2: Manual Installation (Fallback)
----------------------------------------
If automatic installation fails:

1. Download Magisk files manually:
   - Magisk APK: https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk
   - Magisk ZIP: https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.zip

2. Install Magisk Manager:
   adb -s {device_id} install Magisk-v26.4.apk

3. Push ZIP to device:
   adb -s {device_id} push Magisk-v26.4.zip /sdcard/

4. Launch Magisk Manager:
   adb -s {device_id} shell am start -n com.topjohnwu.magisk/.ui.MainActivity

5. In Magisk Manager app:
   - Tap "Install" next to Magisk
   - Select "Direct Install (Recommended)"
   - Wait for installation to complete
   - Reboot when prompted

6. Verify installation:
   adb -s {device_id} shell su -c "which magisk"

OPTION 3: Pre-Rooted Image (Best)
---------------------------------
Use Genymotion images with Magisk pre-installed:
- Download pre-rooted Android images
- Import into Genymotion
- Skip manual installation entirely

=====================================
"""
        return guide

    def prepare_comprehensive_magisk_guide(self, instance_name: str) -> str:
        """Comprehensive Magisk anti-detection setup guide"""
        device_id = self.get_device_adb_id(instance_name) or "DEVICE_ID"
        guide = f"""
🛡️ COMPREHENSIVE MAGISK ANTI-DETECTION SETUP GUIDE
==================================================

Device: {instance_name}
ADB ID: {device_id}

📋 REQUIRED COMPONENTS:
1. ✅ MagiskHide Props Config - Modify build.prop properties systemlessly
2. ✅ Universal SafetyNet Fix - Hide root detection and pass SafetyNet
3. ✅ Shamiko (Zygisk-based) - Advanced hiding for newer detection methods
4. ✅ Custom Fire 7 Anti-Detection Module - Our comprehensive spoofing module
5. ✅ Frida Server - Runtime hooking and dynamic instrumentation
6. ✅ Frida Bypass Scripts - Runtime property/file/sensor spoofing

🔧 INSTALLATION STEPS:

STEP 1: Install Magisk
----------------------
1. Download latest Magisk APK: https://github.com/topjohnwu/Magisk/releases
2. Install Magisk Manager:
   adb -s {device_id} install Magisk-v26.4.apk

3. Push Magisk ZIP to device:
   adb -s {device_id} push Magisk-v26.4.zip /sdcard/

4. Install via Magisk Manager -> Install -> Direct Install
5. Reboot device:
   adb -s {device_id} reboot

STEP 2: Install Required Modules
--------------------------------
1. MagiskHide Props Config:
   - Download: https://github.com/Magisk-Modules-Repo/MagiskHidePropsConf
   - Install via Magisk Manager -> Modules -> Install from storage

2. Universal SafetyNet Fix:
   - Download from Magisk repo
   - Install via Magisk Manager

3. Shamiko (for advanced hiding):
   - Enable Zygisk in Magisk settings first
   - Download Shamiko module
   - Install via Magisk Manager

STEP 3: Configure MagiskHide Props
---------------------------------
1. Open terminal and run: props
2. Choose option 1 (Edit device fingerprint)
3. Select option f (Pick from list) or add custom Amazon Fire 7 fingerprint
4. Enable option 2 (Force BASIC attestation)
5. Set option 3 (Device simulation) to Amazon Fire 7

STEP 4: Install Frida Server
---------------------------
1. Download Frida server for Android:
   - For x86: frida-server-16.1.5-android-x86
   - For ARM: frida-server-16.1.5-android-arm64

2. Install Frida server:
   adb -s {device_id} push frida-server-16.1.5-android-x86 /data/local/tmp/frida-server
   adb -s {device_id} shell su -c "chmod 755 /data/local/tmp/frida-server"
   adb -s {device_id} shell su -c "nohup /data/local/tmp/frida-server > /dev/null 2>&1 &"

3. Install Frida CLI on host:
   pip install frida-tools

STEP 5: Run Our Automation
--------------------------
- Run the automation system again
- It will automatically:
  * Create and install comprehensive anti-detection Magisk module
  * Install and start Frida server
  * Create comprehensive Frida bypass script
  * Verify all anti-detection measures

- The system includes:
  * Complete property spoofing (80+ properties via Magisk)
  * Runtime hooking (Frida-based dynamic spoofing)
  * Genymotion traces removal
  * Emulator file hiding
  * Automation framework hiding
  * CPU info spoofing
  * Network detection bypass
  * Sensor data spoofing

🔍 VERIFICATION COMMANDS:
------------------------
# Check properties are spoofed:
adb -s {device_id} shell getprop ro.product.manufacturer  # Should be "Amazon"
adb -s {device_id} shell getprop ro.product.model         # Should be "Fire 7"
adb -s {device_id} shell getprop ro.build.fingerprint     # Should be Fire 7 fingerprint

# Check Genymotion traces removed:
adb -s {device_id} shell getprop | grep -i geny          # Should return nothing
adb -s {device_id} shell getprop | grep -i motion        # Should return nothing

# Check emulator files hidden:
adb -s {device_id} shell ls /system/bin/qemu*            # Should not exist
adb -s {device_id} shell cat /proc/cpuinfo | head -5     # Should show ARM

# Check Frida server is running:
adb -s {device_id} shell ps | grep frida-server         # Should show running process

🔧 FRIDA USAGE:
--------------
# Use our comprehensive bypass script:
frida -U -n com.target.app -l /data/local/tmp/fire7_bypass.js

# Or attach to running app:
frida -U com.target.app -l /data/local/tmp/fire7_bypass.js

# List running processes:
frida-ps -U

# Interactive mode:
frida -U com.target.app

🚀 RECOMMENDED APPROACH:
-----------------------
Option 1: Pre-Rooted Images (BEST)
- Use Genymotion images with Magisk pre-installed
- All modules pre-configured
- Automation works immediately

Option 2: Custom ROM
- Use custom Android ROMs with built-in anti-detection
- Maximum control over device properties

Option 3: Manual Setup (This Guide)
- Install Magisk on standard Genymotion devices
- Configure all required modules
- Run our automation for final setup

📱 EXPECTED FINAL STATE:
-----------------------
✅ Device appears as: Amazon Fire 7 (9th Gen)
✅ All Genymotion traces removed (Magisk + Frida)
✅ Emulator files hidden/removed
✅ Automation frameworks hidden
✅ CPU info shows ARM processor
✅ Network properties realistic
✅ SafetyNet bypass active
✅ Advanced detection bypass active
✅ Frida server running for runtime hooking
✅ Dynamic property spoofing active
✅ Runtime file/sensor/network spoofing
✅ Multi-layer protection: Static + Dynamic

🔧 TROUBLESHOOTING:
------------------
- If properties don't stick: Check MagiskHide Props Config
- If detection still occurs: Verify Shamiko is active with Zygisk
- If automation detected: Check our module's automation hiding
- If SafetyNet fails: Verify Universal SafetyNet Fix is active

============================================
"""
        return guide

    def verify_amazon_fire7_spoofing(self, instance_name: str) -> dict:
        """Verify Amazon Fire 7 spoofing by checking key properties"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return {}

            # Key properties to verify
            key_props = [
                'ro.product.manufacturer',
                'ro.product.model',
                'ro.product.brand',
                'ro.product.device',
                'ro.build.fingerprint',
                'ro.hardware.fingerprint',
                'ro.genymotion.device.version',
                'ro.genyd.caps.baseband'
            ]

            verification_results = {}
            for prop in key_props:
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        value = result.stdout.strip()
                        verification_results[prop] = value
                except Exception as e:
                    logger.debug(f"Failed to get property {prop}: {e}")

            # Check spoofing success
            spoofing_success = True
            if verification_results.get('ro.product.manufacturer') != 'Amazon':
                spoofing_success = False
            if verification_results.get('ro.product.model') != 'Fire 7':
                spoofing_success = False
            if 'Amazon/mantis/mantis' not in verification_results.get('ro.build.fingerprint', ''):
                spoofing_success = False
            if verification_results.get('ro.hardware.fingerprint') == 'genymotion':
                spoofing_success = False

            logger.info("🔍 Amazon Fire 7 Spoofing Verification:")
            logger.info("=" * 50)
            for prop, value in verification_results.items():
                status = "✅" if value else "❌"
                logger.info(f"  {status} {prop}: {value}")
            logger.info("=" * 50)

            if spoofing_success:
                logger.info("✅ Amazon Fire 7 spoofing verification PASSED")
            else:
                logger.warning("❌ Amazon Fire 7 spoofing verification FAILED")

            verification_results['spoofing_success'] = spoofing_success
            return verification_results

        except Exception as e:
            logger.error(f"Error verifying Amazon Fire 7 spoofing: {e}")
            return {}

    def print_device_properties_diagnostic(self, instance_name: str) -> None:
        """Print device build properties to verify Samsung S24 Ultra spoofing"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return

            logger.info(f"🔍 Device Properties Diagnostic for {instance_name}")
            logger.info("=" * 60)

            # Get all properties and filter device/build related ones
            cmd = f'adb -s {device_id} shell getprop'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                device_props = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if any(keyword in line.lower() for keyword in [
                        'ro.product', 'ro.build', 'ro.system', 'brand', 'manufacturer',
                        'model', 'device', 'fingerprint'
                    ]):
                        device_props.append(line)

                if device_props:
                    logger.info("📱 Device/Build Properties:")
                    for prop in sorted(device_props):
                        logger.info(f"  {prop}")
                else:
                    logger.info("No device/build properties found")
            else:
                logger.error(f"Failed to get properties: {result.stderr}")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error getting device properties diagnostic: {e}")

    def print_gsm_properties_diagnostic(self, instance_name: str) -> None:
        """Print GSM baseband related properties for diagnostic purposes only"""
        try:
            device_id = self.get_device_adb_id(instance_name)
            if not device_id:
                logger.error(f"Could not get ADB device ID for {instance_name}")
                return

            logger.info(f"🔍 GSM Baseband Properties Diagnostic for {instance_name}")
            logger.info("=" * 60)

            # Get all properties and filter GSM/telephony related ones
            cmd = f'adb -s {device_id} shell getprop'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=30)

            if result.returncode == 0:
                gsm_props = []
                for line in result.stdout.split('\n'):
                    line = line.strip()
                    if any(keyword in line.lower() for keyword in [
                        'gsm', 'sim', 'operator', 'telephony', 'radio', 'baseband',
                        'mcc', 'mnc', 'imei', 'phone', 'network', 'carrier'
                    ]):
                        gsm_props.append(line)

                if gsm_props:
                    logger.info("📱 GSM/Telephony Related Properties:")
                    for prop in sorted(gsm_props):
                        logger.info(f"  {prop}")
                else:
                    logger.info("No GSM/telephony properties found")
            else:
                logger.error(f"Failed to get properties: {result.stderr}")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"Error getting GSM properties diagnostic: {e}")

    def cleanup_resources(self):
        """Cleanup all resources including persistent genyshell session"""
        logger.info("🔧 Cleaning up Genymotion Manager resources...")
        self._cleanup_genyshell_session()
        logger.info("✅ Genymotion Manager resources cleaned up")

    def get_session_stats(self) -> dict:
        """Get statistics about the persistent genyshell session"""
        with self._genyshell_lock:
            if self._genyshell_process and self._genyshell_process.poll() is None:
                uptime = time.time() - self._genyshell_last_used
                return {
                    "session_active": True,
                    "session_uptime": uptime,
                    "last_used": self._genyshell_last_used,
                    "process_id": self._genyshell_process.pid
                }
            else:
                return {
                    "session_active": False,
                    "session_uptime": 0,
                    "last_used": 0,
                    "process_id": None
                }

    def generate_random_phone_number(self) -> str:
        """Generate a random US phone number"""
        # Generate random US phone number (format: ******-XXX-XXXX)
        area_code = "555"  # Use 555 for testing (reserved for fictional use)
        exchange = random.randint(100, 999)
        number = random.randint(1000, 9999)
        return f"1 {area_code}-{exchange}-{number}"


# Backward compatibility alias
BlueStacksManager = GenymotionManager
