#!/usr/bin/env python3
"""
Gmail Account <PERSON><PERSON> with <PERSON><PERSON> Spoofing
Automates the creation of Gmail accounts using spoofed Android devices
"""

import subprocess
import time
import json
import random
import string
import os
import math
from datetime import datetime, timedelta
from typing import Dict, Optional, Tu<PERSON>, List
from loguru import logger
from dataclasses import dataclass

from genymotion_manager import GenymotionManager
from database import DatabaseManager


@dataclass
class PersonalInfo:
    """Generated personal information for account creation"""
    first_name: str
    last_name: str
    username: str
    password: str
    birth_date: str
    phone_number: str
    recovery_email: str

    def to_dict(self) -> Dict:
        return {
            'first_name': self.first_name,
            'last_name': self.last_name,
            'username': self.username,
            'password': self.password,
            'birth_date': self.birth_date,
            'phone_number': self.phone_number,
            'recovery_email': self.recovery_email
        }


class HumanBehaviorSimulator:
    """Simulates realistic human behavior patterns to avoid detection"""

    def __init__(self):
        self.typing_speed_wpm = random.randint(25, 45)  # Words per minute
        self.error_rate = random.uniform(0.02, 0.08)    # 2-8% error rate
        self.pause_probability = 0.15                    # 15% chance to pause while typing
        self.mouse_movement_variance = 5                 # Pixel variance for mouse movements

    def human_delay(self, min_seconds: float = 0.5, max_seconds: float = 2.0) -> float:
        """Generate human-like delay with natural variation"""
        # Use normal distribution for more realistic timing
        mean = (min_seconds + max_seconds) / 2
        std_dev = (max_seconds - min_seconds) / 6  # 99.7% within range

        delay = random.normalvariate(mean, std_dev)
        # Ensure delay is within bounds
        delay = max(min_seconds, min(max_seconds, delay))

        time.sleep(delay)
        return delay

    def typing_delay(self, text: str) -> float:
        """Calculate realistic typing delay based on text length and typing speed"""
        # Average word length is 5 characters
        words = len(text) / 5
        base_time = (words / self.typing_speed_wpm) * 60  # Convert to seconds

        # Add variance for human-like typing
        variance = random.uniform(0.8, 1.3)
        typing_time = base_time * variance

        # Add pauses for longer text
        if len(text) > 10:
            pause_count = random.randint(0, len(text) // 15)
            pause_time = pause_count * random.uniform(0.3, 1.2)
            typing_time += pause_time

        return typing_time

    def simulate_typing_errors(self, text: str) -> List[str]:
        """Simulate realistic typing with occasional errors and corrections"""
        if random.random() > self.error_rate or len(text) < 3:
            return [text]  # No errors

        # Simulate typing with errors
        typing_sequence = []
        error_position = random.randint(1, len(text) - 1)

        # Type up to error position
        partial_text = text[:error_position]
        typing_sequence.append(partial_text)

        # Add wrong character
        wrong_char = random.choice('abcdefghijklmnopqrstuvwxyz')
        wrong_text = partial_text + wrong_char
        typing_sequence.append(wrong_text)

        # Pause (human realizes mistake)
        time.sleep(random.uniform(0.2, 0.8))

        # Backspace and correct
        typing_sequence.append(partial_text)  # After backspace
        typing_sequence.append(text)  # Final correct text

        return typing_sequence

    def add_mouse_jitter(self, x: int, y: int) -> Tuple[int, int]:
        """Add slight mouse movement variance to simulate human imprecision"""
        jitter_x = random.randint(-self.mouse_movement_variance, self.mouse_movement_variance)
        jitter_y = random.randint(-self.mouse_movement_variance, self.mouse_movement_variance)

        return (x + jitter_x, y + jitter_y)

    def reading_delay(self, text_length: int) -> float:
        """Simulate time needed to read text (average reading speed: 200-300 WPM)"""
        words = text_length / 5  # Average word length
        reading_speed_wpm = random.randint(200, 300)
        reading_time = (words / reading_speed_wpm) * 60

        # Minimum reading time for short text
        reading_time = max(0.5, reading_time)

        time.sleep(reading_time)
        return reading_time

    def decision_delay(self) -> float:
        """Simulate human decision-making time"""
        delay = random.uniform(1.0, 3.5)
        time.sleep(delay)
        return delay

    def scroll_behavior(self, device_id: str):
        """Simulate natural scrolling behavior"""
        try:
            # Random scroll direction and distance
            if random.choice([True, False]):
                # Scroll down
                start_y = random.randint(400, 600)
                end_y = start_y - random.randint(100, 300)
            else:
                # Scroll up
                start_y = random.randint(200, 400)
                end_y = start_y + random.randint(100, 300)

            x = random.randint(200, 400)  # Center-ish area

            # Simulate scroll with human-like speed
            scroll_cmd = f'adb -s {device_id} shell "input swipe {x} {start_y} {x} {end_y} {random.randint(300, 800)}"'
            subprocess.run(scroll_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Brief pause after scrolling
            self.human_delay(0.3, 1.0)

        except Exception as e:
            logger.debug(f"Scroll behavior simulation failed: {e}")

    def random_page_interaction(self, device_id: str):
        """Simulate random page interactions like a human browsing"""
        try:
            interactions = [
                self._simulate_tap_and_hold,
                self._simulate_double_tap,
                self.scroll_behavior,
                self._simulate_zoom_gesture
            ]

            # 30% chance to perform random interaction
            if random.random() < 0.3:
                interaction = random.choice(interactions)
                interaction(device_id)

        except Exception as e:
            logger.debug(f"Random interaction simulation failed: {e}")

    def _simulate_tap_and_hold(self, device_id: str):
        """Simulate tap and hold gesture"""
        x = random.randint(100, 400)
        y = random.randint(200, 600)
        duration = random.randint(500, 1500)

        cmd = f'adb -s {device_id} shell "input swipe {x} {y} {x} {y} {duration}"'
        subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)

    def _simulate_double_tap(self, device_id: str):
        """Simulate double tap gesture"""
        x = random.randint(100, 400)
        y = random.randint(200, 600)

        # First tap
        cmd1 = f'adb -s {device_id} shell "input tap {x} {y}"'
        subprocess.run(cmd1, shell=True, capture_output=True, text=True, timeout=2)

        # Brief delay
        time.sleep(random.uniform(0.1, 0.3))

        # Second tap with slight position variance
        x2, y2 = self.add_mouse_jitter(x, y)
        cmd2 = f'adb -s {device_id} shell "input tap {x2} {y2}"'
        subprocess.run(cmd2, shell=True, capture_output=True, text=True, timeout=2)

    def _simulate_zoom_gesture(self, device_id: str):
        """Simulate pinch zoom gesture"""
        center_x = random.randint(200, 300)
        center_y = random.randint(300, 500)

        # Pinch out (zoom in)
        if random.choice([True, False]):
            x1_start, y1_start = center_x - 50, center_y - 50
            x1_end, y1_end = center_x - 100, center_y - 100
            x2_start, y2_start = center_x + 50, center_y + 50
            x2_end, y2_end = center_x + 100, center_y + 100
        else:
            # Pinch in (zoom out)
            x1_start, y1_start = center_x - 100, center_y - 100
            x1_end, y1_end = center_x - 50, center_y - 50
            x2_start, y2_start = center_x + 100, center_y + 100
            x2_end, y2_end = center_x + 50, center_y + 50

        duration = random.randint(300, 800)

        # Simulate two-finger gesture (simplified)
        cmd = f'adb -s {device_id} shell "input swipe {x1_start} {y1_start} {x1_end} {y1_end} {duration}"'
        subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=3)


class GmailAccountCreator:
    """Automated Gmail account creation with device spoofing and human behavior simulation"""

    def __init__(self):
        self.genymotion = GenymotionManager()
        self.db = DatabaseManager()
        self.accounts_file = "created_accounts.txt"
        self.faker_script = "generate_fake_data.js"
        self.human_behavior = HumanBehaviorSimulator()

        # Create accounts file if it doesn't exist
        if not os.path.exists(self.accounts_file):
            with open(self.accounts_file, 'w') as f:
                f.write("# Created Gmail Accounts\n")
                f.write("# Format: email:password:first_name:last_name:birth_date:created_at\n\n")

    def generate_personal_info(self) -> PersonalInfo:
        """Generate realistic personal information using Faker.js"""
        try:
            logger.info("🎭 Generating realistic personal information...")

            # Run Faker.js script to generate data
            result = subprocess.run(
                ['node', self.faker_script],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode != 0:
                logger.error(f"❌ Faker.js script failed: {result.stderr}")
                return self._generate_fallback_info()

            # Parse JSON output from Faker.js
            fake_data = json.loads(result.stdout.strip())

            # Create PersonalInfo object
            info = PersonalInfo(
                first_name=fake_data['firstName'],
                last_name=fake_data['lastName'],
                username=fake_data['username'],
                password=fake_data['password'],
                birth_date=fake_data['birthDate'],
                phone_number=fake_data['phoneNumber'],
                recovery_email=fake_data['recoveryEmail']
            )

            logger.info(f"✅ Generated info for: {info.first_name} {info.last_name}")
            logger.info(f"   Username: {info.username}")
            logger.info(f"   Birth Date: {info.birth_date}")

            return info

        except Exception as e:
            logger.error(f"❌ Error generating personal info: {e}")
            return self._generate_fallback_info()

    def _generate_fallback_info(self) -> PersonalInfo:
        """Generate basic personal info as fallback"""
        logger.info("🔄 Using fallback personal info generation...")

        first_names = ['John', 'Jane', 'Mike', 'Sarah', 'David', 'Emma', 'Chris', 'Lisa']
        last_names = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis']

        first_name = random.choice(first_names)
        last_name = random.choice(last_names)

        # Generate username
        username = f"{first_name.lower()}{last_name.lower()}{random.randint(100, 999)}"

        # Generate password
        password = ''.join(random.choices(string.ascii_letters + string.digits, k=12))

        # Generate birth date (18-65 years old)
        birth_year = random.randint(1959, 2006)
        birth_month = random.randint(1, 12)
        birth_day = random.randint(1, 28)
        birth_date = f"{birth_month:02d}/{birth_day:02d}/{birth_year}"

        # Generate phone number
        phone_number = f"+1{random.randint(200, 999)}{random.randint(200, 999)}{random.randint(1000, 9999)}"

        # Generate recovery email
        recovery_email = f"{username}<EMAIL>"

        return PersonalInfo(
            first_name=first_name,
            last_name=last_name,
            username=username,
            password=password,
            birth_date=birth_date,
            phone_number=phone_number,
            recovery_email=recovery_email
        )

    def create_spoofed_device(self) -> Tuple[Optional[str], Optional[str]]:
        """Create and configure a spoofed Android device"""
        try:
            logger.info("📱 Creating spoofed Android device...")

            # Generate random device name
            device_name = f"MyPhone_{random.randint(100000, 999999)}"

            # Create device with Android 14.0 (only downloaded image)
            logger.info("📱 Creating device with Android 14.0...")
            success = self.genymotion.create_new_instance(device_name, android_version="14")
            if not success:
                logger.error("❌ Failed to create device")
                return None, None

            # Start device
            start_success = self.genymotion.start_instance(device_name)
            if not start_success:
                logger.error("❌ Failed to start device")
                return None, None

            # Get the actual device ID
            device_id = self.genymotion.actual_device_id
            if not device_id:
                logger.error("❌ Failed to get device ID")
                return None, None

            logger.info(f"✅ Device started successfully with ID: {device_id}")

            # Wait for device to be ready (HTC One proven method)
            logger.info("⏳ Waiting for device to be fully ready...")
            if not self._wait_for_android_boot_completion(device_id):
                logger.error("❌ Device boot completion timeout")
                return None, None

            # Apply spoofing
            logger.info("🛡️ Applying device spoofing...")
            spoofing_success = self.genymotion.customize_device_identifiers(device_name)

            if spoofing_success:
                logger.info("✅ Device spoofing applied successfully")
            else:
                logger.warning("⚠️ Device spoofing had issues but continuing...")

            return device_name, device_id

        except Exception as e:
            logger.error(f"❌ Error creating spoofed device: {e}")
            return None, None

    def navigate_to_gmail_signup(self, device_id: str) -> bool:
        """Navigate to Gmail signup page using default Android browser"""
        try:
            logger.info("🌐 Navigating to Gmail signup with human-like behavior...")

            # Human-like delay before starting
            logger.info("⏳ Simulating human startup delay...")
            self.human_behavior.human_delay(2.0, 4.0)

            # Use default Android browser with intent
            logger.info("📱 Opening default Android browser...")

            # Use proven HTC One browser opening method
            logger.info("🔗 Navigating to google.com using proven method...")

            # Method 1: Try default browser intent (HTC One proven method)
            logger.info("🔍 Attempting to launch browser with default intent...")
            result = subprocess.run([
                'adb', '-s', device_id, 'shell', 'am', 'start',
                '-a', 'android.intent.action.VIEW',
                '-d', 'https://www.google.com'
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                logger.info("✅ Browser launched successfully with default intent")
                logger.debug(f"ADB output: {result.stdout}")
                if result.stderr:
                    logger.debug(f"ADB stderr: {result.stderr}")
            else:
                logger.warning(f"⚠️ Default intent failed: {result.stderr}")

                # Method 2: Try specific browser packages (HTC One fallback method)
                browser_packages = [
                    'com.android.browser',
                    'com.google.android.browser',
                    'com.android.chrome'
                ]

                browser_launched = False
                for package in browser_packages:
                    logger.info(f"🔍 Trying browser package: {package}")
                    try:
                        result = subprocess.run([
                            'adb', '-s', device_id, 'shell', 'am', 'start',
                            '-a', 'android.intent.action.VIEW',
                            '-d', 'https://www.google.com',
                            package
                        ], capture_output=True, text=True, timeout=15)

                        if result.returncode == 0:
                            logger.info(f"✅ Browser launched with {package}")
                            logger.debug(f"ADB output: {result.stdout}")
                            if result.stderr:
                                logger.debug(f"ADB stderr: {result.stderr}")
                            browser_launched = True
                            break
                        else:
                            logger.debug(f"Failed to launch {package}: returncode {result.returncode}")
                            if result.stderr:
                                logger.debug(f"Error: {result.stderr}")

                    except Exception as e:
                        logger.debug(f"Failed to launch {package}: {e}")
                        continue

                if not browser_launched:
                    logger.error("❌ Could not launch any browser")
                    return False

            # Wait for page to load (HTC One proven timing)
            logger.info("⏳ Waiting for page to load...")
            time.sleep(8)  # HTC One proven timing

            # Additional human-like delay
            self.human_behavior.human_delay(2.0, 4.0)

            # Simulate human reading the page
            logger.info("👁️ Simulating page reading time...")
            self.human_behavior.reading_delay(50)  # Simulate reading Google homepage

            # Random page interaction (like a human browsing)
            logger.info("🖱️ Simulating human browsing behavior...")
            self.human_behavior.random_page_interaction(device_id)

            # Take screenshot for verification
            self._take_screenshot(device_id, "01_google_homepage")

            logger.info("✅ Browser navigation completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error navigating to Gmail: {e}")
            return False

    def _verify_browser_opened(self, device_id: str) -> bool:
        """Verify that a browser is open and showing content"""
        try:
            # Check current activity to see if a browser is running
            activity_cmd = f'adb -s {device_id} shell "dumpsys window windows | grep -E \'mCurrentFocus|mFocusedApp\'"'
            result = subprocess.run(activity_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                output = result.stdout.lower()
                browser_indicators = ['browser', 'chrome', 'webview', 'google']
                return any(indicator in output for indicator in browser_indicators)

            return False

        except Exception as e:
            logger.debug(f"Browser verification failed: {e}")
            return False

    def _wait_for_android_boot_completion(self, device_id: str) -> bool:
        """Wait for Android boot completion (HTC One proven method)"""
        try:
            logger.info("🔍 Checking Android boot completion...")
            max_wait = 120  # 2 minutes
            wait_interval = 5

            for i in range(0, max_wait, wait_interval):
                try:
                    # Check boot completion property
                    boot_result = subprocess.run([
                        'adb', '-s', device_id, 'shell', 'getprop', 'sys.boot_completed'
                    ], capture_output=True, text=True, timeout=10)

                    if boot_result.returncode == 0 and boot_result.stdout.strip() == '1':
                        logger.info("✅ Android boot completed successfully")

                        # Additional check: ensure package manager is ready
                        pm_result = subprocess.run([
                            'adb', '-s', device_id, 'shell', 'pm', 'list', 'packages', '|', 'head', '-1'
                        ], capture_output=True, text=True, timeout=10)

                        if pm_result.returncode == 0:
                            logger.info("✅ Package manager is ready")
                            return True
                        else:
                            logger.debug("Package manager not ready yet...")
                    else:
                        logger.debug(f"Boot not completed yet (attempt {i//wait_interval + 1})")

                    time.sleep(wait_interval)

                except Exception as e:
                    logger.debug(f"Boot check failed: {e}")
                    time.sleep(wait_interval)

            logger.error("❌ Android boot completion timeout")
            return False

        except Exception as e:
            logger.error(f"❌ Error checking boot completion: {e}")
            return False

    def _verify_android_system_responsive(self, device_id: str) -> bool:
        """Verify Android system is responsive (HTC One proven method)"""
        try:
            # Test basic ADB connection
            result = subprocess.run([
                'adb', '-s', device_id, 'shell', 'echo', 'test'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode != 0 or result.stdout.strip() != 'test':
                logger.debug("Basic ADB test failed")
                return False

            # Test activity manager
            am_result = subprocess.run([
                'adb', '-s', device_id, 'shell', 'am', 'get-current-user'
            ], capture_output=True, text=True, timeout=10)

            if am_result.returncode != 0:
                logger.debug("Activity manager test failed")
                return False

            # Test input system
            input_result = subprocess.run([
                'adb', '-s', device_id, 'shell', 'input', 'keyevent', 'KEYCODE_UNKNOWN'
            ], capture_output=True, text=True, timeout=10)

            # Input command should succeed (even with unknown keycode)
            if input_result.returncode != 0:
                logger.debug("Input system test failed")
                return False

            logger.info("✅ Android system is responsive")
            return True

        except Exception as e:
            logger.debug(f"Android system responsiveness check failed: {e}")
            return False

    def _unlock_screen_if_needed(self, device_id: str) -> bool:
        """Unlock screen if lock screen is present (Android 14.0 requirement)"""
        try:
            logger.info("🔓 Checking if screen unlock is needed...")

            # Check if screen is locked by looking for lock screen indicators
            screen_check_cmd = f'adb -s {device_id} shell "dumpsys window | grep -E \'mDreamingLockscreen|mShowingLockscreen|KeyguardController\'"'
            result = subprocess.run(screen_check_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Also check current activity for lock screen
            activity_cmd = f'adb -s {device_id} shell "dumpsys activity activities | grep -E \'mResumedActivity|mFocusedActivity\'"'
            activity_result = subprocess.run(activity_cmd, shell=True, capture_output=True, text=True, timeout=10)

            screen_locked = False
            if result.returncode == 0:
                output = result.stdout.lower()
                if 'true' in output or 'lockscreen' in output:
                    screen_locked = True

            if activity_result.returncode == 0:
                activity_output = activity_result.stdout.lower()
                if 'keyguard' in activity_output or 'lockscreen' in activity_output:
                    screen_locked = True

            if screen_locked:
                logger.info("🔒 Lock screen detected, unlocking...")
                return self._perform_screen_unlock(device_id)
            else:
                logger.info("✅ Screen already unlocked")
                return True

        except Exception as e:
            logger.warning(f"⚠️ Screen lock check failed: {e}")
            # Try to unlock anyway as a precaution
            return self._perform_screen_unlock(device_id)

    def _perform_screen_unlock(self, device_id: str) -> bool:
        """Perform screen unlock gestures for Android 14.0"""
        try:
            logger.info("🔓 Performing screen unlock...")

            # Method 1: Wake up the screen first
            logger.debug("📱 Waking up screen...")
            wake_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_WAKEUP"'
            subprocess.run(wake_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Brief delay for screen to wake up
            time.sleep(1)

            # Method 2: Swipe up from bottom to unlock (Android 14.0 gesture)
            logger.debug("👆 Performing swipe up gesture...")

            # Get screen dimensions for proper swipe coordinates
            screen_size = self._get_screen_dimensions(device_id)
            if screen_size:
                width, height = screen_size
                # Swipe from bottom center upward
                start_x = width // 2
                start_y = int(height * 0.9)  # 90% down from top
                end_x = width // 2
                end_y = int(height * 0.3)    # 30% down from top
            else:
                # Fallback coordinates for common screen sizes
                start_x, start_y = 400, 800
                end_x, end_y = 400, 300

            # Perform swipe up gesture
            swipe_cmd = f'adb -s {device_id} shell "input swipe {start_x} {start_y} {end_x} {end_y} 500"'
            subprocess.run(swipe_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Wait for unlock animation
            time.sleep(2)

            # Method 3: Alternative unlock methods if swipe doesn't work
            logger.debug("🔑 Trying alternative unlock methods...")

            # Try menu key (some devices)
            menu_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_MENU"'
            subprocess.run(menu_cmd, shell=True, capture_output=True, text=True, timeout=5)
            time.sleep(1)

            # Try home key
            home_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_HOME"'
            subprocess.run(home_cmd, shell=True, capture_output=True, text=True, timeout=5)
            time.sleep(1)

            # Method 4: Dismiss any additional dialogs
            logger.debug("❌ Dismissing any setup dialogs...")

            # Try to dismiss setup wizards or welcome screens
            dismiss_coords = [
                (400, 700),  # Common "Skip" or "OK" button position
                (600, 700),  # Alternative position
                (200, 700),  # Another position
            ]

            for x, y in dismiss_coords:
                tap_cmd = f'adb -s {device_id} shell "input tap {x} {y}"'
                subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=3)
                time.sleep(0.5)

            # Final verification
            time.sleep(2)
            logger.info("✅ Screen unlock sequence completed")

            # Take screenshot to verify unlock
            self._take_screenshot(device_id, "00_screen_unlocked")

            return True

        except Exception as e:
            logger.error(f"❌ Screen unlock failed: {e}")
            return False

    def _take_screenshot(self, device_id: str, name: str):
        """Take screenshot for debugging"""
        try:
            screenshot_dir = "screenshots"
            os.makedirs(screenshot_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            screenshot_path = f"{screenshot_dir}/{name}_{timestamp}.png"

            cmd = f'adb -s {device_id} exec-out screencap -p > {screenshot_path}'
            subprocess.run(cmd, shell=True, timeout=10)

            logger.debug(f"📸 Screenshot saved: {screenshot_path}")

        except Exception as e:
            logger.debug(f"⚠️ Screenshot failed: {e}")

    def save_account_to_file(self, info: PersonalInfo, email: str):
        """Save created account to text file"""
        try:
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            account_line = f"{email}:{info.password}:{info.first_name}:{info.last_name}:{info.birth_date}:{timestamp}\n"

            with open(self.accounts_file, 'a') as f:
                f.write(account_line)

            logger.info(f"💾 Account saved to {self.accounts_file}")

        except Exception as e:
            logger.error(f"❌ Error saving account to file: {e}")

    def save_account_to_database(self, info: PersonalInfo, email: str, device_name: str):
        """Save created account to database"""
        try:
            account_data = {
                'email': email,
                'password': info.password,
                'first_name': info.first_name,
                'last_name': info.last_name,
                'birth_date': info.birth_date,
                'phone_number': info.phone_number,
                'recovery_email': info.recovery_email,
                'device_name': device_name,
                'created_at': datetime.now().isoformat(),
                'status': 'created'
            }

            # Save to database (you'll need to implement this in your Database class)
            # self.db.save_gmail_account(account_data)

            logger.info("💾 Account saved to database")

        except Exception as e:
            logger.error(f"❌ Error saving account to database: {e}")

    def click_sign_in_button(self, device_id: str) -> bool:
        """Click the Sign In button in top right corner of Google homepage"""
        try:
            logger.info("🔘 Looking for Sign In button...")

            # Wait for page to load completely
            time.sleep(5)

            # First, try to find Sign In button using UI automator
            if self._find_and_click_signin_with_ui(device_id):
                logger.info("✅ Found and clicked Sign In using UI detection")
                return True

            # Fallback: Try coordinate-based clicking for common positions
            logger.info("🔄 Trying coordinate-based Sign In detection...")

            # Get screen dimensions for better coordinate calculation
            screen_size = self._get_screen_dimensions(device_id)

            if screen_size:
                width, height = screen_size
                # Calculate relative positions based on screen size
                sign_in_positions = [
                    (int(width * 0.85), int(height * 0.08)),  # Top right (85%, 8%)
                    (int(width * 0.90), int(height * 0.12)),  # Alternative top right
                    (int(width * 0.80), int(height * 0.15)),  # Slightly lower right
                ]
            else:
                # Fallback to fixed coordinates
                sign_in_positions = [
                    (350, 80),   # Top right area
                    (400, 100),  # Alternative position
                    (300, 120),  # Another position
                ]

            for i, (x, y) in enumerate(sign_in_positions):
                logger.info(f"🔘 Trying sign-in position {i+1}: ({x}, {y})")
                tap_cmd = f'adb -s {device_id} shell "input tap {x} {y}"'
                subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=5)
                time.sleep(3)

                # Take screenshot to verify
                self._take_screenshot(device_id, f"02_signin_attempt_{i+1}")

                # Check if we're on sign-in page
                if self._check_for_email_input(device_id):
                    logger.info("✅ Successfully navigated to sign-in page")
                    return True

            # If all methods fail, try direct navigation to signup
            logger.info("🔄 Trying direct navigation to Gmail signup...")
            navigate_cmd = f'adb -s {device_id} shell "am start -a android.intent.action.VIEW -d https://accounts.google.com/signup"'
            subprocess.run(navigate_cmd, shell=True, capture_output=True, text=True, timeout=10)
            time.sleep(5)

            self._take_screenshot(device_id, "02_direct_signup")
            return True

        except Exception as e:
            logger.error(f"❌ Error clicking sign in: {e}")
            return False

    def _find_and_click_signin_with_ui(self, device_id: str) -> bool:
        """Try to find and click Sign In button using UI automator"""
        try:
            # Dump UI hierarchy
            dump_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml"'
            subprocess.run(dump_cmd, shell=True, capture_output=True, text=True, timeout=10)

            # Get UI content
            get_ui_cmd = f'adb -s {device_id} shell "cat /sdcard/ui_dump.xml"'
            result = subprocess.run(get_ui_cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()

                # Look for sign in related text
                signin_keywords = ['sign in', 'signin', 'log in', 'login', 'account']

                for keyword in signin_keywords:
                    if keyword in ui_content:
                        logger.info(f"🔍 Found '{keyword}' in UI")
                        # Try to click using UI automator
                        click_cmd = f'adb -s {device_id} shell "uiautomator runtest uiautomator-stub.jar -c com.github.uiautomatorstub.Stub -e class com.github.uiautomatorstub.Stub -e method testUIAutomatorStub"'
                        # This is a simplified approach - in practice, you'd need more sophisticated UI element detection
                        return False  # For now, return False to use coordinate method

            return False

        except Exception as e:
            logger.debug(f"UI-based sign in detection failed: {e}")
            return False

    def _get_screen_dimensions(self, device_id: str) -> tuple:
        """Get screen dimensions of the device"""
        try:
            size_cmd = f'adb -s {device_id} shell "wm size"'
            result = subprocess.run(size_cmd, shell=True, capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                # Parse output like "Physical size: 1080x1920"
                output = result.stdout.strip()
                if 'x' in output:
                    dimensions = output.split(':')[-1].strip().split('x')
                    if len(dimensions) == 2:
                        width = int(dimensions[0])
                        height = int(dimensions[1])
                        logger.debug(f"Screen dimensions: {width}x{height}")
                        return (width, height)

            return None

        except Exception as e:
            logger.debug(f"Failed to get screen dimensions: {e}")
            return None

    def _check_for_email_input(self, device_id: str) -> bool:
        """Check if email input field is present"""
        try:
            # Use UI automator to check for email input
            cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                email_indicators = ['email', 'username', 'create account', 'sign up']
                return any(indicator in ui_content for indicator in email_indicators)

            return False

        except Exception as e:
            logger.debug(f"UI check failed: {e}")
            return False

    def create_gmail_account(self, device_id: str, info: PersonalInfo) -> Optional[str]:
        """Automate Gmail account creation process"""
        try:
            logger.info("📝 Starting Gmail account creation process...")

            # Step 1: Look for "Create account" option
            logger.info("🔍 Looking for 'Create account' option...")
            time.sleep(3)

            # Try to find and click "Create account"
            create_account_coords = [
                (400, 600),  # Common position for create account
                (200, 500),  # Alternative position
                (300, 550),  # Another common position
            ]

            for i, (x, y) in enumerate(create_account_coords):
                logger.info(f"🔘 Trying create account position {i+1}: ({x}, {y})")
                tap_cmd = f'adb -s {device_id} shell "input tap {x} {y}"'
                subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=5)
                time.sleep(2)

                self._take_screenshot(device_id, f"03_create_account_attempt_{i+1}")

                # Check if we're on the account creation form
                if self._check_for_account_form(device_id):
                    break

            # Step 2: Fill in first name
            logger.info(f"✏️ Entering first name: {info.first_name}")
            self._fill_text_field(device_id, info.first_name, "first_name")
            time.sleep(1)

            # Step 3: Fill in last name
            logger.info(f"✏️ Entering last name: {info.last_name}")
            self._fill_text_field(device_id, info.last_name, "last_name")
            time.sleep(1)

            # Step 4: Fill in username
            logger.info(f"✏️ Entering username: {info.username}")
            self._fill_text_field(device_id, info.username, "username")
            time.sleep(1)

            # Step 5: Fill in password
            logger.info("✏️ Entering password...")
            self._fill_text_field(device_id, info.password, "password")
            time.sleep(1)

            # Step 6: Confirm password
            logger.info("✏️ Confirming password...")
            self._fill_text_field(device_id, info.password, "confirm_password")
            time.sleep(1)

            # Take screenshot of filled form
            self._take_screenshot(device_id, "04_form_filled")

            # Step 7: Click Next/Continue
            logger.info("▶️ Clicking Next/Continue...")
            self._click_next_button(device_id)
            time.sleep(5)

            # Step 8: Handle phone verification if required
            if self._check_for_phone_verification(device_id):
                logger.info("📱 Phone verification required...")
                self._handle_phone_verification(device_id, info.phone_number)

            # Step 9: Handle additional steps (birth date, etc.)
            self._handle_additional_info(device_id, info)

            # Step 10: Complete account creation
            final_email = f"{info.username}@gmail.com"
            logger.info(f"✅ Account creation completed: {final_email}")

            return final_email

        except Exception as e:
            logger.error(f"❌ Error creating Gmail account: {e}")
            return None

    def _check_for_account_form(self, device_id: str) -> bool:
        """Check if we're on the account creation form"""
        try:
            cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                form_indicators = ['first name', 'last name', 'choose username', 'password']
                return any(indicator in ui_content for indicator in form_indicators)

            return False

        except Exception as e:
            logger.debug(f"Form check failed: {e}")
            return False

    def _fill_text_field(self, device_id: str, text: str, field_type: str):
        """Fill a text field with human-like typing behavior"""
        try:
            logger.info(f"✏️ Typing {field_type} with human behavior...")

            # Human-like delay before starting to type
            self.human_behavior.human_delay(0.5, 1.5)

            # Clear any existing text first
            clear_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_CTRL_A && input keyevent KEYCODE_DEL"'
            subprocess.run(clear_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Brief pause after clearing
            self.human_behavior.human_delay(0.3, 0.8)

            # Simulate typing with potential errors and corrections
            typing_sequence = self.human_behavior.simulate_typing_errors(text)

            for i, text_to_type in enumerate(typing_sequence):
                # Calculate typing delay for this segment
                typing_delay = self.human_behavior.typing_delay(text_to_type)

                # Escape special characters for shell
                escaped_text = text_to_type.replace('"', '\\"').replace("'", "\\'")

                if i > 0:  # Not the first entry, so we're correcting
                    # Clear previous text
                    clear_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_CTRL_A && input keyevent KEYCODE_DEL"'
                    subprocess.run(clear_cmd, shell=True, capture_output=True, text=True, timeout=5)

                    # Brief pause before retyping
                    self.human_behavior.human_delay(0.2, 0.5)

                # Type the text
                type_cmd = f'adb -s {device_id} shell "input text \\"{escaped_text}\\""'
                subprocess.run(type_cmd, shell=True, capture_output=True, text=True, timeout=10)

                # Wait for typing to complete (simulated)
                time.sleep(min(typing_delay, 3.0))  # Cap at 3 seconds for very long text

                # Random pause during typing (simulate thinking)
                if len(text_to_type) > 5 and random.random() < self.human_behavior.pause_probability:
                    logger.debug("💭 Simulating thinking pause...")
                    self.human_behavior.human_delay(0.5, 2.0)

            # Human-like delay before moving to next field
            self.human_behavior.human_delay(0.5, 1.2)

            # Press Tab to move to next field
            tab_cmd = f'adb -s {device_id} shell "input keyevent KEYCODE_TAB"'
            subprocess.run(tab_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Brief pause after tab
            self.human_behavior.human_delay(0.3, 0.8)

        except Exception as e:
            logger.error(f"❌ Error filling {field_type} field: {e}")

    def _click_next_button(self, device_id: str):
        """Click Next/Continue button with human-like behavior"""
        try:
            logger.info("🔘 Looking for Next/Continue button with human behavior...")

            # Human-like delay before looking for button
            self.human_behavior.decision_delay()

            # Focus on top right corner where Sign In button is located
            logger.info("🔍 Looking for Sign In button in top right corner...")

            # Get screen dimensions for accurate positioning
            screen_size = self._get_screen_dimensions(device_id)

            if screen_size:
                width, height = screen_size
                # Calculate top right area coordinates
                sign_in_coords = [
                    (int(width * 0.85), int(height * 0.08)),  # Top right corner (85%, 8%)
                    (int(width * 0.90), int(height * 0.12)),  # Alternative top right
                    (int(width * 0.80), int(height * 0.15)),  # Slightly lower right
                    (int(width * 0.88), int(height * 0.10)),  # Center of top right area
                ]
            else:
                # Fallback coordinates for common screen sizes
                sign_in_coords = [
                    (650, 80),   # Top right for ~720p
                    (700, 100),  # Alternative position
                    (600, 120),  # Slightly lower
                    (680, 90),   # Center of area
                ]

            for i, (x, y) in enumerate(sign_in_coords):
                logger.info(f"🔘 Trying Sign In position {i+1}: ({x}, {y}) - Top right area")

                # Add human-like mouse jitter
                human_x, human_y = self.human_behavior.add_mouse_jitter(x, y)

                # Human-like delay before clicking
                self.human_behavior.human_delay(0.8, 1.5)

                tap_cmd = f'adb -s {device_id} shell "input tap {human_x} {human_y}"'
                subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=5)

                # Human-like delay after clicking
                self.human_behavior.human_delay(2.0, 3.5)

                # Take screenshot to verify
                self._take_screenshot(device_id, f"03_signin_attempt_{i+1}")

                # Check if we successfully navigated to sign-in page
                if self._check_for_email_input(device_id):
                    logger.info("✅ Successfully found and clicked Sign In button!")
                    return True

                # If first attempt failed, simulate human confusion
                if i == 0:
                    logger.info("🤔 First attempt failed, trying alternative positions...")
                    self.human_behavior.human_delay(1.0, 2.0)

            logger.warning("⚠️ Could not find Sign In button in top right area")
            return False

        except Exception as e:
            logger.error(f"❌ Error clicking next button: {e}")

    def _check_for_phone_verification(self, device_id: str) -> bool:
        """Check if phone verification is required"""
        try:
            cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                phone_indicators = ['phone', 'verify', 'mobile', 'number']
                return any(indicator in ui_content for indicator in phone_indicators)

            return False

        except Exception as e:
            logger.debug(f"Phone verification check failed: {e}")
            return False

    def _handle_phone_verification(self, device_id: str, phone_number: str):
        """Handle phone verification step"""
        try:
            logger.info("📱 Handling phone verification...")

            # Fill in phone number
            self._fill_text_field(device_id, phone_number, "phone")
            time.sleep(2)

            # Click Next
            self._click_next_button(device_id)
            time.sleep(3)

            # For now, we'll skip verification (would need SMS service integration)
            logger.warning("⚠️ Phone verification step - manual intervention may be required")
            self._take_screenshot(device_id, "06_phone_verification")

        except Exception as e:
            logger.error(f"❌ Error handling phone verification: {e}")

    def _handle_additional_info(self, device_id: str, info: PersonalInfo):
        """Handle additional information steps (birth date, etc.)"""
        try:
            logger.info("📅 Handling additional information...")

            # Check for birth date input
            if self._check_for_birth_date(device_id):
                logger.info(f"📅 Entering birth date: {info.birth_date}")
                self._fill_birth_date(device_id, info.birth_date)
                time.sleep(2)
                self._click_next_button(device_id)
                time.sleep(3)

            # Check for gender selection
            if self._check_for_gender(device_id):
                logger.info("👤 Selecting gender...")
                self._select_gender(device_id)
                time.sleep(2)
                self._click_next_button(device_id)
                time.sleep(3)

            # Handle any additional steps
            self._take_screenshot(device_id, "07_additional_info")

        except Exception as e:
            logger.error(f"❌ Error handling additional info: {e}")

    def _check_for_birth_date(self, device_id: str) -> bool:
        """Check if birth date input is required"""
        try:
            cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                birth_indicators = ['birth', 'birthday', 'date of birth', 'month', 'year']
                return any(indicator in ui_content for indicator in birth_indicators)

            return False

        except Exception as e:
            logger.debug(f"Birth date check failed: {e}")
            return False

    def _fill_birth_date(self, device_id: str, birth_date: str):
        """Fill birth date fields (MM/DD/YYYY format)"""
        try:
            # Parse birth date
            month, day, year = birth_date.split('/')

            # Fill month
            self._fill_text_field(device_id, month, "month")
            time.sleep(0.5)

            # Fill day
            self._fill_text_field(device_id, day, "day")
            time.sleep(0.5)

            # Fill year
            self._fill_text_field(device_id, year, "year")
            time.sleep(0.5)

        except Exception as e:
            logger.error(f"❌ Error filling birth date: {e}")

    def _check_for_gender(self, device_id: str) -> bool:
        """Check if gender selection is required"""
        try:
            cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                ui_content = result.stdout.lower()
                gender_indicators = ['gender', 'male', 'female', 'other']
                return any(indicator in ui_content for indicator in gender_indicators)

            return False

        except Exception as e:
            logger.debug(f"Gender check failed: {e}")
            return False

    def _select_gender(self, device_id: str):
        """Select gender (randomly choose between options)"""
        try:
            # Try clicking on common gender selection positions
            gender_coords = [
                (200, 400),  # First option
                (200, 450),  # Second option
                (200, 500),  # Third option
            ]

            # Randomly select one
            selected_coord = random.choice(gender_coords)
            tap_cmd = f'adb -s {device_id} shell "input tap {selected_coord[0]} {selected_coord[1]}"'
            subprocess.run(tap_cmd, shell=True, capture_output=True, text=True, timeout=5)

        except Exception as e:
            logger.error(f"❌ Error selecting gender: {e}")

    def _simulate_natural_browsing(self, device_id: str):
        """Simulate safe natural browsing behavior without navigation (keeps Google page)"""
        try:
            logger.info("🌐 Simulating natural page reading behavior...")

            # Safe browsing actions that don't navigate away from Google
            safe_actions = [
                'read_pause',
                'gentle_scroll',
                'page_scan'
            ]

            # Random number of safe browsing actions (2-4)
            num_actions = random.randint(2, 4)

            for i in range(num_actions):
                action_type = random.choice(safe_actions)

                logger.debug(f"🎭 Safe browsing action {i+1}/{num_actions}: {action_type}")

                if action_type == 'read_pause':
                    # Simulate reading Google homepage content
                    reading_time = random.uniform(3.0, 8.0)
                    logger.debug(f"📖 Reading Google homepage for {reading_time:.1f}s")
                    time.sleep(reading_time)

                elif action_type == 'gentle_scroll':
                    # Gentle scrolling to look at page content (small movements)
                    logger.debug("📜 Gentle page scrolling")
                    self._gentle_page_scroll(device_id)

                elif action_type == 'page_scan':
                    # Simulate scanning the page by looking at different areas
                    logger.debug("👁️ Scanning page content")
                    self._simulate_page_scanning(device_id)

                # Pause between actions
                self.human_behavior.human_delay(1.5, 3.0)

        except Exception as e:
            logger.debug(f"Natural browsing simulation failed: {e}")

    def _gentle_page_scroll(self, device_id: str):
        """Perform gentle scrolling that doesn't navigate away"""
        try:
            # Small scroll movements to simulate reading
            scroll_distance = random.randint(50, 150)  # Small distance

            if random.choice([True, False]):
                # Scroll down slightly
                start_y = 400
                end_y = start_y - scroll_distance
            else:
                # Scroll up slightly
                start_y = 400
                end_y = start_y + scroll_distance

            x = random.randint(200, 400)  # Center area
            duration = random.randint(300, 600)  # Slow scroll

            scroll_cmd = f'adb -s {device_id} shell "input swipe {x} {start_y} {x} {end_y} {duration}"'
            subprocess.run(scroll_cmd, shell=True, capture_output=True, text=True, timeout=5)

            # Brief pause after scrolling
            self.human_behavior.human_delay(0.5, 1.5)

        except Exception as e:
            logger.debug(f"Gentle scroll failed: {e}")

    def _simulate_page_scanning(self, device_id: str):
        """Simulate human eye movement by brief pauses at different screen areas"""
        try:
            # Define areas of interest on Google homepage
            scan_areas = [
                (200, 200),  # Top left (logo area)
                (400, 300),  # Center (search box area)
                (600, 200),  # Top right (sign in area)
                (400, 500),  # Lower center (buttons area)
            ]

            # Look at 2-3 random areas
            areas_to_scan = random.sample(scan_areas, random.randint(2, 3))

            for x, y in areas_to_scan:
                # Brief pause to simulate looking at this area
                scan_time = random.uniform(0.8, 2.0)
                logger.debug(f"👁️ Scanning area ({x}, {y}) for {scan_time:.1f}s")
                time.sleep(scan_time)

        except Exception as e:
            logger.debug(f"Page scanning failed: {e}")

    def run_complete_automation(self) -> Dict:
        """Run the complete Gmail account creation automation"""
        device_name = None
        device_id = None

        try:
            logger.info("🚀 Starting Gmail Account Creation Automation with Human Behavior")
            logger.info("=" * 60)

            # Anti-detection: Random startup delay
            startup_delay = random.uniform(5.0, 15.0)
            logger.info(f"⏳ Anti-detection startup delay: {startup_delay:.1f}s")
            time.sleep(startup_delay)

            # Step 1: Generate personal information
            logger.info("📋 Step 1: Generating personal information...")
            info = self.generate_personal_info()

            # Anti-detection: Simulate human preparation time
            logger.info("🧠 Simulating human preparation time...")
            self.human_behavior.human_delay(3.0, 8.0)

            # Step 2: Create and configure spoofed device
            logger.info("📱 Step 2: Creating spoofed device...")
            device_name, device_id = self.create_spoofed_device()

            if not device_name or not device_id:
                raise Exception("Failed to create spoofed device")

            # Anti-detection: Device settling time
            logger.info("📱 Allowing device to settle...")
            self.human_behavior.human_delay(5.0, 10.0)

            # Verify Android system is responsive (HTC One method)
            logger.info("🔍 Verifying Android system responsiveness...")
            if not self._verify_android_system_responsive(device_id):
                logger.warning("⚠️ Android system may not be fully responsive, but continuing...")
                # Don't fail here, just warn

            # Unlock screen if needed (Android 14.0 requirement)
            logger.info("🔓 Ensuring screen is unlocked...")
            if not self._unlock_screen_if_needed(device_id):
                logger.warning("⚠️ Screen unlock may have failed, but continuing...")
                # Don't fail here, just warn

            # Step 3: Navigate to Google
            logger.info("🌐 Step 3: Navigating to Google...")
            if not self.navigate_to_gmail_signup(device_id):
                raise Exception("Failed to navigate to Google")

            # Anti-detection: Simulate browsing behavior before signup
            logger.info("🌐 Simulating natural browsing behavior...")
            self._simulate_natural_browsing(device_id)

            # Step 4: Click Sign In
            logger.info("🔘 Step 4: Clicking Sign In...")
            if not self.click_sign_in_button(device_id):
                raise Exception("Failed to click Sign In")

            # Step 5: Create Gmail account
            logger.info("📝 Step 5: Creating Gmail account...")
            email = self.create_gmail_account(device_id, info)

            if not email:
                raise Exception("Failed to create Gmail account")

            # Step 6: Save account information
            logger.info("💾 Step 6: Saving account information...")
            self.save_account_to_file(info, email)
            self.save_account_to_database(info, email, device_name)

            # Success!
            result = {
                'success': True,
                'email': email,
                'password': info.password,
                'first_name': info.first_name,
                'last_name': info.last_name,
                'birth_date': info.birth_date,
                'device_name': device_name,
                'created_at': datetime.now().isoformat()
            }

            logger.info("🎉 Gmail Account Creation Completed Successfully!")
            logger.info(f"📧 Email: {email}")
            logger.info(f"👤 Name: {info.first_name} {info.last_name}")
            logger.info("=" * 60)

            return result

        except Exception as e:
            logger.error(f"❌ Gmail account creation failed: {e}")

            result = {
                'success': False,
                'error': str(e),
                'device_name': device_name,
                'created_at': datetime.now().isoformat()
            }

            return result

        finally:
            # Always cleanup device
            if device_name:
                logger.info("🧹 Cleaning up device...")
                self.cleanup_device(device_name)

    def cleanup_device(self, device_name: str):
        """Clean up the created device"""
        try:
            if device_name:
                logger.info(f"🧹 Cleaning up device: {device_name}")
                self.genymotion.stop_instance(device_name)
                self.genymotion.delete_instance(device_name)
                logger.info("✅ Device cleanup completed")
        except Exception as e:
            logger.error(f"❌ Error cleaning up device: {e}")


def main():
    """Main function for testing Gmail account creation"""
    try:
        creator = GmailAccountCreator()
        result = creator.run_complete_automation()

        if result['success']:
            print(f"✅ SUCCESS: Created account {result['email']}")
        else:
            print(f"❌ FAILED: {result['error']}")

        return result

    except Exception as e:
        logger.error(f"❌ Main execution failed: {e}")
        return {'success': False, 'error': str(e)}


if __name__ == "__main__":
    main()
