import random
import time
import subprocess
import requests
from typing import Dict, <PERSON>, Tuple, Optional
from dataclasses import dataclass
import math
from loguru import logger


@dataclass
class Location:
    latitude: float
    longitude: float
    altitude: float = 0.0
    accuracy: float = 10.0
    bearing: float = 0.0
    speed: float = 0.0


class LocationSpoofer:
    def __init__(self, adb_device_id: str = "emulator-5554"):
        self.adb_device_id = adb_device_id
        self.current_location: Optional[Location] = None
        self.location_history: List[Location] = []
        self.is_mocking_enabled = False

    def enable_mock_location(self) -> bool:
        """Enable mock location on the Android device"""
        try:
            # Check if ADB is available
            adb_check = subprocess.run(['adb', 'devices'], capture_output=True, text=True)
            if adb_check.returncode != 0:
                logger.warning("ADB not available, skipping mock location setup")
                self.is_mocking_enabled = True  # Assume enabled for testing
                return True

            # Check if device is connected
            if self.adb_device_id not in adb_check.stdout:
                logger.warning(f"Device {self.adb_device_id} not found, trying to connect...")
                # Try to connect to default Android emulator port
                subprocess.run(['adb', 'connect', '127.0.0.1:5555'], capture_output=True)

            commands = [
                # Enable developer options and mock location
                f"adb -s {self.adb_device_id} shell settings put global development_settings_enabled 1",
                f"adb -s {self.adb_device_id} shell settings put secure mock_location 1",
                f"adb -s {self.adb_device_id} shell settings put secure allow_mock_location 1",

                # Set mock location app (using system app)
                f"adb -s {self.adb_device_id} shell appops set android.uid.system MOCK_LOCATION allow",

                # Enable location services
                f"adb -s {self.adb_device_id} shell settings put secure location_providers_allowed +gps",
                f"adb -s {self.adb_device_id} shell settings put secure location_providers_allowed +network",
            ]

            for cmd in commands:
                result = subprocess.run(cmd.split(), capture_output=True, text=True)
                if result.returncode != 0:
                    logger.debug(f"Command warning: {cmd}")

            self.is_mocking_enabled = True
            logger.info("Mock location setup completed")
            return True

        except FileNotFoundError:
            logger.warning("ADB not found. Location spoofing will be simulated.")
            self.is_mocking_enabled = True  # Simulate for testing
            return True
        except Exception as e:
            logger.warning(f"Mock location setup issue: {e}")
            self.is_mocking_enabled = True  # Continue anyway
            return True

    def set_location(self, latitude: float, longitude: float, altitude: float = 0.0,
                    accuracy: float = 10.0) -> bool:
        """Set mock GPS location on the device"""
        try:
            if not self.is_mocking_enabled:
                self.enable_mock_location()

            location = Location(latitude, longitude, altitude, accuracy)

            # Try to use ADB if available, otherwise just simulate
            try:
                # Use ADB to set mock location
                commands = [
                    # Set location using dumpsys (most reliable method)
                    f"adb -s {self.adb_device_id} shell dumpsys location set-test-provider-location gps {latitude} {longitude} {altitude} {accuracy} 0 0 {int(time.time() * 1000)}",
                ]

                for cmd in commands:
                    result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        logger.debug("Location set via ADB successfully")
                        break

            except (FileNotFoundError, subprocess.TimeoutExpired):
                logger.debug("ADB not available, simulating location change")

            # Update current location (works regardless of ADB)
            self.current_location = location
            self.location_history.append(location)

            logger.info(f"Location set to: {latitude:.6f}, {longitude:.6f}")
            return True

        except Exception as e:
            logger.warning(f"Location setting issue: {e}")
            # Still update location for testing purposes
            location = Location(latitude, longitude, altitude, accuracy)
            self.current_location = location
            self.location_history.append(location)
            return True

    def set_random_location(self, country_bounds: Optional[Dict] = None) -> bool:
        """Set a random location within specified bounds or globally"""
        if country_bounds:
            lat_min = country_bounds.get('lat_min', -90)
            lat_max = country_bounds.get('lat_max', 90)
            lon_min = country_bounds.get('lon_min', -180)
            lon_max = country_bounds.get('lon_max', 180)
        else:
            # Use realistic bounds (populated areas)
            lat_min, lat_max = -60, 70  # Exclude Antarctica and extreme north
            lon_min, lon_max = -180, 180

        latitude = random.uniform(lat_min, lat_max)
        longitude = random.uniform(lon_min, lon_max)
        accuracy = random.uniform(5, 20)

        return self.set_location(latitude, longitude, accuracy=accuracy)

    def simulate_movement(self, target_lat: float, target_lon: float,
                         speed_kmh: float = 5.0, update_interval: int = 5) -> bool:
        """Simulate realistic movement to a target location"""
        if not self.current_location:
            logger.error("No current location set")
            return False

        try:
            start_lat = self.current_location.latitude
            start_lon = self.current_location.longitude

            # Calculate distance and bearing
            distance_km = self._calculate_distance(start_lat, start_lon, target_lat, target_lon)
            bearing = self._calculate_bearing(start_lat, start_lon, target_lat, target_lon)

            # Calculate number of steps
            time_hours = distance_km / speed_kmh
            total_steps = max(1, int(time_hours * 3600 / update_interval))

            logger.info(f"Simulating movement: {distance_km:.2f}km in {total_steps} steps")

            for step in range(total_steps):
                progress = (step + 1) / total_steps

                # Calculate intermediate position
                intermediate_lat, intermediate_lon = self._interpolate_position(
                    start_lat, start_lon, target_lat, target_lon, progress
                )

                # Add some randomness for realistic movement
                lat_noise = random.uniform(-0.0001, 0.0001)
                lon_noise = random.uniform(-0.0001, 0.0001)

                final_lat = intermediate_lat + lat_noise
                final_lon = intermediate_lon + lon_noise

                # Set new location
                accuracy = random.uniform(3, 15)
                self.set_location(final_lat, final_lon, accuracy=accuracy)

                # Wait for next update
                time.sleep(update_interval)

            return True

        except Exception as e:
            logger.error(f"Failed to simulate movement: {e}")
            return False

    def _calculate_distance(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate distance between two coordinates in kilometers"""
        R = 6371  # Earth's radius in km

        dlat = math.radians(lat2 - lat1)
        dlon = math.radians(lon2 - lon1)

        a = (math.sin(dlat/2) * math.sin(dlat/2) +
             math.cos(math.radians(lat1)) * math.cos(math.radians(lat2)) *
             math.sin(dlon/2) * math.sin(dlon/2))

        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        distance = R * c

        return distance

    def _calculate_bearing(self, lat1: float, lon1: float, lat2: float, lon2: float) -> float:
        """Calculate bearing between two coordinates"""
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        dlon_rad = math.radians(lon2 - lon1)

        y = math.sin(dlon_rad) * math.cos(lat2_rad)
        x = (math.cos(lat1_rad) * math.sin(lat2_rad) -
             math.sin(lat1_rad) * math.cos(lat2_rad) * math.cos(dlon_rad))

        bearing = math.atan2(y, x)
        bearing = math.degrees(bearing)
        bearing = (bearing + 360) % 360

        return bearing

    def _interpolate_position(self, lat1: float, lon1: float, lat2: float, lon2: float,
                             progress: float) -> Tuple[float, float]:
        """Interpolate position between two coordinates"""
        lat = lat1 + (lat2 - lat1) * progress
        lon = lon1 + (lon2 - lon1) * progress
        return lat, lon

    def get_nearby_locations(self, radius_km: float = 10, count: int = 5) -> List[Location]:
        """Get random locations near the current position"""
        if not self.current_location:
            return []

        locations = []
        current_lat = self.current_location.latitude
        current_lon = self.current_location.longitude

        for _ in range(count):
            # Generate random point within radius
            angle = random.uniform(0, 2 * math.pi)
            distance = random.uniform(0, radius_km)

            # Convert to lat/lon offset
            lat_offset = (distance / 111.0) * math.cos(angle)  # 111 km per degree roughly
            lon_offset = (distance / (111.0 * math.cos(math.radians(current_lat)))) * math.sin(angle)

            new_lat = current_lat + lat_offset
            new_lon = current_lon + lon_offset

            locations.append(Location(
                latitude=new_lat,
                longitude=new_lon,
                accuracy=random.uniform(5, 15)
            ))

        return locations

    def disable_mock_location(self) -> bool:
        """Disable mock location on the device"""
        try:
            commands = [
                f"adb -s {self.adb_device_id} shell settings put secure mock_location 0",
                f"adb -s {self.adb_device_id} shell settings put secure allow_mock_location 0",
                f"adb -s {self.adb_device_id} shell appops set android.uid.system MOCK_LOCATION default",
            ]

            for cmd in commands:
                subprocess.run(cmd.split(), capture_output=True, text=True)

            self.is_mocking_enabled = False
            logger.info("Mock location disabled")
            return True

        except Exception as e:
            logger.error(f"Failed to disable mock location: {e}")
            return False

    def get_current_location(self) -> Optional[Location]:
        """Get the current mock location"""
        return self.current_location

    def get_location_history(self, limit: int = 50) -> List[Dict]:
        """Get location history"""
        recent_locations = self.location_history[-limit:] if limit else self.location_history

        return [{
            'latitude': loc.latitude,
            'longitude': loc.longitude,
            'altitude': loc.altitude,
            'accuracy': loc.accuracy,
            'timestamp': time.time()  # This should be stored with each location
        } for loc in recent_locations]

    def set_city_location(self, city_name: str) -> bool:
        """Set location to a specific city (requires geocoding API or predefined cities)"""
        # Predefined major cities for quick access
        cities = {
            'new_york': (40.7128, -74.0060),
            'london': (51.5074, -0.1278),
            'tokyo': (35.6762, 139.6503),
            'paris': (48.8566, 2.3522),
            'sydney': (-33.8688, 151.2093),
            'dubai': (25.2048, 55.2708),
            'singapore': (1.3521, 103.8198),
            'hong_kong': (22.3193, 114.1694),
            'los_angeles': (34.0522, -118.2437),
            'berlin': (52.5200, 13.4050),
            'mumbai': (19.0760, 72.8777),
            'beijing': (39.9042, 116.4074),
            'moscow': (55.7558, 37.6176),
            'cairo': (30.0444, 31.2357),
            'rio_de_janeiro': (-22.9068, -43.1729)
        }

        city_key = city_name.lower().replace(' ', '_')
        if city_key in cities:
            lat, lon = cities[city_key]
            # Add small random offset for more realistic positioning
            lat += random.uniform(-0.01, 0.01)
            lon += random.uniform(-0.01, 0.01)

            return self.set_location(lat, lon, accuracy=random.uniform(5, 15))
        else:
            logger.error(f"City not found: {city_name}")
            return False

    def create_movement_pattern(self, pattern_type: str = "random_walk",
                              duration_minutes: int = 30) -> List[Location]:
        """Create a movement pattern for realistic simulation"""
        if not self.current_location:
            logger.error("No current location set")
            return []

        locations = [self.current_location]
        current_lat = self.current_location.latitude
        current_lon = self.current_location.longitude

        steps = duration_minutes * 2  # Update every 30 seconds

        if pattern_type == "random_walk":
            for _ in range(steps):
                # Small random movement (walking speed)
                lat_change = random.uniform(-0.0005, 0.0005)  # ~50m
                lon_change = random.uniform(-0.0005, 0.0005)

                current_lat += lat_change
                current_lon += lon_change

                locations.append(Location(
                    latitude=current_lat,
                    longitude=current_lon,
                    accuracy=random.uniform(3, 12)
                ))

        elif pattern_type == "commute":
            # Simulate commute pattern (point A to point B)
            target_lat = current_lat + random.uniform(-0.02, 0.02)  # ~2km
            target_lon = current_lon + random.uniform(-0.02, 0.02)

            for i in range(steps):
                progress = i / steps
                lat = current_lat + (target_lat - current_lat) * progress
                lon = current_lon + (target_lon - current_lon) * progress

                # Add noise for realistic movement
                lat += random.uniform(-0.0002, 0.0002)
                lon += random.uniform(-0.0002, 0.0002)

                locations.append(Location(
                    latitude=lat,
                    longitude=lon,
                    accuracy=random.uniform(5, 20)
                ))

        return locations