#!/usr/bin/env python3
"""
Start Device and Test Gmail Form
Ensures device is running before testing Gmail form interaction
"""

import sys
import os
import time
import subprocess
import re
from typing import Dict, List, Optional

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager


class DeviceGmailTester:
    def __init__(self):
        self.genymotion = GenymotionManager()
        
    def find_any_device(self) -> Optional[str]:
        """Find any available device (running or stopped)"""
        print("🔍 Looking for available devices...")
        
        try:
            instances = self.genymotion.get_available_instances()
            
            if not instances:
                print("❌ No devices found")
                return None
            
            # List all devices
            print("📱 Available devices:")
            for name, info in instances.items():
                status = info.get('status', 'unknown')
                print(f"  - {name}: {status}")
            
            # Return first device found
            device_name = list(instances.keys())[0]
            print(f"✅ Selected device: {device_name}")
            return device_name
            
        except Exception as e:
            print(f"❌ Error finding devices: {e}")
            return None
    
    def ensure_device_running(self, device_name: str) -> bool:
        """Ensure device is running, start if needed"""
        print(f"🚀 Ensuring device is running: {device_name}")
        
        try:
            # Check current status
            instances = self.genymotion.get_available_instances()
            device_info = instances.get(device_name)
            
            if not device_info:
                print(f"❌ Device {device_name} not found")
                return False
            
            current_status = device_info.get('status')
            print(f"📊 Current status: {current_status}")
            
            if current_status == 'running':
                print(f"✅ Device {device_name} is already running")
                return True
            elif current_status == 'stopped':
                print(f"🔄 Starting device {device_name}...")
                
                # Start the device
                success = self.genymotion.start_instance_by_name(device_name)
                
                if success:
                    print(f"✅ Device {device_name} start command sent successfully")
                    
                    # Wait for device to start
                    print("⏳ Waiting for device to start...")
                    for i in range(12):  # Wait up to 60 seconds
                        time.sleep(5)
                        
                        # Check status
                        instances = self.genymotion.get_available_instances()
                        device_info = instances.get(device_name, {})
                        status = device_info.get('status')
                        
                        print(f"   Status check {i+1}/12: {status}")
                        
                        if status == 'running':
                            print(f"✅ Device {device_name} is now running!")
                            
                            # Additional wait for device to be fully ready
                            print("⏳ Waiting for device to be fully ready...")
                            time.sleep(15)
                            return True
                    
                    print(f"⚠️ Device may still be starting, continuing anyway...")
                    return True
                else:
                    print(f"❌ Failed to start device {device_name}")
                    return False
            else:
                print(f"❌ Device {device_name} in unknown status: {current_status}")
                return False
                
        except Exception as e:
            print(f"❌ Error ensuring device running: {e}")
            return False
    
    def wait_for_adb_connection(self, device_name: str, max_attempts: int = 10) -> Optional[str]:
        """Wait for ADB connection to be established"""
        print(f"🔗 Waiting for ADB connection...")
        
        try:
            device_id = self.genymotion.get_device_adb_id(device_name)
            print(f"📱 Device ADB ID: {device_id}")
            
            for attempt in range(max_attempts):
                print(f"   Attempt {attempt + 1}/{max_attempts}...")
                
                # Kill and restart ADB server
                subprocess.run('adb kill-server', shell=True, timeout=5)
                time.sleep(1)
                subprocess.run('adb start-server', shell=True, timeout=5)
                time.sleep(2)
                
                # Connect to device if it's network ADB
                if ':' in device_id:
                    subprocess.run(f'adb connect {device_id}', shell=True, timeout=10)
                    time.sleep(3)
                
                # Test connection
                result = subprocess.run(f'adb -s {device_id} shell echo "test"', 
                                      shell=True, capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print(f"✅ ADB connection established!")
                    return device_id
                else:
                    print(f"   Connection failed: {result.stderr.strip()}")
                    time.sleep(3)
            
            print(f"❌ Could not establish ADB connection after {max_attempts} attempts")
            return None
            
        except Exception as e:
            print(f"❌ Error waiting for ADB connection: {e}")
            return None
    
    def open_gmail_signup(self, device_id: str) -> bool:
        """Open Gmail signup page"""
        print("🌐 Opening Gmail signup page...")
        
        try:
            # Wake up device and unlock
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_WAKEUP', shell=True, timeout=5)
            time.sleep(2)
            subprocess.run(f'adb -s {device_id} shell input swipe 400 800 400 200', shell=True, timeout=5)
            time.sleep(2)
            
            # Open browser with Gmail signup URL
            url = "https://accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp"
            cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "{url}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode == 0:
                print("✅ Browser opened with Gmail signup URL")
                print("⏳ Waiting for page to load...")
                time.sleep(10)
                return True
            else:
                print(f"❌ Failed to open browser: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error opening Gmail signup: {e}")
            return False
    
    def analyze_gmail_page(self, device_id: str) -> Dict:
        """Analyze the current Gmail signup page"""
        print("🔍 Analyzing Gmail signup page...")
        
        try:
            # Take screenshot
            subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_signup_analysis.png', 
                         shell=True, timeout=10)
            print("📸 Screenshot saved: /tmp/gmail_signup_analysis.png")
            
            # Get UI dump
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)
            
            if result.returncode != 0:
                return {"error": "Failed to get UI dump"}
            
            ui_content = result.stdout
            
            # Analyze the page
            analysis = {
                "page_loaded": len(ui_content) > 1000,
                "has_google_branding": "google" in ui_content.lower(),
                "has_create_account": "create" in ui_content.lower() and "account" in ui_content.lower(),
                "input_fields": [],
                "buttons": [],
                "form_context": "unknown"
            }
            
            # Find input fields
            edittext_pattern = r'<node[^>]*class="android\.widget\.EditText"[^>]*>'
            edittext_matches = re.findall(edittext_pattern, ui_content)
            
            for i, match in enumerate(edittext_matches):
                field_info = self._parse_field(match, i)
                analysis["input_fields"].append(field_info)
                print(f"📝 Input field {i}: '{field_info['text']}' / '{field_info['hint']}'")
            
            # Find buttons
            button_patterns = [
                r'<node[^>]*class="android\.widget\.Button"[^>]*text="([^"]*)"[^>]*>',
                r'<node[^>]*clickable="true"[^>]*text="([^"]*)"[^>]*>'
            ]
            
            for pattern in button_patterns:
                button_matches = re.findall(pattern, ui_content)
                for button_text in button_matches:
                    if button_text.strip():
                        analysis["buttons"].append(button_text)
                        print(f"🔘 Button found: '{button_text}'")
            
            # Determine context
            if "webview" in ui_content.lower() or "chrome" in ui_content.lower():
                analysis["form_context"] = "web"
            else:
                analysis["form_context"] = "native"
            
            print(f"📊 Analysis complete:")
            print(f"   Page loaded: {analysis['page_loaded']}")
            print(f"   Google branding: {analysis['has_google_branding']}")
            print(f"   Create account text: {analysis['has_create_account']}")
            print(f"   Input fields: {len(analysis['input_fields'])}")
            print(f"   Buttons: {len(analysis['buttons'])}")
            print(f"   Context: {analysis['form_context']}")
            
            return analysis
            
        except Exception as e:
            return {"error": str(e)}
    
    def _parse_field(self, element_xml: str, index: int) -> Dict:
        """Parse input field from XML"""
        field = {"index": index, "text": "", "hint": "", "bounds": None}
        
        # Extract text
        text_match = re.search(r'text="([^"]*)"', element_xml)
        if text_match:
            field["text"] = text_match.group(1)
        
        # Extract hint/content-desc
        hint_match = re.search(r'content-desc="([^"]*)"', element_xml)
        if hint_match:
            field["hint"] = hint_match.group(1)
        
        # Extract bounds
        bounds_match = re.search(r'bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', element_xml)
        if bounds_match:
            x1, y1, x2, y2 = bounds_match.groups()
            field["bounds"] = {
                "x1": int(x1), "y1": int(y1), "x2": int(x2), "y2": int(y2),
                "center_x": (int(x1) + int(x2)) // 2,
                "center_y": (int(y1) + int(y2)) // 2
            }
        
        return field
    
    def test_form_interaction(self, device_id: str, analysis: Dict) -> Dict:
        """Test form interaction with the analyzed fields"""
        print("⌨️ Testing form interaction...")
        
        results = {"tests": [], "success_count": 0}
        
        for field in analysis["input_fields"][:3]:  # Test first 3 fields
            print(f"\n🧪 Testing field {field['index']}: '{field['text']}' / '{field['hint']}'")
            
            if not field["bounds"]:
                print("   ❌ No bounds information")
                continue
            
            bounds = field["bounds"]
            center_x = bounds["center_x"]
            center_y = bounds["center_y"]
            
            test_result = {
                "field_index": field["index"],
                "field_info": field,
                "tap_success": False,
                "input_success": False
            }
            
            try:
                # Tap the field
                print(f"   📍 Tapping at ({center_x}, {center_y})")
                tap_result = subprocess.run(f'adb -s {device_id} shell input tap {center_x} {center_y}', 
                                          shell=True, timeout=5)
                test_result["tap_success"] = tap_result.returncode == 0
                
                if test_result["tap_success"]:
                    time.sleep(1)
                    
                    # Try to input text
                    test_text = f"TestName{field['index']}"
                    print(f"   ⌨️ Inputting: '{test_text}'")
                    
                    input_result = subprocess.run(f'adb -s {device_id} shell input text "{test_text}"', 
                                                shell=True, timeout=5)
                    test_result["input_success"] = input_result.returncode == 0
                    
                    if test_result["input_success"]:
                        print(f"   ✅ Input successful")
                        results["success_count"] += 1
                    else:
                        print(f"   ❌ Input failed")
                    
                    time.sleep(1)
                else:
                    print(f"   ❌ Tap failed")
                
            except Exception as e:
                test_result["error"] = str(e)
                print(f"   ❌ Test error: {e}")
            
            results["tests"].append(test_result)
        
        return results
    
    def run_complete_test(self) -> Dict:
        """Run complete device start and Gmail form test"""
        print("🚀 Starting Complete Device and Gmail Form Test")
        print("=" * 60)
        
        # Step 1: Find device
        device_name = self.find_any_device()
        if not device_name:
            return {"error": "No devices available"}
        
        # Step 2: Ensure device is running
        if not self.ensure_device_running(device_name):
            return {"error": "Failed to start device"}
        
        # Step 3: Wait for ADB connection
        device_id = self.wait_for_adb_connection(device_name)
        if not device_id:
            return {"error": "Failed to establish ADB connection"}
        
        # Step 4: Open Gmail signup
        if not self.open_gmail_signup(device_id):
            return {"error": "Failed to open Gmail signup page"}
        
        # Step 5: Analyze page
        analysis = self.analyze_gmail_page(device_id)
        if "error" in analysis:
            return {"error": f"Page analysis failed: {analysis['error']}"}
        
        # Step 6: Test form interaction
        interaction_results = self.test_form_interaction(device_id, analysis)
        
        return {
            "device_name": device_name,
            "device_id": device_id,
            "page_analysis": analysis,
            "interaction_results": interaction_results,
            "success": True
        }


def main():
    """Main test execution"""
    print("🧪 Device Start and Gmail Form Test")
    print("=" * 60)
    
    tester = DeviceGmailTester()
    results = tester.run_complete_test()
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)
    
    if "error" in results:
        print(f"❌ Test failed: {results['error']}")
        return 1
    
    print(f"✅ Test completed successfully!")
    print(f"📱 Device: {results['device_name']} ({results['device_id']})")
    
    analysis = results["page_analysis"]
    print(f"🌐 Page Analysis:")
    print(f"   Context: {analysis['form_context']}")
    print(f"   Input fields found: {len(analysis['input_fields'])}")
    print(f"   Buttons found: {len(analysis['buttons'])}")
    
    interaction = results["interaction_results"]
    print(f"⌨️ Form Interaction:")
    print(f"   Fields tested: {len(interaction['tests'])}")
    print(f"   Successful inputs: {interaction['success_count']}")
    
    if interaction['success_count'] == 0 and len(interaction['tests']) > 0:
        print(f"\n⚠️ ISSUE IDENTIFIED: Form input is not working")
        print(f"   Possible solutions:")
        print(f"   1. Use WebView-specific input methods")
        print(f"   2. Handle keyboard focus differently")
        print(f"   3. Use JavaScript injection for web forms")
    
    print(f"\n📸 Screenshot available: /tmp/gmail_signup_analysis.png")
    
    return 0


if __name__ == "__main__":
    exit(main())
