#!/usr/bin/env python3
"""
Test Actual Gmail Automation
Run the actual Gmail automation to see exactly where it fails
"""

import sys
import os
import time

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator, PersonalInfo
from genymotion_manager import GenymotionManager


def test_gmail_automation():
    """Test the actual Gmail automation process"""
    print("🧪 Testing Actual Gmail Automation")
    print("=" * 50)

    # Initialize components
    genymotion = GenymotionManager()
    gmail_creator = GmailAccountCreator()

    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()

    running_device = None
    device_id = None

    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break

    if not running_device:
        print("❌ No running device found")
        return False

    # Create test personal info
    print("👤 Creating test personal info...")
    test_info = PersonalInfo(
        first_name="Ahmet",
        last_name="<PERSON><PERSON>lma<PERSON>",
        username="ahmetyilmaz" + str(int(time.time()))[-4:],  # Add timestamp for uniqueness
        password="TestPassword123!",
        phone_number="+************",
        birth_date="1990-01-01",
        recovery_email="<EMAIL>"
    )

    print(f"   First Name: {test_info.first_name}")
    print(f"   Last Name: {test_info.last_name}")
    print(f"   Username: {test_info.username}")

    # Test navigation
    print("\n🌐 Testing Gmail navigation...")
    navigation_success = gmail_creator.navigate_to_gmail_signup(device_id)

    if navigation_success:
        print("✅ Navigation completed")
    else:
        print("❌ Navigation failed")
        return False

    # Wait a bit for page to settle
    print("⏳ Waiting for page to settle...")
    time.sleep(5)

    # Test account creation
    print("\n📝 Testing Gmail account creation...")
    try:
        result = gmail_creator.create_gmail_account(device_id, test_info)

        if result:
            print(f"✅ Gmail account creation completed: {result}")
            return True
        else:
            print("❌ Gmail account creation failed")
            return False

    except Exception as e:
        print(f"❌ Gmail account creation error: {e}")
        return False


def main():
    """Main test execution"""
    success = test_gmail_automation()

    print("\n" + "=" * 50)
    if success:
        print("✅ Gmail automation test completed successfully!")
        print("📸 Check screenshots in the screenshots directory")
    else:
        print("❌ Gmail automation test failed")
        print("📸 Check screenshots for debugging")

    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
