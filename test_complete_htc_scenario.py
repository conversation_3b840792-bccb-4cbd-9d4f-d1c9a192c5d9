#!/usr/bin/env python3
"""
Complete End-to-End Test for HTC One Android 14.0 Browser Automation

This test performs the complete workflow:
1. Check Genymotion installation
2. Create HTC One Android 14.0 virtual device
3. Start the device
4. Wait for Android boot completion
5. Connect via ADB
6. Launch default Android browser
7. Navigate to Google.com
8. Perform search interaction
9. Cleanup resources

This is a REAL test that creates actual devices - use with caution!
"""

import time
import subprocess
import sys
import os
from pathlib import Path
from typing import Optional

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.genymotion_manager import GenymotionManager
from src.appium_server import AppiumServerManager
from loguru import logger


class CompleteHTCScenarioTest:
    """Complete end-to-end test for HTC One scenario"""

    def __init__(self):
        self.genymotion_manager = GenymotionManager()
        self.appium_manager = AppiumServerManager()
        self.test_instance_name = "HTC_One_Test_Device"
        self.device_id = None
        self.cleanup_needed = False

        # HTC One Android 14.0 configuration
        self.device_config = {
            "hardware_profile": "HTC One",
            "android_version": "14",
            "device_name": "HTC One",
            "screen_resolution": "1080x1920",
            "dpi": 441,
            "deviceManufacturer": "HTC",
            "deviceModel": "HTC One",
            "platformVersion": "14"
        }

    def run_complete_test(self) -> bool:
        """Run the complete end-to-end test"""
        try:
            logger.info("🚀 Starting Complete HTC One Android 14.0 Test")
            logger.info("=" * 70)

            # Step 1: Check prerequisites
            if not self._check_prerequisites():
                return False

            # Step 2: Create HTC One device
            if not self._create_htc_device():
                return False

            # Step 3: Start the device
            if not self._start_device():
                return False

            # Step 4: Wait for device ready
            if not self._wait_for_device_ready():
                return False

            # Step 5: Verify ADB connection
            if not self._verify_adb_connection():
                return False

            # Step 6: Check Android system
            if not self._verify_android_system():
                return False

            # Step 7: Launch browser and visit Google
            if not self._test_browser_automation():
                return False

            # Step 8: Perform search test
            if not self._test_google_search():
                return False

            logger.info("🎉 Complete test passed successfully!")
            return True

        except Exception as e:
            logger.error(f"❌ Test failed with exception: {e}")
            return False
        finally:
            # Always cleanup
            self._cleanup_resources()

    def _check_prerequisites(self) -> bool:
        """Check all prerequisites"""
        logger.info("📋 Checking prerequisites...")

        # Check Genymotion installation
        try:
            result = self.genymotion_manager._execute_gmtool(['version'])
            if not result or result.returncode != 0:
                logger.error("❌ Genymotion Desktop not found")
                logger.error("Please install Genymotion Desktop from https://www.genymotion.com/")
                return False
            logger.info("✅ Genymotion Desktop found")
        except Exception as e:
            logger.error(f"❌ Error checking Genymotion: {e}")
            return False

        # Check ADB
        try:
            result = subprocess.run(['adb', 'version'], capture_output=True, timeout=10)
            if result.returncode != 0:
                logger.error("❌ ADB not found or not working")
                return False
            logger.info("✅ ADB found and working")
        except Exception as e:
            logger.error(f"❌ Error checking ADB: {e}")
            return False

        # Check system resources
        import psutil
        memory_gb = psutil.virtual_memory().total / (1024**3)
        if memory_gb < 4:
            logger.warning(f"⚠️ Low system memory: {memory_gb:.1f}GB (4GB+ recommended)")
        else:
            logger.info(f"✅ System memory: {memory_gb:.1f}GB")

        return True

    def _create_htc_device(self) -> bool:
        """Create HTC One Android 14.0 device"""
        logger.info(f"📱 Creating HTC One device: {self.test_instance_name}")

        try:
            # Check if device already exists
            instances = self.genymotion_manager.get_available_instances()
            if self.test_instance_name in instances:
                logger.info(f"Device '{self.test_instance_name}' already exists")
                return True

            # Create new device
            success = self.genymotion_manager.create_new_instance(
                self.test_instance_name,
                self.device_config["android_version"]
            )

            if success:
                logger.info(f"✅ Device '{self.test_instance_name}' created successfully")
                self.cleanup_needed = True

                # Configure device properties
                self._configure_device_properties()
                return True
            else:
                logger.error(f"❌ Failed to create device '{self.test_instance_name}'")
                return False

        except Exception as e:
            logger.error(f"❌ Error creating device: {e}")
            return False

    def _configure_device_properties(self) -> bool:
        """Configure HTC One specific properties"""
        logger.info("⚙️ Configuring HTC One properties...")

        try:
            # Configure device using gmtool
            config_commands = [
                ['admin', 'edit', self.test_instance_name, '--width', '1080', '--height', '1920', '--density', '441'],
                ['admin', 'edit', self.test_instance_name, '--sysprop', 'MANUFACTURER:HTC'],
                ['admin', 'edit', self.test_instance_name, '--sysprop', 'MODEL:HTC One'],
                ['admin', 'edit', self.test_instance_name, '--sysprop', 'DEVICE:HTC One']
            ]

            for cmd in config_commands:
                try:
                    result = self.genymotion_manager._execute_gmtool(cmd)
                    if result and result.returncode == 0:
                        logger.debug(f"✅ Configuration applied: {' '.join(cmd[2:])}")
                    else:
                        logger.warning(f"⚠️ Configuration may have failed: {' '.join(cmd[2:])}")
                except Exception as e:
                    logger.debug(f"Configuration command failed: {e}")

            logger.info("✅ Device configuration completed")
            return True

        except Exception as e:
            logger.warning(f"⚠️ Some configuration options failed: {e}")
            return True  # Continue even if some configs fail

    def _start_device(self) -> bool:
        """Start the HTC One device"""
        logger.info(f"▶️ Starting device: {self.test_instance_name}")

        try:
            success = self.genymotion_manager.start_instance(self.test_instance_name)

            if success:
                logger.info(f"✅ Device '{self.test_instance_name}' started successfully")

                # Get device ID
                if hasattr(self.genymotion_manager, 'actual_device_id') and self.genymotion_manager.actual_device_id:
                    self.device_id = self.genymotion_manager.actual_device_id
                    logger.info(f"📱 Device ID: {self.device_id}")

                return True
            else:
                logger.error(f"❌ Failed to start device '{self.test_instance_name}'")
                return False

        except Exception as e:
            logger.error(f"❌ Error starting device: {e}")
            return False

    def _wait_for_device_ready(self) -> bool:
        """Wait for device to be fully ready"""
        logger.info("⏳ Waiting for device to be ready...")

        max_wait = 120  # 2 minutes
        wait_interval = 10

        for i in range(0, max_wait, wait_interval):
            try:
                # Check ADB devices
                result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)

                # Look for any device that's online
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                online_devices = [line for line in lines if '\tdevice' in line]

                if online_devices:
                    # Use the first online device
                    self.device_id = online_devices[0].split('\t')[0]
                    logger.info(f"✅ Device found: {self.device_id}")

                    # Wait a bit more for full boot
                    time.sleep(10)
                    return True

                logger.info(f"⏳ Waiting for device... ({i + wait_interval}/{max_wait}s)")
                time.sleep(wait_interval)

            except Exception as e:
                logger.debug(f"Device check failed: {e}")
                time.sleep(wait_interval)

        logger.error("❌ Device did not become ready within timeout")
        return False

    def _verify_adb_connection(self) -> bool:
        """Verify ADB connection is working"""
        logger.info("🔗 Verifying ADB connection...")

        if not self.device_id:
            logger.error("❌ No device ID available")
            return False

        try:
            # Test basic ADB command
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'echo', 'test'
            ], capture_output=True, text=True, timeout=10)

            if result.returncode == 0 and 'test' in result.stdout:
                logger.info("✅ ADB connection verified")
                return True
            else:
                logger.error("❌ ADB connection test failed")
                return False

        except Exception as e:
            logger.error(f"❌ ADB connection error: {e}")
            return False

    def _verify_android_system(self) -> bool:
        """Verify Android system is fully booted"""
        logger.info("🤖 Verifying Android system...")

        try:
            # Check boot completion
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'getprop', 'sys.boot_completed'
            ], capture_output=True, text=True, timeout=10)

            if result.stdout.strip() == '1':
                logger.info("✅ Android system fully booted")
            else:
                logger.warning("⚠️ Android may still be booting")

            # Check Android version
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'getprop', 'ro.build.version.release'
            ], capture_output=True, text=True, timeout=10)

            android_version = result.stdout.strip()
            logger.info(f"📱 Android version: {android_version}")

            # Check device model
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'getprop', 'ro.product.model'
            ], capture_output=True, text=True, timeout=10)

            device_model = result.stdout.strip()
            logger.info(f"📱 Device model: {device_model}")

            return True

        except Exception as e:
            logger.error(f"❌ Android system verification failed: {e}")
            return False

    def _test_browser_automation(self) -> bool:
        """Test browser launch and Google.com navigation"""
        logger.info("🌐 Testing browser automation...")

        try:
            # Method 1: Try default browser intent
            logger.info("🔍 Attempting to launch browser with default intent...")
            result = subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'am', 'start',
                '-a', 'android.intent.action.VIEW',
                '-d', 'https://www.google.com'
            ], capture_output=True, text=True, timeout=15)

            if result.returncode == 0:
                logger.info("✅ Browser launched successfully")
                time.sleep(8)  # Wait for page load
                return True

            # Method 2: Try specific browser packages
            browser_packages = [
                'com.android.browser',
                'com.google.android.browser',
                'com.android.chrome'
            ]

            for package in browser_packages:
                logger.info(f"🔍 Trying browser package: {package}")
                try:
                    result = subprocess.run([
                        'adb', '-s', self.device_id, 'shell', 'am', 'start',
                        '-a', 'android.intent.action.VIEW',
                        '-d', 'https://www.google.com',
                        package
                    ], capture_output=True, text=True, timeout=15)

                    if result.returncode == 0:
                        logger.info(f"✅ Browser launched with {package}")
                        time.sleep(8)  # Wait for page load
                        return True

                except Exception as e:
                    logger.debug(f"Failed to launch {package}: {e}")
                    continue

            logger.error("❌ Could not launch any browser")
            return False

        except Exception as e:
            logger.error(f"❌ Browser automation failed: {e}")
            return False

    def _test_google_search(self) -> bool:
        """Test Google search functionality"""
        logger.info("🔍 Testing Google search...")

        try:
            # Wait for page to fully load
            time.sleep(5)

            # Take a screenshot for verification
            self._take_screenshot("before_search")

            # Tap on search box (HTC One coordinates: 540, 350)
            logger.info("👆 Tapping search box...")
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'tap', '540', '350'
            ], timeout=5)

            time.sleep(2)

            # Type search query
            search_query = "HTC One Android 14 automation test"
            logger.info(f"⌨️ Typing search query: {search_query}")
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'text', search_query
            ], timeout=10)

            time.sleep(2)

            # Press Enter
            logger.info("⏎ Pressing Enter...")
            subprocess.run([
                'adb', '-s', self.device_id, 'shell', 'input', 'keyevent', 'KEYCODE_ENTER'
            ], timeout=5)

            # Wait for search results
            time.sleep(5)

            # Take screenshot of results
            self._take_screenshot("search_results")

            logger.info("✅ Google search completed successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Google search failed: {e}")
            return False

    def _take_screenshot(self, name: str) -> bool:
        """Take a screenshot for verification"""
        try:
            screenshot_path = f"/tmp/htc_test_{name}_{int(time.time())}.png"
            result = subprocess.run([
                'adb', '-s', self.device_id, 'exec-out', 'screencap', '-p'
            ], capture_output=True, timeout=10)

            if result.returncode == 0:
                with open(screenshot_path, 'wb') as f:
                    f.write(result.stdout)
                logger.info(f"📸 Screenshot saved: {screenshot_path}")
                return True
            else:
                logger.warning("⚠️ Screenshot failed")
                return False

        except Exception as e:
            logger.debug(f"Screenshot error: {e}")
            return False

    def _cleanup_resources(self) -> bool:
        """Cleanup test resources"""
        logger.info("🧹 Cleaning up resources...")

        try:
            # Stop Appium if running
            if hasattr(self, 'appium_manager'):
                try:
                    self.appium_manager.stop_server()
                    logger.info("✅ Appium server stopped")
                except:
                    pass

            # Optionally stop the device (uncomment if you want to stop it)
            # if self.cleanup_needed and self.test_instance_name:
            #     try:
            #         self.genymotion_manager.stop_instance(self.test_instance_name)
            #         logger.info(f"✅ Device '{self.test_instance_name}' stopped")
            #     except Exception as e:
            #         logger.warning(f"⚠️ Failed to stop device: {e}")

            if self.cleanup_needed:
                logger.info(f"💡 Device '{self.test_instance_name}' is still running")
                logger.info(f"💡 To stop it manually: gmtool admin stop \"{self.test_instance_name}\"")
                logger.info(f"💡 To delete it: gmtool admin delete \"{self.test_instance_name}\"")

            logger.info("✅ Cleanup completed")
            return True

        except Exception as e:
            logger.error(f"❌ Cleanup error: {e}")
            return False


def main():
    """Main test function"""
    logger.info("=" * 70)
    logger.info("🧪 COMPLETE HTC ONE ANDROID 14.0 END-TO-END TEST")
    logger.info("=" * 70)
    logger.warning("⚠️ This test creates REAL virtual devices!")
    logger.warning("⚠️ Ensure you have sufficient system resources")
    logger.info("=" * 70)

    # Check for auto-run flag
    auto_run = '--auto' in sys.argv or '--yes' in sys.argv

    if not auto_run:
        # Ask for confirmation
        try:
            response = input("\n🤔 Do you want to proceed with the complete test? (y/N): ")
            if response.lower() != 'y':
                logger.info("❌ Test cancelled by user")
                return
        except KeyboardInterrupt:
            logger.info("\n❌ Test cancelled by user")
            return
    else:
        logger.info("🚀 Auto-run mode enabled, proceeding with test...")

    test = CompleteHTCScenarioTest()
    success = test.run_complete_test()

    logger.info("\n" + "=" * 70)
    if success:
        logger.info("🎉 COMPLETE TEST PASSED!")
        logger.info("✅ HTC One Android 14.0 device created and tested successfully")
        logger.info("✅ Browser automation working correctly")
        logger.info("✅ Google.com navigation and search completed")
    else:
        logger.error("❌ COMPLETE TEST FAILED!")
        logger.error("Check the logs above for specific failure points")

    logger.info("=" * 70)


if __name__ == "__main__":
    main()
