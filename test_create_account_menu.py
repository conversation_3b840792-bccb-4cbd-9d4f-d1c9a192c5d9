#!/usr/bin/env python3
"""
Test Create Account Menu Flow
Test the two-step process: Create Account → For my personal use
"""

import sys
import os
import time
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator
from genymotion_manager import Genymotion<PERSON>anager


def test_create_account_menu():
    """Test the Create Account menu flow"""
    print("🔘 Testing Create Account Menu Flow")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    gmail_creator = GmailAccountCreator()
    
    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()
    
    running_device = None
    device_id = None
    
    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not running_device:
        print("❌ No running device found")
        return False
    
    # Step 1: Navigate to Gmail
    print("\n🌐 Step 1: Navigating to Gmail...")
    navigation_success = gmail_creator.navigate_to_gmail_signup(device_id)
    
    if not navigation_success:
        print("❌ Navigation failed")
        return False
    
    print("✅ Navigation completed")
    time.sleep(5)
    
    # Take initial screenshot
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/01_initial_page.png', 
                     shell=True, timeout=10)
        print("📸 Initial page: /tmp/01_initial_page.png")
    except:
        pass
    
    # Step 2: Click Create Account
    print("\n🔘 Step 2: Clicking Create Account...")
    
    create_account_patterns = ['create account', 'hesap oluştur', 'create']
    
    if gmail_creator._click_element_with_ocr(device_id, create_account_patterns, "create account button"):
        print("✅ Successfully clicked Create Account with OCR")
    else:
        print("🔘 OCR failed, trying coordinate-based clicking...")
        # Try bottom-left area where Create Account typically appears
        coords = [(119, 849), (150, 850), (100, 800)]  # Based on your screenshot
        
        clicked_successfully = False
        for i, (x, y) in enumerate(coords):
            print(f"   Trying Create Account position {i+1}: ({x}, {y})")
            subprocess.run(f'adb -s {device_id} shell input tap {x} {y}', shell=True, timeout=5)
            time.sleep(3)
            
            # Check if menu appeared by looking for "For my personal use"
            if check_for_menu(device_id):
                print(f"✅ Successfully clicked Create Account at position {i+1}")
                clicked_successfully = True
                break
        
        if not clicked_successfully:
            print("❌ Could not click Create Account")
            return False
    
    time.sleep(3)
    
    # Take screenshot after Create Account click
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/02_after_create_account.png', 
                     shell=True, timeout=10)
        print("📸 After Create Account: /tmp/02_after_create_account.png")
    except:
        pass
    
    # Step 3: Click "For my personal use"
    print("\n👤 Step 3: Clicking 'For my personal use'...")
    
    personal_use_patterns = ['for my personal use', 'personal use', 'personal']
    
    if gmail_creator._click_element_with_ocr(device_id, personal_use_patterns, "personal use option"):
        print("✅ Successfully clicked 'For my personal use' with OCR")
    else:
        print("🔘 OCR failed, trying coordinate-based clicking...")
        # Based on your screenshot, "For my personal use" is at approximately (143, 620)
        personal_coords = [(143, 620), (150, 620), (200, 620)]
        
        clicked_successfully = False
        for i, (x, y) in enumerate(personal_coords):
            print(f"   Trying personal use position {i+1}: ({x}, {y})")
            subprocess.run(f'adb -s {device_id} shell input tap {x} {y}', shell=True, timeout=5)
            time.sleep(3)
            
            # Check if we reached the signup form
            if gmail_creator._check_for_signup_form(device_id):
                print(f"✅ Successfully clicked 'For my personal use' at position {i+1}")
                clicked_successfully = True
                break
        
        if not clicked_successfully:
            print("❌ Could not click 'For my personal use'")
            return False
    
    time.sleep(3)
    
    # Take final screenshot
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/03_signup_form.png', 
                     shell=True, timeout=10)
        print("📸 Signup form: /tmp/03_signup_form.png")
    except:
        pass
    
    # Step 4: Verify we're on the signup form
    print("\n✅ Step 4: Verifying signup form...")
    
    if gmail_creator._check_for_signup_form(device_id):
        print("✅ Successfully reached Gmail signup form!")
        return True
    else:
        print("❌ Did not reach signup form")
        return False


def check_for_menu(device_id: str) -> bool:
    """Check if the Create Account menu appeared"""
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            ui_content = result.stdout.lower()
            menu_indicators = ['for my personal use', 'for my child', 'for work']
            return any(indicator in ui_content for indicator in menu_indicators)
        
        return False
    except:
        return False


def main():
    """Main test execution"""
    success = test_create_account_menu()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Create Account menu flow test PASSED!")
        print("🎉 Successfully navigated: Gmail → Create Account → For my personal use → Signup Form")
    else:
        print("❌ Create Account menu flow test FAILED")
        print("🔍 Check the screenshots for debugging:")
        print("   - /tmp/01_initial_page.png")
        print("   - /tmp/02_after_create_account.png") 
        print("   - /tmp/03_signup_form.png")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
