#!/usr/bin/env python3
"""
Test Direct Signup URL
Test if the direct signup URL lands us on the signup form
"""

import sys
import os
import time
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import Genymotion<PERSON>ana<PERSON>


def test_direct_signup_url():
    """Test the direct signup URL navigation"""
    print("🔗 Testing Direct Signup URL")
    print("=" * 50)

    # Initialize components
    genymotion = GenymotionManager()

    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()

    running_device = None
    device_id = None

    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break

    if not running_device:
        print("❌ No running device found")
        return False

    # Step 1: Navigate directly to Gmail signup URL using intent
    print("\n🌐 Step 1: Navigating directly to Gmail signup URL...")

    direct_url = "https://accounts.google.com/signup/v2/webcreateaccount?flowName=GlifWebSignIn&flowEntry=SignUp"
    print(f"   Using direct intent with URL: {direct_url}")

    # Use intent to open the URL directly in the default browser
    intent_cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "{direct_url}"'
    intent_result = subprocess.run(intent_cmd, shell=True, capture_output=True, text=True, timeout=15)

    if intent_result.returncode == 0:
        print("✅ Intent launched successfully")
        print(f"   Intent output: {intent_result.stdout.strip()}")
    else:
        print("❌ Intent failed")
        print(f"   Error: {intent_result.stderr}")
        return False

    # Wait for page to load
    print("   Waiting for page to load...")
    time.sleep(15)  # Give more time for the page to fully load

    # Take screenshot
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/direct_signup_result.png',
                     shell=True, timeout=10)
        print("📸 Direct signup result: /tmp/direct_signup_result.png")
    except:
        pass

    # Step 2: Analyze what page we landed on
    print("\n🔍 Step 2: Analyzing the page...")

    # Get UI dump to analyze content
    try:
        cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=15)

        if result.returncode == 0:
            ui_content = result.stdout.lower()

            # Check for signup form indicators
            signup_indicators = [
                'first name', 'last name', 'choose username', 'choose your username',
                'firstname', 'lastname', 'username', 'create account'
            ]

            # Check for signin page indicators
            signin_indicators = [
                'sign in', 'email or phone', 'for my personal use', 'for my child'
            ]

            found_signup = [indicator for indicator in signup_indicators if indicator in ui_content]
            found_signin = [indicator for indicator in signin_indicators if indicator in ui_content]

            print(f"📊 Found signup indicators: {found_signup}")
            print(f"📊 Found signin indicators: {found_signin}")

            if found_signup and not found_signin:
                print("✅ SUCCESS: Landed directly on signup form!")
                return True
            elif found_signin:
                print("⚠️ WARNING: Landed on signin page, need to click Create Account")

                # Look for Create Account button
                if 'create account' in ui_content:
                    print("✅ Found 'Create Account' button on signin page")
                    return "signin_with_create_account"
                else:
                    print("❌ No 'Create Account' button found")
                    return False
            else:
                print("❓ UNKNOWN: Could not determine page type")
                print("Raw UI content (first 500 chars):")
                print(ui_content[:500])
                return False
        else:
            print("❌ Could not get UI dump")
            return False

    except Exception as e:
        print(f"❌ Error analyzing page: {e}")
        return False


def main():
    """Main test execution"""
    result = test_direct_signup_url()

    print("\n" + "=" * 50)
    if result == True:
        print("✅ Direct signup URL test PASSED!")
        print("🎉 Successfully landed directly on Gmail signup form!")
    elif result == "signin_with_create_account":
        print("⚠️ Direct signup URL test PARTIAL SUCCESS")
        print("📝 Landed on signin page but found Create Account button")
        print("💡 Need to implement Create Account clicking logic")
    else:
        print("❌ Direct signup URL test FAILED")
        print("🔍 Check the screenshot: /tmp/direct_signup_result.png")

    return 0 if result else 1


if __name__ == "__main__":
    exit(main())
