#!/usr/bin/env python3
"""
End-to-End Device Creation and Anti-Detection Spoofing Test
Production-ready test to validate complete spoofing system
"""

import sys
import time
import json
import subprocess
from pathlib import Path
from loguru import logger

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

from genymotion_manager import GenymotionManager
from database import DatabaseManager


class EndToEndSpoofingTest:
    """Comprehensive end-to-end spoofing validation test"""

    def __init__(self):
        self.genymotion_manager = GenymotionManager()
        self.db_manager = DatabaseManager()
        self.test_device_name = f"E2E_Test_Fire7_{int(time.time())}"
        self.test_results = {
            'device_creation': False,
            'device_startup': False,
            'widget_customization': False,
            'magisk_installation': False,
            'frida_installation': False,
            'anti_detection_verification': False,
            'property_verification': False,
            'file_verification': False,
            'overall_success': False
        }

    def run_complete_test(self) -> dict:
        """Run complete end-to-end spoofing test"""
        logger.info("🚀 Starting End-to-End Anti-Detection Spoofing Test")
        logger.info("=" * 80)

        try:
            # Phase 1: Device Creation
            if not self._test_device_creation():
                return self._finalize_results("Device creation failed")

            # Phase 2: Device Startup
            if not self._test_device_startup():
                return self._finalize_results("Device startup failed")

            # Phase 3: Widget Customization
            if not self._test_widget_customization():
                return self._finalize_results("Widget customization failed")

            # Phase 4: Magisk Anti-Detection Setup
            if not self._test_magisk_setup():
                return self._finalize_results("Magisk setup failed")

            # Phase 5: Frida Installation
            if not self._test_frida_setup():
                return self._finalize_results("Frida setup failed")

            # Phase 6: Comprehensive Verification
            if not self._test_anti_detection_verification():
                return self._finalize_results("Anti-detection verification failed")

            # Phase 7: Detailed Property Verification
            if not self._test_property_verification():
                return self._finalize_results("Property verification failed")

            # Phase 8: File System Verification
            if not self._test_file_verification():
                return self._finalize_results("File verification failed")

            return self._finalize_results("All tests passed", success=True)

        except Exception as e:
            logger.error(f"❌ Test execution failed: {e}")
            return self._finalize_results(f"Test execution error: {e}")

        finally:
            # Cleanup
            self._cleanup_test_device()

    def _test_device_creation(self) -> bool:
        """Test device creation with root access"""
        logger.info("📱 Phase 1: Testing Device Creation")

        try:
            # Create device with root access
            success = self.genymotion_manager.create_new_instance(
                instance_name=self.test_device_name,
                android_version="14"  # Android 14
            )

            if success:
                logger.info("✅ Device creation successful")
                self.test_results['device_creation'] = True
                return True
            else:
                logger.error("❌ Device creation failed")
                return False

        except Exception as e:
            logger.error(f"❌ Device creation error: {e}")
            return False

    def _test_device_startup(self) -> bool:
        """Test device startup and ADB connectivity"""
        logger.info("🔄 Phase 2: Testing Device Startup")

        try:
            # Start the device
            success = self.genymotion_manager.start_instance(self.test_device_name)

            if success:
                # Wait for device to be fully ready (Android 14 takes longer)
                logger.info("⏳ Waiting for device to be fully ready...")
                time.sleep(60)  # Wait longer for Android 14

                # Verify ADB connectivity
                device_id = self.genymotion_manager.get_device_adb_id(self.test_device_name)
                if device_id:
                    logger.info(f"✅ Device startup successful - ADB ID: {device_id}")
                    self.test_results['device_startup'] = True
                    return True
                else:
                    logger.error("❌ Device started but ADB connectivity failed")
                    return False
            else:
                logger.error("❌ Device startup failed")
                return False

        except Exception as e:
            logger.error(f"❌ Device startup error: {e}")
            return False

    def _test_widget_customization(self) -> bool:
        """Test Genymotion widget customization"""
        logger.info("🔧 Phase 3: Testing Widget Customization")

        try:
            # Test phone number for widget customization
            test_phone = "**************"

            # Run widget customization
            success = self.genymotion_manager.customize_device_identifiers(
                instance_name=self.test_device_name,
                phone_number=test_phone
            )

            if success:
                logger.info("✅ Widget customization successful")
                self.test_results['widget_customization'] = True
                return True
            else:
                logger.error("❌ Widget customization failed")
                return False

        except Exception as e:
            logger.error(f"❌ Widget customization error: {e}")
            return False

    def _test_magisk_setup(self) -> bool:
        """Test Magisk anti-detection module installation"""
        logger.info("🛡️ Phase 4: Testing Magisk Anti-Detection Setup")

        try:
            # Install advanced Magisk spoofing
            success = self.genymotion_manager.install_advanced_magisk_spoofing(self.test_device_name)

            if success:
                logger.info("✅ Magisk module installation successful")

                # Reboot device to activate modules
                reboot_success = self.genymotion_manager.reboot_device_for_magisk(self.test_device_name)
                if reboot_success:
                    logger.info("✅ Device reboot successful")
                    self.test_results['magisk_installation'] = True
                    return True
                else:
                    logger.warning("⚠️ Magisk installed but reboot failed")
                    return False
            else:
                logger.error("❌ Magisk installation failed")
                return False

        except Exception as e:
            logger.error(f"❌ Magisk setup error: {e}")
            return False

    def _test_frida_setup(self) -> bool:
        """Test Frida server installation and script creation"""
        logger.info("🔍 Phase 5: Testing Frida Setup")

        try:
            # Install Frida server
            frida_success = self.genymotion_manager.install_frida_server(self.test_device_name)

            if frida_success:
                logger.info("✅ Frida server installation successful")

                # Create bypass script
                script_success = self.genymotion_manager.create_frida_bypass_script(self.test_device_name)
                if script_success:
                    logger.info("✅ Frida bypass script creation successful")
                    self.test_results['frida_installation'] = True
                    return True
                else:
                    logger.warning("⚠️ Frida server installed but script creation failed")
                    return False
            else:
                logger.warning("⚠️ Frida installation failed - may require manual setup")
                # Don't fail the test for Frida as it may require manual installation
                return True

        except Exception as e:
            logger.error(f"❌ Frida setup error: {e}")
            return True  # Don't fail test for Frida issues

    def _test_anti_detection_verification(self) -> bool:
        """Test comprehensive anti-detection verification"""
        logger.info("🔍 Phase 6: Testing Anti-Detection Verification")

        try:
            # Run comprehensive verification
            verification = self.genymotion_manager.verify_anti_detection_setup(self.test_device_name)

            if verification and verification.get('overall_success'):
                logger.info("✅ Anti-detection verification successful")
                self.test_results['anti_detection_verification'] = True
                return True
            else:
                logger.warning("⚠️ Anti-detection verification partial or failed")
                # Log detailed results
                if verification:
                    for key, value in verification.items():
                        status = "✅" if value else "❌"
                        logger.info(f"  {status} {key}: {value}")
                return False

        except Exception as e:
            logger.error(f"❌ Anti-detection verification error: {e}")
            return False

    def _test_property_verification(self) -> bool:
        """Test detailed property verification"""
        logger.info("📋 Phase 7: Testing Property Verification")

        try:
            device_id = self.genymotion_manager.get_device_adb_id(self.test_device_name)
            if not device_id:
                logger.error("❌ Could not get device ADB ID")
                return False

            # Test critical properties
            critical_properties = {
                'ro.product.manufacturer': 'Amazon',
                'ro.product.model': 'Fire 7',
                'ro.product.brand': 'Amazon',
                'ro.hardware': 'qcom',
                'ro.kernel.qemu': '0',
                'ro.genymotion.device.version': '',
                'ro.genyd.caps.baseband': ''
            }

            success_count = 0
            total_count = len(critical_properties)

            for prop, expected in critical_properties.items():
                try:
                    cmd = f'adb -s {device_id} shell getprop {prop}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode == 0:
                        value = result.stdout.strip()

                        if expected == '':  # Should be empty
                            if not value:
                                logger.info(f"✅ {prop}: (empty) ✓")
                                success_count += 1
                            else:
                                logger.error(f"❌ {prop}: '{value}' (should be empty)")
                        else:  # Should contain expected value
                            if expected in value:
                                logger.info(f"✅ {prop}: '{value}' ✓")
                                success_count += 1
                            else:
                                logger.error(f"❌ {prop}: '{value}' (expected: {expected})")
                    else:
                        logger.error(f"❌ Failed to get property: {prop}")

                except Exception as e:
                    logger.error(f"❌ Error checking property {prop}: {e}")

            success_rate = success_count / total_count
            logger.info(f"📊 Property verification: {success_count}/{total_count} ({success_rate:.1%})")

            if success_rate >= 0.8:  # 80% success rate
                logger.info("✅ Property verification successful")
                self.test_results['property_verification'] = True
                return True
            else:
                logger.error("❌ Property verification failed")
                return False

        except Exception as e:
            logger.error(f"❌ Property verification error: {e}")
            return False

    def _test_file_verification(self) -> bool:
        """Test file system verification"""
        logger.info("📁 Phase 8: Testing File System Verification")

        try:
            device_id = self.genymotion_manager.get_device_adb_id(self.test_device_name)
            if not device_id:
                logger.error("❌ Could not get device ADB ID")
                return False

            # Test that emulator files are hidden
            emulator_files = [
                '/system/bin/qemu-props',
                '/system/lib/libc_malloc_debug_qemu.so',
                '/system/lib64/libc_malloc_debug_qemu.so'
            ]

            hidden_count = 0
            total_count = len(emulator_files)

            for file_path in emulator_files:
                try:
                    cmd = f'adb -s {device_id} shell ls {file_path}'
                    result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                    if result.returncode != 0:  # File doesn't exist (good)
                        logger.info(f"✅ Emulator file hidden: {file_path}")
                        hidden_count += 1
                    else:
                        logger.warning(f"⚠️ Emulator file still exists: {file_path}")

                except Exception as e:
                    logger.debug(f"Error checking file {file_path}: {e}")
                    hidden_count += 1  # Assume hidden if error

            # Test CPU info spoofing
            try:
                cmd = f'adb -s {device_id} shell cat /proc/cpuinfo | head -3'
                result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=5)

                if result.returncode == 0:
                    cpu_info = result.stdout.lower()
                    if 'armv8' in cpu_info or 'arm' in cpu_info:
                        logger.info("✅ CPU info shows ARM processor")
                        hidden_count += 1
                    else:
                        logger.warning(f"⚠️ CPU info suspicious: {cpu_info[:100]}")

            except Exception as e:
                logger.debug(f"Error checking CPU info: {e}")

            success_rate = hidden_count / (total_count + 1)  # +1 for CPU info
            logger.info(f"📊 File verification: {hidden_count}/{total_count + 1} ({success_rate:.1%})")

            if success_rate >= 0.75:  # 75% success rate
                logger.info("✅ File verification successful")
                self.test_results['file_verification'] = True
                return True
            else:
                logger.warning("⚠️ File verification partial")
                return False

        except Exception as e:
            logger.error(f"❌ File verification error: {e}")
            return False

    def _finalize_results(self, message: str, success: bool = False) -> dict:
        """Finalize test results"""
        self.test_results['overall_success'] = success

        logger.info("=" * 80)
        logger.info("📊 END-TO-END TEST RESULTS")
        logger.info("=" * 80)

        for phase, result in self.test_results.items():
            if phase != 'overall_success':
                status = "✅" if result else "❌"
                logger.info(f"  {status} {phase.replace('_', ' ').title()}: {result}")

        logger.info("=" * 80)

        if success:
            logger.info(f"✅ OVERALL RESULT: SUCCESS - {message}")
        else:
            logger.error(f"❌ OVERALL RESULT: FAILED - {message}")

        logger.info("=" * 80)

        return self.test_results

    def _cleanup_test_device(self):
        """Clean up test device"""
        try:
            logger.info(f"🧹 Cleaning up test device: {self.test_device_name}")

            # Stop device
            self.genymotion_manager.stop_instance(self.test_device_name)

            # Delete device
            self.genymotion_manager.delete_instance(self.test_device_name)

            logger.info("✅ Test device cleanup completed")

        except Exception as e:
            logger.warning(f"⚠️ Cleanup error: {e}")


def main():
    """Run end-to-end spoofing test"""
    logger.info("🚀 Starting End-to-End Anti-Detection Spoofing Test")

    # Create and run test
    test = EndToEndSpoofingTest()
    results = test.run_complete_test()

    # Save results
    results_file = f"test_results_{int(time.time())}.json"
    with open(results_file, 'w') as f:
        json.dump(results, f, indent=2)

    logger.info(f"📄 Test results saved to: {results_file}")

    # Exit with appropriate code
    exit_code = 0 if results.get('overall_success') else 1
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
