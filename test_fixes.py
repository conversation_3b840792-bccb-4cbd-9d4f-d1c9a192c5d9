#!/usr/bin/env python3
"""
Simple test script to verify the fixes work without requiring all dependencies
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent / "src"))

def test_database_datetime_fix():
    """Test that database datetime handling is fixed"""
    print("Testing database datetime fix...")

    try:
        from database import DatabaseManager

        # Create database manager
        db_manager = DatabaseManager("sqlite:///test_automation.db")

        # Test session data with datetime object
        session_data = {
            'session_id': 'test-session-123',
            'device_name': 'Test Device',
            'device_model': 'Google Pixel 6',
            'platform_version': '13',
            'android_id': '1234567890abcdef',
            'imei': '123456789012345',
            'advertising_id': '12345678-1234-1234-1234-123456789012',
            'location_name': 'test_location',
            'latitude': 40.7128,
            'longitude': -74.0060,
            'start_time': datetime.utcnow()  # This should work now
        }

        # This should not fail with datetime error
        result = db_manager.create_automation_session(session_data)

        if result:
            print("✅ Database datetime fix working - session created successfully")
        else:
            print("❌ Database datetime fix failed - session creation failed")

        return result

    except Exception as e:
        print(f"❌ Database datetime fix failed with error: {e}")
        return False

def test_device_manager_error_handling():
    """Test that device manager has better error handling"""
    print("Testing device manager error handling...")

    try:
        from device_manager import DeviceProfileManager

        # Create device manager
        device_manager = DeviceProfileManager()

        # Create a test session
        session_id, profile = device_manager.create_new_session()
        print(f"✅ Device session created: {session_id}")

        # Test setup with non-existent session (should handle gracefully)
        result = device_manager.setup_device_session("non-existent-session")
        if not result:
            print("✅ Error handling working - gracefully handled non-existent session")
        else:
            print("❌ Error handling issue - should have returned False for non-existent session")

        return True

    except Exception as e:
        print(f"❌ Device manager error handling test failed: {e}")
        return False

def test_genymotion_manager():
    """Test Genymotion manager improvements"""
    print("Testing Genymotion manager...")

    try:
        from genymotion_manager import GenymotionManager

        # Create Genymotion manager
        gm_manager = GenymotionManager()

        # Test device profile generation
        profile = gm_manager.get_random_device_profile()
        print(f"✅ Device profile generated: {profile.get('deviceName', 'Unknown')}")

        # Test instance configuration (should not fail even without Genymotion)
        result = gm_manager.configure_genymotion_instance("TestInstance", profile)
        print(f"✅ Genymotion configuration result: {result}")

        return True

    except Exception as e:
        print(f"❌ Genymotion manager test failed: {e}")
        return False

def test_appium_server_improvements():
    """Test Appium server improvements"""
    print("Testing Appium server improvements...")

    try:
        from appium_server import AppiumServerManager

        # Create Appium server manager
        appium_manager = AppiumServerManager()

        # Test device ID detection (should not fail even without ADB)
        device_id = appium_manager._get_available_device_id()
        print(f"✅ Device ID detection completed: {device_id or 'No devices found'}")

        # Test capabilities generation
        test_profile = {
            'platformName': 'Android',
            'platformVersion': '13',
            'deviceName': 'Test Device',
            'udid': 'emulator-5554',
            'automationName': 'UiAutomator2'
        }

        capabilities = appium_manager.generate_capabilities(test_profile)
        print(f"✅ Capabilities generated with UDID: {capabilities.get('udid')}")

        return True

    except Exception as e:
        print(f"❌ Appium server test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 Running BlueStacks Automation System Fix Tests\n")

    tests = [
        test_database_datetime_fix,
        test_device_manager_error_handling,
        test_bluestacks_manager,
        test_appium_server_improvements
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}\n")

    print(f"📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All fixes are working correctly!")
        return True
    else:
        print("⚠️  Some issues remain, but system should be more stable")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
