#!/usr/bin/env python3
"""
Test Gmail API Endpoints
Tests the Gmail automation API endpoints
"""

import requests
import json
import time
import sys


def test_api_endpoints():
    """Test Gmail automation API endpoints"""
    base_url = "http://localhost:8000"
    
    print("🧪 Testing Gmail Automation API Endpoints")
    print("=" * 50)
    
    # Test 1: Check API status
    print("\n1️⃣ Testing API Status...")
    try:
        response = requests.get(f"{base_url}/api/status")
        if response.status_code == 200:
            status = response.json()
            print(f"✅ API Status: {status}")
        else:
            print(f"❌ API Status failed: {response.status_code}")
    except Exception as e:
        print(f"❌ API Status error: {e}")
    
    # Test 2: Test Turkish data generation
    print("\n2️⃣ Testing Turkish Data Generation...")
    try:
        response = requests.post(f"{base_url}/api/gmail/test-data")
        if response.status_code == 200:
            data = response.json()
            print("✅ Turkish Data Generation:")
            for i, profile in enumerate(data['test_profiles'], 1):
                print(f"   Profile {i}:")
                print(f"     Name: {profile['first_name']} {profile['last_name']}")
                print(f"     Username: {profile['username']}")
                print(f"     Phone: {profile['phone_number']}")
        else:
            print(f"❌ Turkish data test failed: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Turkish data test error: {e}")
    
    # Test 3: Get existing accounts
    print("\n3️⃣ Testing Get Accounts...")
    try:
        response = requests.get(f"{base_url}/api/gmail/accounts?limit=5")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {data['total_accounts']} existing accounts")
            if data['accounts']:
                print("   Recent accounts:")
                for account in data['accounts'][:3]:
                    print(f"     📧 {account['email']} - {account['first_name']} {account['last_name']}")
            else:
                print("   No accounts found")
        else:
            print(f"❌ Get accounts failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Get accounts error: {e}")
    
    # Test 4: Create single Gmail account (optional - requires user confirmation)
    print("\n4️⃣ Testing Single Gmail Account Creation...")
    create_account = input("Do you want to test creating a real Gmail account? (y/N): ").lower().strip()
    
    if create_account == 'y':
        print("🚀 Creating Gmail account...")
        try:
            payload = {
                "count": 1,
                "use_turkish_data": True,
                "cleanup_device": True,
                "save_to_file": True
            }
            
            response = requests.post(
                f"{base_url}/api/gmail/create",
                json=payload,
                timeout=600  # 10 minutes timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Gmail Account Creation Result:")
                print(f"   Success: {result['success']}")
                print(f"   Success Rate: {result['success_rate']:.1f}%")
                
                if result['successful_accounts']:
                    for account in result['successful_accounts']:
                        print(f"   📧 Created: {account['email']}")
                        print(f"   👤 Name: {account['first_name']} {account['last_name']}")
                
                if result['failed_accounts']:
                    print(f"   ❌ Failed accounts: {len(result['failed_accounts'])}")
                    for failed in result['failed_accounts']:
                        print(f"     Error: {failed['error']}")
            else:
                print(f"❌ Gmail creation failed: {response.status_code}")
                print(f"Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Gmail creation error: {e}")
    else:
        print("⏭️ Skipping real account creation")
    
    print("\n" + "=" * 50)
    print("🏁 API Testing Completed!")


def test_websocket_connection():
    """Test WebSocket connection for real-time updates"""
    print("\n🔌 Testing WebSocket Connection...")
    
    try:
        import websocket
        
        def on_message(ws, message):
            data = json.loads(message)
            print(f"📨 WebSocket Message: {data}")
        
        def on_error(ws, error):
            print(f"❌ WebSocket Error: {error}")
        
        def on_close(ws, close_status_code, close_msg):
            print("🔌 WebSocket Connection Closed")
        
        def on_open(ws):
            print("✅ WebSocket Connected")
        
        ws_url = "ws://localhost:8000/ws"
        ws = websocket.WebSocketApp(
            ws_url,
            on_open=on_open,
            on_message=on_message,
            on_error=on_error,
            on_close=on_close
        )
        
        print(f"Connecting to {ws_url}...")
        ws.run_forever()
        
    except ImportError:
        print("⚠️ websocket-client not installed. Install with: pip install websocket-client")
    except Exception as e:
        print(f"❌ WebSocket test error: {e}")


def show_api_documentation():
    """Show API documentation"""
    print("\n📚 Gmail Automation API Documentation")
    print("=" * 50)
    
    endpoints = [
        {
            "method": "POST",
            "path": "/api/gmail/create",
            "description": "Create single or multiple Gmail accounts",
            "payload": {
                "count": 1,
                "use_turkish_data": True,
                "cleanup_device": True,
                "save_to_file": True
            }
        },
        {
            "method": "POST", 
            "path": "/api/gmail/batch",
            "description": "Create multiple Gmail accounts with delays",
            "payload": {
                "count": 3,
                "delay_between_accounts": 30,
                "use_turkish_data": True,
                "cleanup_devices": True
            }
        },
        {
            "method": "GET",
            "path": "/api/gmail/accounts?limit=50",
            "description": "Get list of created Gmail accounts",
            "payload": None
        },
        {
            "method": "POST",
            "path": "/api/gmail/test-data", 
            "description": "Test Turkish data generation without creating accounts",
            "payload": None
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n🔗 {endpoint['method']} {endpoint['path']}")
        print(f"   📝 {endpoint['description']}")
        if endpoint['payload']:
            print(f"   📦 Payload: {json.dumps(endpoint['payload'], indent=6)}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "docs":
            show_api_documentation()
        elif sys.argv[1] == "websocket":
            test_websocket_connection()
        else:
            print("Usage: python test_gmail_api.py [docs|websocket]")
    else:
        test_api_endpoints()
