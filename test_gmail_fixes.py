#!/usr/bin/env python3
"""
Test Gmail Fixes
Test the improved Gmail automation with proper Create Account clicking and field targeting
"""

import sys
import os
import time

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator, PersonalInfo
from genymotion_manager import GenymotionManager


def test_gmail_fixes():
    """Test the Gmail automation fixes"""
    print("🔧 Testing Gmail Automation Fixes")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    gmail_creator = GmailAccountCreator()
    
    # Find running device
    print("🔍 Finding running device...")
    instances = genymotion.get_available_instances()
    
    running_device = None
    device_id = None
    
    for name, info in instances.items():
        if info.get('status') == 'running':
            running_device = name
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not running_device:
        print("❌ No running device found")
        return False
    
    # Create test personal info
    print("👤 Creating test personal info...")
    test_info = PersonalInfo(
        first_name="Mehmet",
        last_name="Özkan", 
        username="mehmetozkan" + str(int(time.time()))[-4:],
        password="TestPassword123!",
        phone_number="+************",
        birth_date="1985-05-15",
        recovery_email="<EMAIL>"
    )
    
    print(f"   First Name: {test_info.first_name}")
    print(f"   Last Name: {test_info.last_name}")
    print(f"   Username: {test_info.username}")
    
    # Test Step 1: Navigation to Gmail signup
    print("\n🌐 Step 1: Testing Gmail navigation...")
    navigation_success = gmail_creator.navigate_to_gmail_signup(device_id)
    
    if navigation_success:
        print("✅ Navigation completed")
    else:
        print("❌ Navigation failed")
        return False
    
    # Wait for page to load
    print("⏳ Waiting for page to load...")
    time.sleep(5)
    
    # Test Step 2: Check if we're on signin page and need to click Create Account
    print("\n🔍 Step 2: Checking page type...")
    
    # Take screenshot before Create Account click
    try:
        import subprocess
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/before_create_account.png', 
                     shell=True, timeout=10)
        print("📸 Screenshot before Create Account: /tmp/before_create_account.png")
    except:
        pass
    
    # Check if we need to click Create Account
    if gmail_creator._check_for_signup_form(device_id):
        print("✅ Already on signup form")
    else:
        print("🔘 On signin form, need to click Create Account")
        
        # Try to click Create Account
        create_account_patterns = ['create account', 'hesap oluştur', 'create', 'sign up']
        
        if gmail_creator._click_element_with_ocr(device_id, create_account_patterns, "create account button"):
            print("✅ Successfully clicked Create Account with OCR")
        else:
            print("🔘 OCR failed, trying coordinate-based clicking...")
            # Try bottom-left coordinates
            coords = [(150, 1100), (200, 1050), (100, 1150)]
            
            for i, (x, y) in enumerate(coords):
                print(f"   Trying position {i+1}: ({x}, {y})")
                subprocess.run(f'adb -s {device_id} shell input tap {x} {y}', shell=True, timeout=5)
                time.sleep(3)
                
                if gmail_creator._check_for_signup_form(device_id):
                    print(f"✅ Successfully clicked Create Account at position {i+1}")
                    break
            else:
                print("❌ Could not find Create Account button")
                return False
    
    # Take screenshot after Create Account click
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/after_create_account.png', 
                     shell=True, timeout=10)
        print("📸 Screenshot after Create Account: /tmp/after_create_account.png")
    except:
        pass
    
    # Test Step 3: Fill form fields with targeted approach
    print("\n✏️ Step 3: Testing targeted form filling...")
    
    # Test first name
    print(f"📝 Testing first name field: {test_info.first_name}")
    first_name_success = gmail_creator._fill_targeted_field(device_id, test_info.first_name, "first_name")
    
    # Test last name  
    print(f"📝 Testing last name field: {test_info.last_name}")
    last_name_success = gmail_creator._fill_targeted_field(device_id, test_info.last_name, "last_name")
    
    # Test username
    print(f"📝 Testing username field: {test_info.username}")
    username_success = gmail_creator._fill_targeted_field(device_id, test_info.username, "username")
    
    # Take final screenshot
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/form_filled.png', 
                     shell=True, timeout=10)
        print("📸 Screenshot after form filling: /tmp/form_filled.png")
    except:
        pass
    
    # Results
    print(f"\n📊 Form Filling Results:")
    print(f"   First name: {'✅' if first_name_success else '❌'}")
    print(f"   Last name: {'✅' if last_name_success else '❌'}")
    print(f"   Username: {'✅' if username_success else '❌'}")
    
    success_count = sum([first_name_success, last_name_success, username_success])
    
    if success_count >= 2:
        print(f"✅ Form filling mostly successful ({success_count}/3)")
        return True
    else:
        print(f"❌ Form filling failed ({success_count}/3)")
        return False


def main():
    """Main test execution"""
    success = test_gmail_fixes()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ Gmail fixes test completed successfully!")
        print("🎉 The Create Account clicking and form field targeting are working!")
    else:
        print("❌ Gmail fixes test failed")
        print("🔍 Check the screenshots for debugging:")
        print("   - /tmp/before_create_account.png")
        print("   - /tmp/after_create_account.png") 
        print("   - /tmp/form_filled.png")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
