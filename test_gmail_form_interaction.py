#!/usr/bin/env python3
"""
Gmail Form Interaction Test
Tests Gmail account creation form field detection and input on any available device
"""

import sys
import os
import time
import subprocess
import json
from typing import Dict, List, Optional

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager
from gmail_account_creator import GmailAccountCreator


class GmailFormTester:
    def __init__(self):
        self.genymotion = GenymotionManager()
        self.gmail_creator = GmailAccountCreator()
        self.device_id = None
        self.device_name = None

    def find_available_device(self) -> Optional[str]:
        """Find any available device (running or stopped)"""
        print("🔍 Looking for available devices...")

        try:
            instances_dict = self.genymotion.get_available_instances()

            # First, try to find a running device
            for device_name, instance in instances_dict.items():
                if instance.get('status') == 'running':
                    print(f"✅ Found running device: {device_name}")
                    return device_name

            # If no running device, find any stopped device
            for device_name, instance in instances_dict.items():
                if instance.get('status') == 'stopped':
                    print(f"📱 Found stopped device: {device_name}")
                    return device_name

            print("❌ No devices found")
            return None

        except Exception as e:
            print(f"❌ Error finding devices: {e}")
            return None

    def ensure_device_running(self, device_name: str) -> bool:
        """Ensure the device is running"""
        print(f"🚀 Ensuring device is running: {device_name}")

        try:
            instances_dict = self.genymotion.get_available_instances()
            device_info = instances_dict.get(device_name)

            if not device_info:
                print(f"❌ Device {device_name} not found")
                return False

            device_status = device_info.get('status')

            if device_status == 'running':
                print(f"✅ Device {device_name} is already running")
                return True
            elif device_status == 'stopped':
                print(f"🔄 Starting device {device_name}...")
                success = self.genymotion.start_instance_by_name(device_name)
                if success:
                    print(f"✅ Device {device_name} started successfully")
                    # Wait for device to be ready
                    print("⏳ Waiting for device to be ready...")
                    time.sleep(30)
                    return True
                else:
                    print(f"❌ Failed to start device {device_name}")
                    return False
            else:
                print(f"❌ Device {device_name} in unknown status: {device_status}")
                return False

        except Exception as e:
            print(f"❌ Error ensuring device running: {e}")
            return False

    def get_device_adb_id(self, device_name: str) -> Optional[str]:
        """Get ADB ID for the device"""
        try:
            device_id = self.genymotion.get_device_adb_id(device_name)
            print(f"📱 Device ADB ID: {device_id}")
            return device_id
        except Exception as e:
            print(f"❌ Error getting device ADB ID: {e}")
            return None

    def test_device_connectivity(self, device_id: str) -> bool:
        """Test basic device connectivity"""
        print(f"🔗 Testing device connectivity: {device_id}")

        try:
            # Test ADB connection
            cmd = f'adb -s {device_id} shell echo "test"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print("✅ ADB connection successful")
                return True
            else:
                print(f"❌ ADB connection failed: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Device connectivity test failed: {e}")
            return False

    def unlock_device_screen(self, device_id: str) -> bool:
        """Unlock device screen if needed"""
        print("🔓 Unlocking device screen...")

        try:
            # Wake up the device
            subprocess.run(f'adb -s {device_id} shell input keyevent KEYCODE_WAKEUP',
                         shell=True, timeout=5)
            time.sleep(2)

            # Swipe up to unlock (common unlock gesture)
            subprocess.run(f'adb -s {device_id} shell input swipe 400 800 400 200',
                         shell=True, timeout=5)
            time.sleep(2)

            print("✅ Screen unlock attempted")
            return True

        except Exception as e:
            print(f"⚠️ Screen unlock failed: {e}")
            return False

    def open_browser_and_navigate(self, device_id: str) -> bool:
        """Open browser and navigate to Google account creation"""
        print("🌐 Opening browser and navigating to Google...")

        try:
            # Open default browser
            cmd = f'adb -s {device_id} shell am start -a android.intent.action.VIEW -d "https://accounts.google.com/signup"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)

            if result.returncode == 0:
                print("✅ Browser opened successfully")
                time.sleep(10)  # Wait for page to load
                return True
            else:
                print(f"❌ Failed to open browser: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Browser navigation failed: {e}")
            return False

    def analyze_current_screen(self, device_id: str) -> Dict:
        """Analyze current screen content"""
        print("🔍 Analyzing current screen...")

        try:
            # Take screenshot
            screenshot_cmd = f'adb -s {device_id} exec-out screencap -p > /tmp/gmail_test_screenshot.png'
            subprocess.run(screenshot_cmd, shell=True, timeout=10)
            print("📸 Screenshot taken: /tmp/gmail_test_screenshot.png")

            # Get UI dump
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)

            analysis = {
                "screenshot_taken": True,
                "ui_dump_success": result.returncode == 0,
                "ui_content": result.stdout if result.returncode == 0 else None
            }

            if analysis["ui_dump_success"]:
                ui_content = result.stdout.lower()

                # Analyze form fields
                analysis.update({
                    "has_name_field": any(keyword in ui_content for keyword in ['first name', 'firstname', 'given name']),
                    "has_surname_field": any(keyword in ui_content for keyword in ['last name', 'lastname', 'surname', 'family name']),
                    "has_username_field": any(keyword in ui_content for keyword in ['username', 'choose username']),
                    "has_password_field": any(keyword in ui_content for keyword in ['password', 'create password']),
                    "has_google_text": 'google' in ui_content,
                    "has_create_account": any(keyword in ui_content for keyword in ['create account', 'sign up']),
                    "has_input_fields": 'android.widget.edittext' in ui_content,
                    "input_field_count": ui_content.count('android.widget.edittext')
                })

                print(f"📊 Screen Analysis Results:")
                for key, value in analysis.items():
                    if key not in ['ui_content']:
                        print(f"   {key}: {value}")

            return analysis

        except Exception as e:
            print(f"❌ Screen analysis failed: {e}")
            return {"error": str(e)}

    def test_form_field_detection(self, device_id: str) -> Dict:
        """Test form field detection and interaction"""
        print("📝 Testing form field detection...")

        try:
            # Get UI dump for detailed analysis
            ui_cmd = f'adb -s {device_id} shell "uiautomator dump /sdcard/ui_dump.xml && cat /sdcard/ui_dump.xml"'
            result = subprocess.run(ui_cmd, shell=True, capture_output=True, text=True, timeout=15)

            if result.returncode != 0:
                return {"error": "Failed to get UI dump"}

            ui_content = result.stdout

            # Find all EditText fields
            import re
            edittext_pattern = r'<node[^>]*class="android\.widget\.EditText"[^>]*>'
            edittext_matches = re.findall(edittext_pattern, ui_content)

            fields_info = []
            for i, match in enumerate(edittext_matches):
                # Extract bounds
                bounds_match = re.search(r'bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"', match)
                # Extract text/hint
                text_match = re.search(r'text="([^"]*)"', match)
                hint_match = re.search(r'content-desc="([^"]*)"', match)

                field_info = {
                    "index": i,
                    "bounds": bounds_match.groups() if bounds_match else None,
                    "text": text_match.group(1) if text_match else "",
                    "hint": hint_match.group(1) if hint_match else "",
                    "raw": match
                }
                fields_info.append(field_info)

            print(f"📋 Found {len(fields_info)} input fields:")
            for field in fields_info:
                print(f"   Field {field['index']}: text='{field['text']}', hint='{field['hint']}'")
                if field['bounds']:
                    x1, y1, x2, y2 = field['bounds']
                    center_x = (int(x1) + int(x2)) // 2
                    center_y = (int(y1) + int(y2)) // 2
                    print(f"     Center: ({center_x}, {center_y})")

            return {
                "total_fields": len(fields_info),
                "fields": fields_info,
                "ui_dump_length": len(ui_content)
            }

        except Exception as e:
            print(f"❌ Form field detection failed: {e}")
            return {"error": str(e)}

    def test_input_interaction(self, device_id: str, field_info: Dict) -> bool:
        """Test input interaction with a specific field"""
        print(f"⌨️ Testing input interaction with field {field_info['index']}...")

        try:
            if not field_info['bounds']:
                print("❌ No bounds information for field")
                return False

            x1, y1, x2, y2 = field_info['bounds']
            center_x = (int(x1) + int(x2)) // 2
            center_y = (int(y1) + int(y2)) // 2

            # Tap on the field
            tap_cmd = f'adb -s {device_id} shell input tap {center_x} {center_y}'
            result = subprocess.run(tap_cmd, shell=True, timeout=5)

            if result.returncode == 0:
                print(f"✅ Tapped field at ({center_x}, {center_y})")
                time.sleep(1)

                # Try to input test text
                test_text = f"TestField{field_info['index']}"
                input_cmd = f'adb -s {device_id} shell input text "{test_text}"'
                input_result = subprocess.run(input_cmd, shell=True, timeout=5)

                if input_result.returncode == 0:
                    print(f"✅ Input text '{test_text}' successfully")
                    time.sleep(1)
                    return True
                else:
                    print(f"❌ Failed to input text")
                    return False
            else:
                print(f"❌ Failed to tap field")
                return False

        except Exception as e:
            print(f"❌ Input interaction failed: {e}")
            return False

    def run_comprehensive_test(self) -> Dict:
        """Run comprehensive Gmail form interaction test"""
        print("🚀 Starting Comprehensive Gmail Form Interaction Test")
        print("=" * 60)

        test_results = {
            "device_found": False,
            "device_started": False,
            "connectivity_ok": False,
            "browser_opened": False,
            "screen_analysis": {},
            "form_detection": {},
            "input_tests": []
        }

        # Step 1: Find available device
        device_name = self.find_available_device()
        if not device_name:
            test_results["error"] = "No devices available"
            return test_results

        test_results["device_found"] = True
        test_results["device_name"] = device_name
        self.device_name = device_name

        # Step 2: Ensure device is running
        if not self.ensure_device_running(device_name):
            test_results["error"] = "Failed to start device"
            return test_results

        test_results["device_started"] = True

        # Step 3: Get device ADB ID
        device_id = self.get_device_adb_id(device_name)
        if not device_id:
            test_results["error"] = "Failed to get device ADB ID"
            return test_results

        test_results["device_id"] = device_id
        self.device_id = device_id

        # Step 4: Test connectivity
        if not self.test_device_connectivity(device_id):
            test_results["error"] = "Device connectivity failed"
            return test_results

        test_results["connectivity_ok"] = True

        # Step 5: Unlock screen
        self.unlock_device_screen(device_id)

        # Step 6: Open browser and navigate
        if not self.open_browser_and_navigate(device_id):
            test_results["error"] = "Failed to open browser"
            return test_results

        test_results["browser_opened"] = True

        # Step 7: Analyze screen
        screen_analysis = self.analyze_current_screen(device_id)
        test_results["screen_analysis"] = screen_analysis

        # Step 8: Test form field detection
        form_detection = self.test_form_field_detection(device_id)
        test_results["form_detection"] = form_detection

        # Step 9: Test input interactions
        if "fields" in form_detection and form_detection["fields"]:
            for field in form_detection["fields"][:3]:  # Test first 3 fields
                input_success = self.test_input_interaction(device_id, field)
                test_results["input_tests"].append({
                    "field_index": field["index"],
                    "success": input_success,
                    "field_info": field
                })

        return test_results


def main():
    """Main test execution"""
    print("🧪 Gmail Form Interaction Test Suite")
    print("=" * 60)

    tester = GmailFormTester()
    results = tester.run_comprehensive_test()

    print("\n" + "=" * 60)
    print("📊 Test Results Summary")
    print("=" * 60)

    # Print results
    for key, value in results.items():
        if key not in ['screen_analysis', 'form_detection', 'input_tests']:
            print(f"{key}: {value}")

    if "screen_analysis" in results:
        print(f"\n📱 Screen Analysis:")
        analysis = results["screen_analysis"]
        for key, value in analysis.items():
            if key != 'ui_content':
                print(f"  {key}: {value}")

    if "form_detection" in results:
        print(f"\n📝 Form Detection:")
        detection = results["form_detection"]
        print(f"  Total fields found: {detection.get('total_fields', 0)}")
        if "fields" in detection:
            for field in detection["fields"]:
                print(f"    Field {field['index']}: '{field['text']}' / '{field['hint']}'")

    if "input_tests" in results:
        print(f"\n⌨️ Input Tests:")
        for test in results["input_tests"]:
            status = "✅" if test["success"] else "❌"
            print(f"  {status} Field {test['field_index']}: {test['success']}")

    print("\n" + "=" * 60)
    print("🎉 Test Complete!")

    if "error" in results:
        print(f"❌ Test failed: {results['error']}")
        return 1
    else:
        print("✅ Test completed successfully!")
        return 0


if __name__ == "__main__":
    exit(main())
