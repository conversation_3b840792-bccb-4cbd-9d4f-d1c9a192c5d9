#!/usr/bin/env python3
"""
Test script to check actual gmtool output and debug device creation issues
"""

import subprocess
import sys
import os
from loguru import logger


def test_gmtool_command(command):
    """Test a gmtool command and show its output"""
    try:
        logger.info(f"Testing command: gmtool {' '.join(command)}")
        
        # Try to find gmtool
        gmtool_paths = [
            "/Applications/Genymotion.app/Contents/MacOS/gmtool",
            "gmtool"  # In PATH
        ]
        
        gmtool_path = None
        for path in gmtool_paths:
            if os.path.exists(path) or path == "gmtool":
                try:
                    result = subprocess.run([path, "version"], capture_output=True, timeout=5)
                    if result.returncode == 0:
                        gmtool_path = path
                        break
                except:
                    continue
        
        if not gmtool_path:
            logger.error("gmtool not found!")
            return None
        
        logger.info(f"Using gmtool at: {gmtool_path}")
        
        # Execute the command
        full_command = [gmtool_path] + command
        result = subprocess.run(full_command, capture_output=True, text=True, timeout=30)
        
        logger.info(f"Return code: {result.returncode}")
        
        if result.stdout:
            logger.info("STDOUT:")
            for i, line in enumerate(result.stdout.split('\n'), 1):
                logger.info(f"  {i:2d}: {repr(line)}")
        
        if result.stderr:
            logger.error("STDERR:")
            for i, line in enumerate(result.stderr.split('\n'), 1):
                logger.error(f"  {i:2d}: {repr(line)}")
        
        return result
        
    except subprocess.TimeoutExpired:
        logger.error("Command timed out")
        return None
    except Exception as e:
        logger.error(f"Error executing command: {e}")
        return None


def parse_hardware_profiles(stdout):
    """Parse hardware profiles and show parsing logic"""
    if not stdout:
        logger.error("No stdout to parse")
        return
    
    logger.info("Parsing hardware profiles:")
    lines = stdout.strip().split('\n')
    
    for i, line in enumerate(lines):
        logger.info(f"Line {i}: {repr(line)}")
        
        if "HTC One" in line:
            logger.info(f"  -> Found HTC One: {line}")
        elif "Custom Phone" in line:
            logger.info(f"  -> Found Custom Phone: {line}")
        elif line.strip() and not line.startswith('-') and not line.startswith('UUID') and not line.startswith('NAME'):
            parts = line.split()
            if len(parts) >= 2:
                logger.info(f"  -> Potential profile: {parts}")


def parse_os_images(stdout, android_version="14"):
    """Parse OS images and show parsing logic"""
    if not stdout:
        logger.error("No stdout to parse")
        return
    
    logger.info(f"Parsing OS images for Android {android_version}:")
    lines = stdout.strip().split('\n')
    
    for i, line in enumerate(lines):
        logger.info(f"Line {i}: {repr(line)}")
        
        if f"Android {android_version}" in line or f"{android_version}.0" in line:
            logger.info(f"  -> Found target version: {line}")
            parts = line.split()
            logger.info(f"  -> Parts: {parts}")
        elif "Android" in line and line.strip() and not line.startswith('-') and not line.startswith('UUID'):
            logger.info(f"  -> Found Android image: {line}")
            parts = line.split()
            logger.info(f"  -> Parts: {parts}")


def main():
    """Run diagnostic tests"""
    logger.info("=" * 60)
    logger.info("GENYMOTION GMTOOL DIAGNOSTIC")
    logger.info("=" * 60)
    
    # Test basic commands
    commands_to_test = [
        (["version"], "Version check"),
        (["admin", "list"], "List instances"),
        (["admin", "hwprofiles"], "Hardware profiles"),
        (["admin", "osimages"], "OS images")
    ]
    
    results = {}
    
    for cmd, description in commands_to_test:
        logger.info(f"\n{'='*40}")
        logger.info(f"Testing: {description}")
        logger.info(f"{'='*40}")
        
        result = test_gmtool_command(cmd)
        results[description] = result
        
        if result and result.returncode == 0:
            if description == "Hardware profiles":
                parse_hardware_profiles(result.stdout)
            elif description == "OS images":
                parse_os_images(result.stdout)
    
    # Summary
    logger.info(f"\n{'='*60}")
    logger.info("SUMMARY")
    logger.info(f"{'='*60}")
    
    for description, result in results.items():
        if result:
            status = "✅ SUCCESS" if result.returncode == 0 else "❌ FAILED"
            logger.info(f"{description}: {status}")
        else:
            logger.info(f"{description}: ❌ ERROR")
    
    # Provide recommendations
    logger.info(f"\n{'='*60}")
    logger.info("RECOMMENDATIONS")
    logger.info(f"{'='*60}")
    
    if not results.get("Version check") or results["Version check"].returncode != 0:
        logger.info("❌ Genymotion Desktop not accessible")
        logger.info("   1. Install Genymotion Desktop from https://www.genymotion.com/")
        logger.info("   2. Start Genymotion Desktop application")
        logger.info("   3. Sign in with your Genymotion account")
    
    elif not results.get("Hardware profiles") or results["Hardware profiles"].returncode != 0:
        logger.info("❌ Hardware profiles not accessible")
        logger.info("   1. Ensure Genymotion Desktop is running")
        logger.info("   2. Sign in to your Genymotion account")
        logger.info("   3. Check internet connection")
    
    elif not results.get("OS images") or results["OS images"].returncode != 0:
        logger.info("❌ OS images not accessible")
        logger.info("   1. Ensure Genymotion Desktop is running")
        logger.info("   2. Sign in to your Genymotion account")
        logger.info("   3. Download Android images from Genymotion Desktop")
    
    else:
        logger.info("✅ All basic checks passed!")
        logger.info("   Device creation should work with proper profile/image names")


if __name__ == "__main__":
    main()
