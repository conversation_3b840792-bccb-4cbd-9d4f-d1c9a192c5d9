#!/usr/bin/env python3
"""
Test script for HTC One Android 14.0 browser automation scenario

This script tests the basic functionality without actually creating a device.
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.genymotion_manager import GenymotionManager
from src.appium_server import AppiumServerManager
from loguru import logger


def test_htc_configuration():
    """Test HTC One device configuration"""
    logger.info("🧪 Testing HTC One configuration...")

    # Test device config
    device_config = {
        "hardware_profile": "HTC One",
        "android_version": "14",
        "device_name": "HTC One",
        "screen_resolution": "1080x1920",
        "dpi": 441,
        "deviceManufacturer": "HTC",
        "deviceModel": "HTC One",
        "platformVersion": "14"
    }

    logger.info(f"✅ Device Config: {device_config['device_name']}")
    logger.info(f"✅ Android Version: {device_config['android_version']}")
    logger.info(f"✅ Screen Resolution: {device_config['screen_resolution']}")
    logger.info(f"✅ DPI: {device_config['dpi']}")

    return True


def test_genymotion_manager():
    """Test Genymotion manager initialization"""
    logger.info("🧪 Testing Genymotion manager...")

    try:
        manager = GenymotionManager()
        logger.info("✅ Genymotion manager initialized")

        # Test device profile generation
        profile = manager.get_random_device_profile()
        logger.info(f"✅ Device profile generated: {profile.get('device_name', 'Unknown')}")

        return True
    except Exception as e:
        logger.error(f"❌ Genymotion manager test failed: {e}")
        return False


def test_appium_capabilities():
    """Test Appium capabilities generation"""
    logger.info("🧪 Testing Appium capabilities...")

    try:
        appium_manager = AppiumServerManager()

        device_config = {
            "platformName": "Android",
            "platformVersion": "14",
            "deviceName": "HTC One",
            "udid": "emulator-5554",
            "automationName": "UiAutomator2",
            "deviceManufacturer": "HTC",
            "deviceModel": "HTC One"
        }

        capabilities = appium_manager.generate_capabilities(
            device_profile=device_config,
            app_package="com.android.browser"
        )

        logger.info("✅ Appium capabilities generated:")
        logger.info(f"  - Platform: {capabilities.get('platformName', 'Android')}")
        logger.info(f"  - Version: {capabilities.get('platformVersion', '14')}")
        logger.info(f"  - Device: {capabilities.get('deviceName', 'HTC One')}")
        logger.info(f"  - App Package: {capabilities.get('appPackage', 'com.android.browser')}")
        logger.info(f"  - UDID: {capabilities.get('udid', 'emulator-5562')}")

        return True
    except Exception as e:
        logger.error(f"❌ Appium capabilities test failed: {e}")
        return False


def test_browser_package_detection():
    """Test browser package detection logic"""
    logger.info("🧪 Testing browser package detection...")

    # Simulate browser packages that might be available
    browser_packages = [
        'com.android.browser',  # AOSP Browser
        'com.google.android.browser',  # Google Browser
        'com.android.chrome',  # Chrome (fallback)
        'org.chromium.webview_shell'  # WebView shell
    ]

    logger.info("✅ Browser packages to check:")
    for package in browser_packages:
        logger.info(f"  - {package}")

    logger.info("✅ Default browser package: com.android.browser")
    return True


def test_screen_coordinates():
    """Test screen coordinates for HTC One"""
    logger.info("🧪 Testing screen coordinates for HTC One...")

    # HTC One screen: 1080x1920
    screen_width = 1080
    screen_height = 1920

    # Calculate search box coordinates (center horizontally, upper third vertically)
    search_x = screen_width // 2  # 540
    search_y = screen_height // 5  # ~384, but we use 350 for better targeting

    logger.info(f"✅ Screen dimensions: {screen_width}x{screen_height}")
    logger.info(f"✅ Search coordinates: ({search_x}, 350)")
    logger.info("✅ Coordinates optimized for Google search box")

    return True


def main():
    """Run all tests"""
    logger.info("=" * 60)
    logger.info("🚀 HTC ONE ANDROID 14.0 SCENARIO TESTS")
    logger.info("=" * 60)

    tests = [
        ("HTC Configuration", test_htc_configuration),
        ("Genymotion Manager", test_genymotion_manager),
        ("Appium Capabilities", test_appium_capabilities),
        ("Browser Package Detection", test_browser_package_detection),
        ("Screen Coordinates", test_screen_coordinates)
    ]

    passed = 0
    total = len(tests)

    for test_name, test_func in tests:
        logger.info(f"\n🧪 Running: {test_name}")
        try:
            if test_func():
                logger.info(f"✅ {test_name}: PASSED")
                passed += 1
            else:
                logger.error(f"❌ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name}: ERROR - {e}")

    logger.info("\n" + "=" * 60)
    logger.info(f"📊 TEST RESULTS: {passed}/{total} tests passed")

    if passed == total:
        logger.info("🎉 All tests passed! HTC One scenario is ready.")
        logger.info("\n💡 To run the full scenario:")
        logger.info("   python3 run_genymotion_scenario.py")
        logger.info("\n💡 To run with verbose logging:")
        logger.info("   python3 run_genymotion_scenario.py --verbose")
    else:
        logger.warning("⚠️ Some tests failed. Check the configuration.")

    logger.info("=" * 60)


if __name__ == "__main__":
    main()
