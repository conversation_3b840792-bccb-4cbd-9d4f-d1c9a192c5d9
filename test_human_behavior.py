#!/usr/bin/env python3
"""
Test Human Behavior Simulation
Tests the human-like behavior patterns for Gmail automation
"""

import sys
import os
import time
import random

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import HumanBehaviorSimulator


def test_human_behavior_features():
    """Test various human behavior simulation features"""
    print("🤖 Testing Human Behavior Simulation Features")
    print("=" * 60)
    
    # Initialize behavior simulator
    behavior = HumanBehaviorSimulator()
    
    print(f"📊 Behavior Profile:")
    print(f"   Typing Speed: {behavior.typing_speed_wpm} WPM")
    print(f"   Error Rate: {behavior.error_rate:.1%}")
    print(f"   Mouse Variance: ±{behavior.mouse_movement_variance} pixels")
    print(f"   Pause Probability: {behavior.pause_probability:.1%}")
    
    # Test 1: Human delays
    print("\n1️⃣ Testing Human-like Delays")
    print("-" * 30)
    
    for i in range(3):
        start_time = time.time()
        delay = behavior.human_delay(1.0, 3.0)
        actual_delay = time.time() - start_time
        print(f"   Delay {i+1}: {delay:.2f}s (actual: {actual_delay:.2f}s)")
    
    # Test 2: Typing behavior
    print("\n2️⃣ Testing Typing Behavior")
    print("-" * 30)
    
    test_texts = [
        "john.doe123",
        "SecurePassword!",
        "<EMAIL>",
        "This is a longer text to test typing behavior with pauses"
    ]
    
    for text in test_texts:
        typing_time = behavior.typing_delay(text)
        print(f"   Text: '{text}'")
        print(f"   Estimated typing time: {typing_time:.2f}s")
        
        # Test error simulation
        typing_sequence = behavior.simulate_typing_errors(text)
        if len(typing_sequence) > 1:
            print(f"   ⚠️ Simulated typing error and correction")
            print(f"   Sequence: {' → '.join(typing_sequence)}")
        else:
            print(f"   ✅ No typing errors simulated")
        print()
    
    # Test 3: Mouse jitter
    print("\n3️⃣ Testing Mouse Movement Variance")
    print("-" * 30)
    
    original_coords = [(100, 200), (300, 400), (500, 600)]
    
    for x, y in original_coords:
        jittered_coords = []
        for _ in range(5):
            jx, jy = behavior.add_mouse_jitter(x, y)
            jittered_coords.append((jx, jy))
        
        print(f"   Original: ({x}, {y})")
        print(f"   Jittered: {jittered_coords}")
        
        # Calculate variance
        x_variance = max(jx for jx, jy in jittered_coords) - min(jx for jx, jy in jittered_coords)
        y_variance = max(jy for jx, jy in jittered_coords) - min(jy for jx, jy in jittered_coords)
        print(f"   Variance: X±{x_variance//2}, Y±{y_variance//2}")
        print()
    
    # Test 4: Reading delays
    print("\n4️⃣ Testing Reading Behavior")
    print("-" * 30)
    
    text_samples = [
        ("Short text", 20),
        ("Medium length text sample", 50),
        ("This is a longer text sample that would take more time to read and understand", 100),
        ("Very long text sample that simulates reading a full paragraph or section of content that requires significant reading time", 200)
    ]
    
    for description, length in text_samples:
        start_time = time.time()
        reading_time = behavior.reading_delay(length)
        actual_time = time.time() - start_time
        print(f"   {description} ({length} chars)")
        print(f"   Estimated: {reading_time:.2f}s, Actual: {actual_time:.2f}s")
    
    # Test 5: Decision delays
    print("\n5️⃣ Testing Decision-Making Delays")
    print("-" * 30)
    
    for i in range(3):
        start_time = time.time()
        decision_time = behavior.decision_delay()
        actual_time = time.time() - start_time
        print(f"   Decision {i+1}: {decision_time:.2f}s (actual: {actual_time:.2f}s)")
    
    print("\n" + "=" * 60)
    print("✅ Human Behavior Testing Completed!")


def demonstrate_realistic_typing():
    """Demonstrate realistic typing with errors and corrections"""
    print("\n🎭 Demonstrating Realistic Typing Simulation")
    print("=" * 60)
    
    behavior = HumanBehaviorSimulator()
    
    # Simulate typing a Gmail signup form
    form_fields = [
        ("First Name", "Ahmet"),
        ("Last Name", "Yılmaz"),
        ("Username", "ahmetyilmaz123"),
        ("Password", "SecurePass123!"),
        ("Confirm Password", "SecurePass123!"),
        ("Phone Number", "+90 532 123 45 67")
    ]
    
    total_time = 0
    
    for field_name, text in form_fields:
        print(f"\n📝 Typing '{field_name}': {text}")
        
        # Calculate typing time
        typing_time = behavior.typing_delay(text)
        total_time += typing_time
        
        # Simulate typing sequence with potential errors
        typing_sequence = behavior.simulate_typing_errors(text)
        
        if len(typing_sequence) > 1:
            print(f"   ⚠️ Typing error detected!")
            for i, step in enumerate(typing_sequence):
                if i == 0:
                    print(f"   1. Initial typing: '{step}'")
                elif i == 1:
                    print(f"   2. Error made: '{step}'")
                elif i == 2:
                    print(f"   3. Backspace to: '{step}'")
                else:
                    print(f"   4. Corrected to: '{step}'")
        else:
            print(f"   ✅ Typed correctly: '{typing_sequence[0]}'")
        
        print(f"   ⏱️ Estimated typing time: {typing_time:.2f}s")
        
        # Add thinking/pause time
        if random.random() < behavior.pause_probability:
            pause_time = random.uniform(0.5, 2.0)
            print(f"   💭 Thinking pause: {pause_time:.2f}s")
            total_time += pause_time
        
        # Add field transition time
        transition_time = random.uniform(0.3, 1.0)
        total_time += transition_time
        print(f"   ➡️ Field transition: {transition_time:.2f}s")
    
    print(f"\n📊 Total Form Completion Time: {total_time:.1f} seconds ({total_time/60:.1f} minutes)")
    print("   This simulates realistic human form-filling speed!")


def show_anti_detection_features():
    """Show anti-detection features"""
    print("\n🛡️ Anti-Detection Features")
    print("=" * 60)
    
    features = [
        "🎯 Variable Typing Speed (25-45 WPM)",
        "❌ Realistic Typing Errors (2-8% error rate)",
        "⏸️ Natural Pauses During Typing",
        "🖱️ Mouse Movement Jitter (±5 pixels)",
        "📖 Reading Time Simulation (200-300 WPM)",
        "🤔 Decision-Making Delays (1-3.5 seconds)",
        "📜 Random Scrolling Behavior",
        "🔄 Natural Page Interactions",
        "⏳ Variable Startup Delays (5-15 seconds)",
        "🌐 Browsing Pattern Simulation",
        "📱 Device Settling Time",
        "🎉 Success Celebration Delays"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print(f"\n💡 Benefits:")
    print(f"   • Mimics real human behavior patterns")
    print(f"   • Reduces automation detection risk")
    print(f"   • Varies timing to avoid pattern recognition")
    print(f"   • Simulates natural user interactions")
    print(f"   • Includes realistic mistakes and corrections")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "typing":
            demonstrate_realistic_typing()
        elif sys.argv[1] == "features":
            show_anti_detection_features()
        else:
            print("Usage: python test_human_behavior.py [typing|features]")
    else:
        test_human_behavior_features()
        demonstrate_realistic_typing()
        show_anti_detection_features()
