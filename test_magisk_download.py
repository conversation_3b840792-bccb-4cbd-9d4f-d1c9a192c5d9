#!/usr/bin/env python3
"""
Diagnostic test for Magisk download issues
"""

import subprocess
import urllib.request
import tempfile
import os
import sys
import time
from loguru import logger

def test_network_connectivity():
    """Test basic network connectivity"""
    logger.info("🌐 Testing network connectivity...")
    
    test_urls = [
        "https://www.google.com",
        "https://github.com",
        "https://api.github.com",
        "https://github.com/topjohnwu/Magisk"
    ]
    
    for url in test_urls:
        try:
            response = urllib.request.urlopen(url, timeout=10)
            logger.info(f"✅ {url} - Status: {response.getcode()}")
        except Exception as e:
            logger.error(f"❌ {url} - Error: {e}")

def test_magisk_urls():
    """Test Magisk download URLs specifically"""
    logger.info("🔗 Testing Magisk URLs...")
    
    magisk_urls = [
        "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.apk",
        "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk",
        "https://github.com/topjohnwu/Magisk/releases/download/v26.3/Magisk-v26.3.apk",
        "https://github.com/topjohnwu/Magisk/releases/download/v26.1/Magisk-v26.1.apk",
        "https://github.com/topjohnwu/Magisk/releases/download/v27.0/Magisk-v27.0.zip",
        "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.zip"
    ]
    
    for url in magisk_urls:
        try:
            # Test HEAD request first
            req = urllib.request.Request(url, method='HEAD')
            response = urllib.request.urlopen(req, timeout=15)
            logger.info(f"✅ {url} - Status: {response.getcode()}, Size: {response.headers.get('Content-Length', 'Unknown')}")
        except Exception as e:
            logger.error(f"❌ {url} - Error: {e}")

def test_download_methods():
    """Test different download methods"""
    logger.info("📥 Testing download methods...")
    
    test_url = "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk"
    
    # Method 1: urllib
    try:
        logger.info("Testing urllib.request...")
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            urllib.request.urlretrieve(test_url, tmp_file.name)
            size = os.path.getsize(tmp_file.name)
            logger.info(f"✅ urllib download successful - Size: {size} bytes")
            os.unlink(tmp_file.name)
    except Exception as e:
        logger.error(f"❌ urllib failed: {e}")
    
    # Method 2: requests
    try:
        import requests
        logger.info("Testing requests...")
        response = requests.get(test_url, timeout=30)
        response.raise_for_status()
        logger.info(f"✅ requests download successful - Size: {len(response.content)} bytes")
    except Exception as e:
        logger.error(f"❌ requests failed: {e}")
    
    # Method 3: curl
    try:
        logger.info("Testing curl...")
        with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
            curl_cmd = f'curl -L -o "{tmp_file.name}" "{test_url}"'
            result = subprocess.run(curl_cmd, shell=True, capture_output=True, text=True, timeout=60)
            if result.returncode == 0:
                size = os.path.getsize(tmp_file.name)
                logger.info(f"✅ curl download successful - Size: {size} bytes")
            else:
                logger.error(f"❌ curl failed: {result.stderr}")
            os.unlink(tmp_file.name)
    except Exception as e:
        logger.error(f"❌ curl failed: {e}")

def test_adb_connectivity():
    """Test ADB connectivity"""
    logger.info("📱 Testing ADB connectivity...")
    
    try:
        # Test ADB devices
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, timeout=10)
        logger.info(f"ADB devices output:\n{result.stdout}")
        
        # Find connected devices
        device_lines = [line.strip() for line in result.stdout.split('\n') if 'device' in line and 'List' not in line]
        
        if not device_lines:
            logger.warning("⚠️ No ADB devices found")
            return None
        
        # Get first device
        device_id = device_lines[0].split()[0]
        logger.info(f"📱 Testing with device: {device_id}")
        
        # Test basic ADB commands
        test_cmd = f'adb -s {device_id} shell echo "ADB test successful"'
        result = subprocess.run(test_cmd, shell=True, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            logger.info(f"✅ ADB communication successful: {result.stdout.strip()}")
        else:
            logger.error(f"❌ ADB communication failed: {result.stderr}")
        
        return device_id
        
    except Exception as e:
        logger.error(f"❌ ADB test failed: {e}")
        return None

def test_adb_push(device_id):
    """Test ADB push functionality"""
    if not device_id:
        logger.warning("⚠️ Skipping ADB push test - no device available")
        return
    
    logger.info(f"📁 Testing ADB push to device: {device_id}")
    
    try:
        # Create a test file
        with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt') as tmp_file:
            tmp_file.write("ADB push test file")
            test_file_path = tmp_file.name
        
        # Test push
        push_cmd = f'adb -s {device_id} push "{test_file_path}" /sdcard/adb_test.txt'
        result = subprocess.run(push_cmd, shell=True, capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            logger.info("✅ ADB push successful")
            
            # Verify file exists on device
            verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/adb_test.txt"'
            verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)
            
            if verify_result.returncode == 0:
                logger.info(f"✅ File verified on device: {verify_result.stdout.strip()}")
                
                # Clean up
                cleanup_cmd = f'adb -s {device_id} shell "rm /sdcard/adb_test.txt"'
                subprocess.run(cleanup_cmd, shell=True, capture_output=True, text=True, timeout=10)
            else:
                logger.error(f"❌ File verification failed: {verify_result.stderr}")
        else:
            logger.error(f"❌ ADB push failed: {result.stderr}")
        
        # Clean up local file
        os.unlink(test_file_path)
        
    except Exception as e:
        logger.error(f"❌ ADB push test failed: {e}")

def test_full_magisk_download_push():
    """Test complete Magisk download and push process"""
    logger.info("🔧 Testing complete Magisk download and push process...")
    
    device_id = test_adb_connectivity()
    if not device_id:
        logger.error("❌ Cannot test without ADB device")
        return
    
    test_url = "https://github.com/topjohnwu/Magisk/releases/download/v26.4/Magisk-v26.4.apk"
    
    try:
        # Download file
        logger.info("📥 Downloading Magisk APK...")
        with tempfile.NamedTemporaryFile(suffix='.apk', delete=False) as tmp_file:
            urllib.request.urlretrieve(test_url, tmp_file.name)
            file_size = os.path.getsize(tmp_file.name)
            logger.info(f"✅ Download successful - Size: {file_size} bytes")
            
            if file_size < 1000:
                logger.error("❌ Downloaded file too small - likely corrupted")
                return
            
            # Push to device
            logger.info("📁 Pushing to device...")
            push_cmd = f'adb -s {device_id} push "{tmp_file.name}" /sdcard/MagiskTest.apk'
            result = subprocess.run(push_cmd, shell=True, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("✅ Push successful")
                
                # Verify on device
                verify_cmd = f'adb -s {device_id} shell "ls -la /sdcard/MagiskTest.apk"'
                verify_result = subprocess.run(verify_cmd, shell=True, capture_output=True, text=True, timeout=10)
                
                if verify_result.returncode == 0:
                    logger.info(f"✅ File verified on device: {verify_result.stdout.strip()}")
                    
                    # Clean up
                    cleanup_cmd = f'adb -s {device_id} shell "rm /sdcard/MagiskTest.apk"'
                    subprocess.run(cleanup_cmd, shell=True, capture_output=True, text=True, timeout=10)
                    
                    logger.info("🎉 Complete download and push test SUCCESSFUL!")
                else:
                    logger.error(f"❌ File verification failed: {verify_result.stderr}")
            else:
                logger.error(f"❌ Push failed: {result.stderr}")
            
            # Clean up local file
            os.unlink(tmp_file.name)
            
    except Exception as e:
        logger.error(f"❌ Complete test failed: {e}")

def main():
    logger.info("🚀 Starting Magisk Download Diagnostic Test")
    logger.info("=" * 60)
    
    test_network_connectivity()
    logger.info("-" * 40)
    
    test_magisk_urls()
    logger.info("-" * 40)
    
    test_download_methods()
    logger.info("-" * 40)
    
    device_id = test_adb_connectivity()
    logger.info("-" * 40)
    
    test_adb_push(device_id)
    logger.info("-" * 40)
    
    test_full_magisk_download_push()
    logger.info("-" * 40)
    
    logger.info("🏁 Diagnostic test completed")

if __name__ == "__main__":
    main()
