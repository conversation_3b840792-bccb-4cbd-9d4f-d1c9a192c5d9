#!/usr/bin/env python3
"""
Test OCR functionality
"""

import sys
import os
import subprocess

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from genymotion_manager import GenymotionManager
from gmail_account_creator import GmailAccountCreator


def test_ocr():
    """Test OCR functionality"""
    print("🔍 Testing OCR Functionality")
    print("=" * 50)
    
    # Initialize components
    genymotion = GenymotionManager()
    gmail_creator = GmailAccountCreator()
    
    # Find running device
    instances = genymotion.get_available_instances()
    
    device_id = None
    for name, info in instances.items():
        if info.get('status') == 'running':
            device_id = genymotion.get_device_adb_id(name)
            print(f"✅ Found running device: {name} ({device_id})")
            break
    
    if not device_id:
        print("❌ No running device found")
        return False
    
    # Take a screenshot first
    print("\n📸 Taking screenshot...")
    try:
        subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/ocr_test_screenshot.png', 
                     shell=True, timeout=10)
        print("✅ Screenshot saved: /tmp/ocr_test_screenshot.png")
    except Exception as e:
        print(f"❌ Failed to take screenshot: {e}")
        return False
    
    # Test OCR detection
    print("\n🔍 Testing OCR detection...")
    
    # Test patterns we expect to find
    test_patterns = [
        'create account', 'sign in', 'email', 'phone', 'google', 'account'
    ]
    
    for pattern in test_patterns:
        print(f"\n🔍 Looking for: '{pattern}'")
        
        try:
            found = gmail_creator._find_element_with_ocr(device_id, [pattern], f"test pattern '{pattern}'")
            
            if found:
                x, y = found
                print(f"✅ Found '{pattern}' at coordinates: ({x}, {y})")
            else:
                print(f"❌ Could not find '{pattern}'")
                
        except Exception as e:
            print(f"❌ Error testing '{pattern}': {e}")
    
    # Test clicking with OCR
    print("\n🔘 Testing OCR clicking...")
    
    # Try to find and click something safe (like the address bar or a button)
    click_patterns = ['create account', 'sign in']
    
    for pattern in click_patterns:
        print(f"\n🔘 Trying to click: '{pattern}'")
        
        try:
            success = gmail_creator._click_element_with_ocr(device_id, [pattern], f"test click '{pattern}'")
            
            if success:
                print(f"✅ Successfully clicked '{pattern}'")
                # Take screenshot after click
                subprocess.run(f'adb -s {device_id} exec-out screencap -p > /tmp/ocr_after_click_{pattern.replace(" ", "_")}.png', 
                             shell=True, timeout=10)
                print(f"📸 Screenshot after click: /tmp/ocr_after_click_{pattern.replace(' ', '_')}.png")
                break
            else:
                print(f"❌ Could not click '{pattern}'")
                
        except Exception as e:
            print(f"❌ Error clicking '{pattern}': {e}")
    
    return True


if __name__ == "__main__":
    test_ocr()
