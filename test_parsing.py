#!/usr/bin/env python3
"""
Test the parsing logic with actual gmtool output
"""

def test_os_image_parsing():
    # Actual output from gmtool admin osimages
    stdout = """UUID                                  NAME          ANDROID VERSION  API VERSION  BETA  ARCHITECTURE  SOURCE    
------------------------------------  ------------  ---------------  -----------  ----  ------------  ----------
d992211e-b0e2-417f-8f5f-7ef53ca14d8d  Android 11.0  11.0.0           30           No    arm64         Genymotion
baed2583-e8ba-407c-b75d-41cb2e3bbdb2  Android 12.1  12.1.0           32           No    arm64         Genymotion
1ab6867c-6c21-49c3-8850-8e74b34229da  Android 13.0  13.0.0           33           No    arm64         Genymotion
160ea4fa-d62c-4207-b540-7100541f6dc6  Android 14.0  14.0.0           34           No    arm64         Genymotion
a27c4113-a385-4013-856f-c5bde3dcb69c  Android 15.0  15.0.0           35           No    arm64         Genymotion
"""
    
    android_version = "14"
    
    lines = stdout.strip().split('\n')
    
    print("Testing OS image parsing:")
    print(f"Looking for Android {android_version}")
    print()
    
    for i, line in enumerate(lines):
        print(f"Line {i}: {repr(line)}")
        
        if f"Android {android_version}" in line or f"{android_version}.0" in line:
            print(f"  -> MATCH! Found target version")
            parts = line.split()
            print(f"  -> Parts: {parts}")
            if len(parts) >= 2:
                print(f"  -> parts[1] = {repr(parts[1])}")
                if len(parts) >= 3 and parts[1] == "Android":
                    result = f"{parts[1]} {parts[2]}"
                    print(f"  -> Combined result: {repr(result)}")
                else:
                    result = parts[1]
                    print(f"  -> Single result: {repr(result)}")
        print()


def test_hardware_profile_parsing():
    # Actual output from gmtool admin hwprofiles (truncated)
    stdout = """UUID                                  NAME                     DISPLAY              SOURCE
------------------------------------  -----------------------  -------------------  ------
e104f058-b291-4764-8e0d-d9ff78a41192  Custom Phone             768 x 1280 dpi 320   VENDOR
2f71872c-8bbc-4260-a284-7c6f50ede169  HTC One                  1080 x 1920 dpi 480  VENDOR
"""
    
    lines = stdout.strip().split('\n')
    
    print("Testing hardware profile parsing:")
    print()
    
    for i, line in enumerate(lines):
        print(f"Line {i}: {repr(line)}")
        
        if "HTC One" in line:
            print(f"  -> MATCH! Found HTC One")
            return "HTC One"
        elif "Custom Phone" in line:
            print(f"  -> MATCH! Found Custom Phone")
            return "Custom Phone"
        print()


if __name__ == "__main__":
    test_os_image_parsing()
    print("=" * 50)
    test_hardware_profile_parsing()
