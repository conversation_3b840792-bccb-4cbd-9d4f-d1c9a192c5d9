#!/usr/bin/env python3
"""
Test Turkish Data Generation
Tests the Faker.js Turkish locale data generation
"""

import subprocess
import json
import sys
import os

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from gmail_account_creator import GmailAccountCreator


def test_turkish_data_generation():
    """Test Turkish data generation"""
    print("🇹🇷 Testing Turkish Data Generation with Faker.js")
    print("=" * 60)
    
    try:
        creator = GmailAccountCreator()
        
        # Generate 5 test profiles
        for i in range(5):
            print(f"\n📋 Test Profile {i+1}:")
            print("-" * 30)
            
            info = creator.generate_personal_info()
            
            print(f"👤 Name: {info.first_name} {info.last_name}")
            print(f"📧 Username: {info.username}")
            print(f"🔑 Password: {info.password}")
            print(f"📅 Birth Date: {info.birth_date}")
            print(f"📱 Phone: {info.phone_number}")
            print(f"📮 Recovery Email: {info.recovery_email}")
            
            # Check if names contain Turkish characters
            turkish_chars = ['ğ', 'ü', 'ş', 'ı', 'ö', 'ç', 'Ğ', 'Ü', 'Ş', 'İ', 'Ö', 'Ç']
            has_turkish = any(char in info.first_name + info.last_name for char in turkish_chars)
            
            if has_turkish:
                print("✅ Contains Turkish characters")
            else:
                print("ℹ️ No Turkish characters (normal for some names)")
            
            # Verify phone format
            if info.phone_number.startswith('+90 5'):
                print("✅ Turkish phone format correct")
            else:
                print("⚠️ Phone format may be incorrect")
        
        print("\n" + "=" * 60)
        print("✅ Turkish data generation test completed!")
        
    except Exception as e:
        print(f"❌ Error testing Turkish data: {e}")


def test_direct_faker_call():
    """Test direct Faker.js call"""
    print("\n🔧 Testing Direct Faker.js Call")
    print("=" * 40)
    
    try:
        # Run the Faker.js script directly
        result = subprocess.run(
            ['node', 'generate_fake_data.js'],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            data = json.loads(result.stdout)
            
            print("✅ Direct Faker.js call successful!")
            print(f"👤 Name: {data['firstName']} {data['lastName']}")
            print(f"📧 Username: {data['username']}")
            print(f"📱 Phone: {data['phoneNumber']}")
            print(f"🌍 Locale: {data['locale']}")
            print(f"🕐 Generated: {data['generatedAt']}")
            
            # Show profile data
            if 'profile' in data:
                profile = data['profile']
                print(f"🏢 Occupation: {profile['occupation']}")
                print(f"🏙️ City: {profile['address']['city']}")
                print(f"🌐 Language: {profile['preferredLanguage']}")
                print(f"⏰ Timezone: {profile['timezone']}")
        else:
            print(f"❌ Faker.js script failed: {result.stderr}")
            
    except Exception as e:
        print(f"❌ Error testing direct Faker call: {e}")


if __name__ == "__main__":
    test_turkish_data_generation()
    test_direct_faker_call()
