#!/usr/bin/env python3
"""
Quick Verification Script for HTC One Scenario

This script verifies that all components are working without creating real devices.
Use this for quick testing before running the complete scenario.
"""

import sys
import subprocess
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.genymotion_manager import GenymotionManager
from src.appium_server import AppiumServerManager
from loguru import logger


def verify_genymotion_installation():
    """Verify Genymotion is installed and accessible"""
    logger.info("🔍 Verifying Genymotion installation...")
    
    try:
        manager = GenymotionManager()
        result = manager._execute_gmtool(['version'])
        
        if result and result.returncode == 0:
            logger.info("✅ Genymotion Desktop is installed and accessible")
            logger.info(f"📋 Version info: {result.stdout.strip()}")
            return True
        else:
            logger.error("❌ Genymotion Desktop not found or not working")
            return False
    except Exception as e:
        logger.error(f"❌ Error checking Genymotion: {e}")
        return False


def verify_adb_installation():
    """Verify ADB is installed and working"""
    logger.info("🔍 Verifying ADB installation...")
    
    try:
        result = subprocess.run(['adb', 'version'], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            logger.info("✅ ADB is installed and working")
            logger.info(f"📋 Version: {result.stdout.split()[4]}")
            return True
        else:
            logger.error("❌ ADB not working properly")
            return False
    except FileNotFoundError:
        logger.error("❌ ADB not found in PATH")
        return False
    except Exception as e:
        logger.error(f"❌ Error checking ADB: {e}")
        return False


def verify_device_templates():
    """Verify HTC One device template is available"""
    logger.info("🔍 Verifying HTC One device template...")
    
    try:
        manager = GenymotionManager()
        
        # List available hardware profiles
        result = manager._execute_gmtool(['admin', 'hwprofiles'])
        
        if result and result.returncode == 0:
            output = result.stdout.lower()
            if 'htc' in output or 'htc one' in output:
                logger.info("✅ HTC One template found")
                return True
            else:
                logger.warning("⚠️ HTC One template not explicitly found, but other templates available")
                logger.info("📋 Available templates include custom phone/tablet options")
                return True
        else:
            logger.warning("⚠️ Could not list hardware profiles")
            return True  # Continue anyway
            
    except Exception as e:
        logger.warning(f"⚠️ Error checking device templates: {e}")
        return True  # Continue anyway


def verify_android_images():
    """Verify Android 14.0 images are available"""
    logger.info("🔍 Verifying Android 14.0 images...")
    
    try:
        manager = GenymotionManager()
        
        # List available OS images
        result = manager._execute_gmtool(['admin', 'osimages'])
        
        if result and result.returncode == 0:
            output = result.stdout.lower()
            if 'android 14' in output or '14.0' in output:
                logger.info("✅ Android 14.0 images found")
                return True
            else:
                logger.warning("⚠️ Android 14.0 not explicitly found")
                logger.info("📋 Other Android versions may be available")
                return True
        else:
            logger.warning("⚠️ Could not list OS images")
            return True  # Continue anyway
            
    except Exception as e:
        logger.warning(f"⚠️ Error checking Android images: {e}")
        return True  # Continue anyway


def verify_system_resources():
    """Verify system has sufficient resources"""
    logger.info("🔍 Verifying system resources...")
    
    try:
        import psutil
        
        # Check memory
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        available_gb = memory.available / (1024**3)
        
        logger.info(f"💾 Total Memory: {memory_gb:.1f}GB")
        logger.info(f"💾 Available Memory: {available_gb:.1f}GB")
        
        if memory_gb >= 8:
            logger.info("✅ Excellent memory for virtual devices")
        elif memory_gb >= 4:
            logger.info("✅ Sufficient memory for virtual devices")
        else:
            logger.warning("⚠️ Low memory - may affect performance")
        
        # Check CPU
        cpu_count = psutil.cpu_count()
        logger.info(f"🖥️ CPU Cores: {cpu_count}")
        
        if cpu_count >= 4:
            logger.info("✅ Good CPU for virtualization")
        elif cpu_count >= 2:
            logger.info("✅ Adequate CPU for virtualization")
        else:
            logger.warning("⚠️ Limited CPU - may affect performance")
        
        # Check disk space
        disk = psutil.disk_usage('/')
        disk_free_gb = disk.free / (1024**3)
        logger.info(f"💽 Free Disk Space: {disk_free_gb:.1f}GB")
        
        if disk_free_gb >= 20:
            logger.info("✅ Sufficient disk space")
        elif disk_free_gb >= 10:
            logger.info("✅ Adequate disk space")
        else:
            logger.warning("⚠️ Low disk space - may cause issues")
        
        return True
        
    except ImportError:
        logger.warning("⚠️ psutil not available, skipping resource check")
        return True
    except Exception as e:
        logger.warning(f"⚠️ Error checking system resources: {e}")
        return True


def verify_appium_capabilities():
    """Verify Appium capabilities generation"""
    logger.info("🔍 Verifying Appium capabilities...")
    
    try:
        appium_manager = AppiumServerManager()
        
        # Test HTC One configuration
        device_config = {
            "platformName": "Android",
            "platformVersion": "14",
            "deviceName": "HTC One",
            "udid": "emulator-5554",
            "automationName": "UiAutomator2",
            "deviceManufacturer": "HTC",
            "deviceModel": "HTC One"
        }
        
        capabilities = appium_manager.generate_capabilities(
            device_profile=device_config,
            app_package="com.android.browser"
        )
        
        logger.info("✅ Appium capabilities generated successfully")
        logger.info(f"📱 Platform: {capabilities.get('platformName')}")
        logger.info(f"📱 Version: {capabilities.get('platformVersion')}")
        logger.info(f"📱 Device: {capabilities.get('deviceName')}")
        logger.info(f"📱 Browser: {capabilities.get('appPackage')}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Appium capabilities verification failed: {e}")
        return False


def verify_browser_commands():
    """Verify browser launch commands are correct"""
    logger.info("🔍 Verifying browser launch commands...")
    
    # Test command construction
    browser_packages = [
        'com.android.browser',
        'com.google.android.browser',
        'com.android.chrome'
    ]
    
    test_device_id = "emulator-5554"
    test_url = "https://www.google.com"
    
    for package in browser_packages:
        cmd = [
            'adb', '-s', test_device_id, 'shell', 'am', 'start',
            '-a', 'android.intent.action.VIEW',
            '-d', test_url,
            package
        ]
        logger.info(f"📱 Browser command for {package}: {' '.join(cmd)}")
    
    # Test search commands
    search_commands = [
        ['adb', '-s', test_device_id, 'shell', 'input', 'tap', '540', '350'],
        ['adb', '-s', test_device_id, 'shell', 'input', 'text', 'test query'],
        ['adb', '-s', test_device_id, 'shell', 'input', 'keyevent', 'KEYCODE_ENTER']
    ]
    
    logger.info("🔍 Search automation commands:")
    for cmd in search_commands:
        logger.info(f"📱 {' '.join(cmd)}")
    
    logger.info("✅ Browser commands verified")
    return True


def main():
    """Run all verification checks"""
    logger.info("=" * 60)
    logger.info("🔍 HTC ONE SCENARIO VERIFICATION")
    logger.info("=" * 60)
    
    checks = [
        ("Genymotion Installation", verify_genymotion_installation),
        ("ADB Installation", verify_adb_installation),
        ("Device Templates", verify_device_templates),
        ("Android Images", verify_android_images),
        ("System Resources", verify_system_resources),
        ("Appium Capabilities", verify_appium_capabilities),
        ("Browser Commands", verify_browser_commands)
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        logger.info(f"\n🧪 {check_name}:")
        try:
            if check_func():
                logger.info(f"✅ {check_name}: PASSED")
                passed += 1
            else:
                logger.error(f"❌ {check_name}: FAILED")
        except Exception as e:
            logger.error(f"❌ {check_name}: ERROR - {e}")
    
    logger.info("\n" + "=" * 60)
    logger.info(f"📊 VERIFICATION RESULTS: {passed}/{total} checks passed")
    
    if passed == total:
        logger.info("🎉 All verifications passed!")
        logger.info("✅ System is ready for HTC One scenario")
        logger.info("\n💡 To run the complete test:")
        logger.info("   python3 test_complete_htc_scenario.py")
        logger.info("\n💡 To run the scenario:")
        logger.info("   python3 run_genymotion_scenario.py")
    elif passed >= total - 2:
        logger.warning("⚠️ Most verifications passed - system should work")
        logger.info("💡 You can try running the scenario with caution")
    else:
        logger.error("❌ Multiple verifications failed")
        logger.error("💡 Please fix the issues before running the scenario")
    
    logger.info("=" * 60)


if __name__ == "__main__":
    main()
